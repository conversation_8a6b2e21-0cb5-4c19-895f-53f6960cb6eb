var reactive,effect,release,raw,__create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__commonJS=(e,t)=>function(){return t||(0,e[__getOwnPropNames(e)[0]])((t={exports:{}}).exports,t),t.exports},__export=(e,t)=>{for(var n in t)__defProp(e,n,{get:t[n],enumerable:!0})},__copyProps=(t,n,r,i)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let e of __getOwnPropNames(n))__hasOwnProp.call(t,e)||e===r||__defProp(t,e,{get:()=>n[e],enumerable:!(i=__getOwnPropDesc(n,e))||i.enumerable});return t},__toESM=(e,t,n)=>(n=null!=e?__create(__getProtoOf(e)):{},__copyProps(!t&&e&&e.__esModule?n:__defProp(n,"default",{value:e,enumerable:!0}),e)),__toCommonJS=e=>__copyProps(__defProp({},"__esModule",{value:!0}),e),require_shared_cjs=__commonJS({"node_modules/@vue/shared/dist/shared.cjs.js"(e){"use strict";function t(e,t){const n=Object.create(null);var r=e.split(",");for(let e=0;e<r.length;e++)n[r[e]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}Object.defineProperty(e,"__esModule",{value:!0});var n={[1]:"TEXT",2:"CLASS",4:"STYLE",8:"PROPS",16:"FULL_PROPS",32:"HYDRATE_EVENTS",64:"STABLE_FRAGMENT",128:"KEYED_FRAGMENT",256:"UNKEYED_FRAGMENT",512:"NEED_PATCH",1024:"DYNAMIC_SLOTS",2048:"DEV_ROOT_FRAGMENT","-1":"HOISTED","-2":"BAIL"},r={[1]:"STABLE",2:"DYNAMIC",3:"FORWARDED"},i=t("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt");var o="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",a=t(o),o=t(o+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected"),s=/[>/="'\u0009\u000a\u000c\u0020]/,l={};var c=t("animation-iteration-count,border-image-outset,border-image-slice,border-image-width,box-flex,box-flex-group,box-ordinal-group,column-count,columns,flex,flex-grow,flex-positive,flex-shrink,flex-negative,flex-order,grid-row,grid-row-end,grid-row-span,grid-row-start,grid-column,grid-column-end,grid-column-span,grid-column-start,font-weight,line-clamp,line-height,opacity,order,orphans,tab-size,widows,z-index,zoom,fill-opacity,flood-opacity,stop-opacity,stroke-dasharray,stroke-dashoffset,stroke-miterlimit,stroke-opacity,stroke-width"),u=t("accept,accept-charset,accesskey,action,align,allow,alt,async,autocapitalize,autocomplete,autofocus,autoplay,background,bgcolor,border,buffered,capture,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,contenteditable,contextmenu,controls,coords,crossorigin,csp,data,datetime,decoding,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,enterkeyhint,for,form,formaction,formenctype,formmethod,formnovalidate,formtarget,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,importance,integrity,ismap,itemprop,keytype,kind,label,lang,language,loading,list,loop,low,manifest,max,maxlength,minlength,media,min,multiple,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,referrerpolicy,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,selected,shape,size,sizes,slot,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,translate,type,usemap,value,width,wrap");var d=/;(?![^(]*\))/g,f=/:(.+)/;function p(e){const n={};return e.split(d).forEach(e=>{if(e){const t=e.split(f);1<t.length&&(n[t[0].trim()]=t[1].trim())}}),n}var _=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),g=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),m=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),h=/["'&<>]/;var v=/^-?>|<!--|-->|--!>|<!-$/g;function b(e,t){if(e===t)return!0;let n=E(e),r=E(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=S(e),r=S(t),n||r)return!(!n||!r)&&function(t,n){if(t.length!==n.length)return!1;let r=!0;for(let e=0;r&&e<t.length;e++)r=b(t[e],n[e]);return r}(e,t);if(n=T(e),r=T(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const a in e){var i=e.hasOwnProperty(a),o=t.hasOwnProperty(a);if(i&&!o||!i&&o||!b(e[a],t[a]))return!1}}return String(e)===String(t)}var y,x=(e,t)=>k(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n])=>(e[t+" =>"]=n,e),{})}:A(t)?{[`Set(${t.size})`]:[...t.values()]}:!T(t)||S(t)||j(t)?t:String(t),w=Object.freeze({}),I=Object.freeze([]),F=/^on[^a-z]/,L=Object.assign,B=Object.prototype.hasOwnProperty,S=Array.isArray,k=e=>"[object Map]"===M(e),A=e=>"[object Set]"===M(e),E=e=>e instanceof Date,O=e=>"function"==typeof e,C=e=>"string"==typeof e,T=e=>null!==e&&"object"==typeof e,P=Object.prototype.toString,M=e=>P.call(e),j=e=>"[object Object]"===M(e),q=t(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),R=t=>{const n=Object.create(null);return e=>{return n[e]||(n[e]=t(e))}},H=/-(\w)/g,$=R(e=>e.replace(H,(e,t)=>t?t.toUpperCase():"")),z=/\B([A-Z])/g,D=R(e=>e.replace(z,"-$1").toLowerCase()),N=R(e=>e.charAt(0).toUpperCase()+e.slice(1)),R=R(e=>e?"on"+N(e):"");e.EMPTY_ARR=I,e.EMPTY_OBJ=w,e.NO=()=>!1,e.NOOP=()=>{},e.PatchFlagNames=n,e.babelParserDefaultPlugins=["bigInt","optionalChaining","nullishCoalescingOperator"],e.camelize=$,e.capitalize=N,e.def=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},e.escapeHtml=function(e){const t=""+e;if(!(e=h.exec(t)))return t;let n="",r,i,o=0;for(i=e.index;i<t.length;i++){switch(t.charCodeAt(i)){case 34:r="&quot;";break;case 38:r="&amp;";break;case 39:r="&#39;";break;case 60:r="&lt;";break;case 62:r="&gt;";break;default:continue}o!==i&&(n+=t.substring(o,i)),o=i+1,n+=r}return o!==i?n+t.substring(o,i):n},e.escapeHtmlComment=function(e){return e.replace(v,"")},e.extend=L,e.generateCodeFrame=function(e,n=0,r=e.length){let i=e.split(/(\r?\n)/);var o,a,s,l,c=i.filter((e,t)=>t%2==1);i=i.filter((e,t)=>t%2==0);let u=0;const d=[];for(let t=0;t<i.length;t++)if((u+=i[t].length+(c[t]&&c[t].length||0))>=n){for(let e=t-2;e<=t+2||r>u;e++)e<0||e>=i.length||(o=e+1,d.push(""+o+" ".repeat(Math.max(3-String(o).length,0))+"|  "+i[e]),o=i[e].length,a=c[e]&&c[e].length||0,e===t?(l=n-(u-(o+a)),s=Math.max(1,r>u?o-l:r-n),d.push("   |  "+" ".repeat(l)+"^".repeat(s))):e>t&&(r>u&&(l=Math.max(Math.min(r-u,o),1),d.push("   |  "+"^".repeat(l))),u+=o+a));break}return d.join("\n")},e.getGlobalThis=()=>y=y||("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),e.hasChanged=(e,t)=>e!==t&&(e==e||t==t),e.hasOwn=(e,t)=>B.call(e,t),e.hyphenate=D,e.invokeArrayFns=(t,n)=>{for(let e=0;e<t.length;e++)t[e](n)},e.isArray=S,e.isBooleanAttr=o,e.isDate=E,e.isFunction=O,e.isGloballyWhitelisted=i,e.isHTMLTag=_,e.isIntegerKey=e=>C(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,e.isKnownAttr=u,e.isMap=k,e.isModelListener=e=>e.startsWith("onUpdate:"),e.isNoUnitNumericStyleProp=c,e.isObject=T,e.isOn=e=>F.test(e),e.isPlainObject=j,e.isPromise=e=>T(e)&&O(e.then)&&O(e.catch),e.isReservedProp=q,e.isSSRSafeAttrName=function(e){if(l.hasOwnProperty(e))return l[e];var t=s.test(e);return t&&console.error("unsafe attribute name: "+e),l[e]=!t},e.isSVGTag=g,e.isSet=A,e.isSpecialBooleanAttr=a,e.isString=C,e.isSymbol=e=>"symbol"==typeof e,e.isVoidTag=m,e.looseEqual=b,e.looseIndexOf=function(e,t){return e.findIndex(e=>b(e,t))},e.makeMap=t,e.normalizeClass=function t(n){let r="";if(C(n))r=n;else if(S(n))for(let e=0;e<n.length;e++){var i=t(n[e]);i&&(r+=i+" ")}else if(T(n))for(const e in n)n[e]&&(r+=e+" ");return r.trim()},e.normalizeStyle=function t(n){if(S(n)){const o={};for(let e=0;e<n.length;e++){var r=n[e],i=t(C(r)?p(r):r);if(i)for(const a in i)o[a]=i[a]}return o}if(T(n))return n},e.objectToString=P,e.parseStringStyle=p,e.propsToAttrMap={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},e.remove=(e,t)=>{t=e.indexOf(t);-1<t&&e.splice(t,1)},e.slotFlagsText=r,e.stringifyStyle=function(e){let t="";if(!e)return t;for(const i in e){var n=e[i],r=i.startsWith("--")?i:D(i);(C(n)||"number"==typeof n&&c(r))&&(t+=r+`:${n};`)}return t},e.toDisplayString=e=>null==e?"":T(e)?JSON.stringify(e,x,2):String(e),e.toHandlerKey=R,e.toNumber=e=>{var t=parseFloat(e);return isNaN(t)?e:t},e.toRawType=e=>M(e).slice(8,-1),e.toTypeString=M}}),require_shared=__commonJS({"node_modules/@vue/shared/index.js"(e,t){"use strict";t.exports=require_shared_cjs()}}),require_reactivity_cjs=__commonJS({"node_modules/@vue/reactivity/dist/reactivity.cjs.js"(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var c,u=require_shared(),d=new WeakMap,a=[],f=Symbol("iterate"),p=Symbol("Map key iterate");function r(e,t=u.EMPTY_OBJ){var n,r;(r=e)&&!0===r._isEffect&&(e=e.raw);n=e,r=t,o.id=I++,o.allowRecurse=!!r.allowRecurse,o._isEffect=!0,o.active=!0,o.raw=n,o.deps=[],o.options=r;const i=o;function o(){if(!o.active)return n();if(!a.includes(o)){s(o);try{return l(),a.push(o),c=o,n()}finally{a.pop(),_(),c=a[a.length-1]}}}return t.lazy||i(),i}var I=0;function s(t){const n=t["deps"];if(n.length){for(let e=0;e<n.length;e++)n[e].delete(t);n.length=0}}var t=!0,n=[];function i(){n.push(t),t=!1}function l(){n.push(t),t=!0}function _(){var e=n.pop();t=void 0===e||e}function g(n,r,i){if(t&&void 0!==c){let e=d.get(n),t=(e||d.set(n,e=new Map),e.get(i));t||e.set(i,t=new Set),t.has(c)||(t.add(c),c.deps.push(t),c.options.onTrack&&c.options.onTrack({effect:c,target:n,type:r,key:i}))}}function m(t,n,r,i,o,a){const e=d.get(t);if(e){const s=new Set,l=e=>{e&&e.forEach(e=>{e===c&&!e.allowRecurse||s.add(e)})};if("clear"===n)e.forEach(l);else if("length"===r&&u.isArray(t))e.forEach((e,t)=>{("length"===t||i<=t)&&l(e)});else switch(void 0!==r&&l(e.get(r)),n){case"add":u.isArray(t)?u.isIntegerKey(r)&&l(e.get("length")):(l(e.get(f)),u.isMap(t)&&l(e.get(p)));break;case"delete":u.isArray(t)||(l(e.get(f)),u.isMap(t)&&l(e.get(p)));break;case"set":u.isMap(t)&&l(e.get(f))}s.forEach(e=>{e.options.onTrigger&&e.options.onTrigger({effect:e,target:t,key:r,type:n,newValue:i,oldValue:o,oldTarget:a}),e.options.scheduler?e.options.scheduler(e):e()})}}var F=u.makeMap("__proto__,__v_isRef,__isVue"),h=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(u.isSymbol)),L=o(),B=o(!1,!0),q=o(!0),H=o(!0,!0),$=z();function z(){const e={};return["includes","indexOf","lastIndexOf"].forEach(r=>{e[r]=function(...e){const n=D(this);for(let e=0,t=this.length;e<t;e++)g(n,"get",e+"");var t=n[r](...e);return-1===t||!1===t?n[r](...e.map(D)):t}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){i();e=D(this)[t].apply(this,e);return _(),e}}),e}function o(i=!1,o=!1){return function(e,t,n){if("__v_isReactive"===t)return!i;if("__v_isReadonly"===t)return i;if("__v_raw"===t&&n===(i?o?fe:de:o?ue:ce).get(e))return e;var r=u.isArray(e);if(!i&&r&&u.hasOwn($,t))return Reflect.get($,t,n);n=Reflect.get(e,t,n);return(u.isSymbol(t)?h.has(t):F(t))?n:(i||g(e,"get",t),o?n:N(n)?!r||!u.isIntegerKey(t)?n.value:n:u.isObject(n)?(i?P:T)(n):n)}}function V(s=!1){return function(e,t,n,r){let i=e[t];if(!s&&(n=D(n),i=D(i),!u.isArray(e)&&N(i)&&!N(n)))return i.value=n,!0;var o=u.isArray(e)&&u.isIntegerKey(t)?Number(t)<e.length:u.hasOwn(e,t),a=Reflect.set(e,t,n,r);return e===D(r)&&(o?u.hasChanged(n,i)&&m(e,"set",t,n,i):m(e,"add",t,n)),a}}var U={get:L,set:V(),deleteProperty:function(e,t){var n=u.hasOwn(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&m(e,"delete",t,void 0,r),i},has:function(e,t){var n=Reflect.has(e,t);return u.isSymbol(t)&&h.has(t)||g(e,"has",t),n},ownKeys:function(e){return g(e,"iterate",u.isArray(e)?"length":f),Reflect.ownKeys(e)}},K={get:q,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},J=u.extend({},U,{get:B,set:V(!0)}),W=u.extend({},K,{get:H}),v=e=>u.isObject(e)?T(e):e,b=e=>u.isObject(e)?P(e):e,y=e=>e,x=e=>Reflect.getPrototypeOf(e);function w(e,t,n=!1,r=!1){var i=D(e=e.__v_raw),o=D(t);t===o||n||g(i,"get",t),n||g(i,"get",o);const a=x(i)["has"],s=r?y:n?b:v;return a.call(i,t)?s(e.get(t)):a.call(i,o)?s(e.get(o)):void(e!==i&&e.get(t))}function S(e,t=!1){const n=this.__v_raw;var r=D(n),i=D(e);return e===i||t||g(r,"has",e),t||g(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function k(e,t=!1){return e=e.__v_raw,t||g(D(e),"iterate",f),Reflect.get(e,"size",e)}function G(e){e=D(e);const t=D(this),n=x(t);return n.has.call(t,e)||(t.add(e),m(t,"add",e,e)),this}function Y(e,t){t=D(t);const n=D(this),{has:r,get:i}=x(n);let o=r.call(n,e);o?le(n,r,e):(e=D(e),o=r.call(n,e));var a=i.call(n,e);return n.set(e,t),o?u.hasChanged(t,a)&&m(n,"set",e,t,a):m(n,"add",e,t),this}function X(e){const t=D(this),{has:n,get:r}=x(t);let i=n.call(t,e);i?le(t,n,e):(e=D(e),i=n.call(t,e));var o=r?r.call(t,e):void 0,a=t.delete(e);return i&&m(t,"delete",e,void 0,o),a}function Z(){const e=D(this);var t=0!==e.size,n=new(u.isMap(e)?Map:Set)(e),r=e.clear();return t&&m(e,"clear",void 0,void 0,n),r}function A(a,s){return function(n,r){const i=this,e=i.__v_raw;var t=D(e);const o=s?y:a?b:v;return a||g(t,"iterate",f),e.forEach((e,t)=>n.call(r,o(e),o(t),i))}}function E(s,l,c){return function(...e){const t=this.__v_raw;var n=D(t),r=u.isMap(n);const i="entries"===s||s===Symbol.iterator&&r;r="keys"===s&&r;const o=t[s](...e),a=c?y:l?b:v;return l||g(n,"iterate",r?p:f),{next(){var{value:e,done:t}=o.next();return t?{value:e,done:t}:{value:i?[a(e[0]),a(e[1])]:a(e),done:t}},[Symbol.iterator](){return this}}}}function O(t){return function(...e){e=e[0]?`on key "${e[0]}" `:"";return console.warn(u.capitalize(t)+` operation ${e}failed: target is readonly.`,D(this)),"delete"!==t&&this}}function Q(){const t={get(e){return w(this,e)},get size(){return k(this)},has:S,add:G,set:Y,delete:X,clear:Z,forEach:A(!1,!1)},n={get(e){return w(this,e,!1,!0)},get size(){return k(this)},has:S,add:G,set:Y,delete:X,clear:Z,forEach:A(!1,!0)},r={get(e){return w(this,e,!0)},get size(){return k(this,!0)},has(e){return S.call(this,e,!0)},add:O("add"),set:O("set"),delete:O("delete"),clear:O("clear"),forEach:A(!0,!1)},i={get(e){return w(this,e,!0,!0)},get size(){return k(this,!0)},has(e){return S.call(this,e,!0)},add:O("add"),set:O("set"),delete:O("delete"),clear:O("clear"),forEach:A(!0,!0)},e=["keys","values","entries",Symbol.iterator];return e.forEach(e=>{t[e]=E(e,!1,!1),r[e]=E(e,!0,!1),n[e]=E(e,!1,!0),i[e]=E(e,!0,!0)}),[t,r,n,i]}var[ee,te,ne,re]=Q();function C(r,e){const i=e?r?re:ne:r?te:ee;return(e,t,n)=>"__v_isReactive"===t?!r:"__v_isReadonly"===t?r:"__v_raw"===t?e:Reflect.get(u.hasOwn(i,t)&&t in e?i:e,t,n)}var ie={get:C(!1,!1)},oe={get:C(!1,!0)},ae={get:C(!0,!1)},se={get:C(!0,!0)};function le(e,t,n){var r=D(n);r!==n&&t.call(e,r)&&(n=u.toRawType(e),console.warn(`Reactive ${n} contains both the raw and reactive versions of the same object${"Map"===n?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`))}var ce=new WeakMap,ue=new WeakMap,de=new WeakMap,fe=new WeakMap;function T(e){return e&&e.__v_isReadonly?e:M(e,!1,U,ie,ce)}function P(e){return M(e,!0,K,ae,de)}function M(e,t,n,r,i){if(!u.isObject(e))return console.warn("value cannot be made reactive: "+String(e)),e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;t=i.get(e);if(t)return t;t=function(e){if(e.__v_skip||!Object.isExtensible(e))return 0;switch(u.toRawType(e)){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(e);if(0===t)return e;t=new Proxy(e,2===t?r:n);return i.set(e,t),t}function j(e){return R(e)?j(e.__v_raw):!(!e||!e.__v_isReactive)}function R(e){return!(!e||!e.__v_isReadonly)}function pe(e){return j(e)||R(e)}function D(e){return e&&D(e.__v_raw)||e}var _e=e=>u.isObject(e)?T(e):e;function N(e){return Boolean(e&&!0===e.__v_isRef)}var ge=class{constructor(e,t=!1){this._shallow=t,this.__v_isRef=!0,this._rawValue=t?e:D(e),this._value=t?e:_e(e)}get value(){return g(D(this),"get","value"),this._value}set value(e){e=this._shallow?e:D(e),u.hasChanged(e,this._rawValue)&&(this._rawValue=e,this._value=this._shallow?e:_e(e),m(D(this),"set","value",e))}};function me(e,t=!1){return N(e)?e:new ge(e,t)}function he(e){return N(e)?e.value:e}var ve={get:(e,t,n)=>he(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const i=e[t];return N(i)&&!N(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};var be=class{constructor(e){this.__v_isRef=!0;var{get:e,set:t}=e(()=>g(this,"get","value"),()=>m(this,"set","value"));this._get=e,this._set=t}get value(){return this._get()}set value(e){this._set(e)}};var ye=class{constructor(e,t){this._object=e,this._key=t,this.__v_isRef=!0}get value(){return this._object[this._key]}set value(e){this._object[this._key]=e}};function xe(e,t){return N(e[t])?e[t]:new ye(e,t)}var we=class{constructor(e,t,n){this._setter=t,this._dirty=!0,this.__v_isRef=!0,this.effect=r(e,{lazy:!0,scheduler:()=>{this._dirty||(this._dirty=!0,m(D(this),"set","value"))}}),this.__v_isReadonly=n}get value(){const e=D(this);return e._dirty&&(e._value=this.effect(),e._dirty=!1),g(e,"get","value"),e._value}set value(e){this._setter(e)}};e.ITERATE_KEY=f,e.computed=function(e){let t,n;return n=u.isFunction(e)?(t=e,()=>{console.warn("Write operation failed: computed value is readonly")}):(t=e.get,e.set),new we(t,n,u.isFunction(e)||!e.set)},e.customRef=function(e){return new be(e)},e.effect=r,e.enableTracking=l,e.isProxy=pe,e.isReactive=j,e.isReadonly=R,e.isRef=N,e.markRaw=function(e){return u.def(e,"__v_skip",!0),e},e.pauseTracking=i,e.proxyRefs=function(e){return j(e)?e:new Proxy(e,ve)},e.reactive=T,e.readonly=P,e.ref=function(e){return me(e)},e.resetTracking=_,e.shallowReactive=function(e){return M(e,!1,J,oe,ue)},e.shallowReadonly=function(e){return M(e,!0,W,se,fe)},e.shallowRef=function(e){return me(e,!0)},e.stop=function(e){e.active&&(s(e),e.options.onStop&&e.options.onStop(),e.active=!1)},e.toRaw=D,e.toRef=xe,e.toRefs=function(e){pe(e)||console.warn("toRefs() expects a reactive object but received a plain one.");const t=u.isArray(e)?new Array(e.length):{};for(const n in e)t[n]=xe(e,n);return t},e.track=g,e.trigger=m,e.triggerRef=function(e){m(D(e),"set","value",e.value)},e.unref=he}}),require_reactivity=__commonJS({"node_modules/@vue/reactivity/index.js"(e,t){"use strict";t.exports=require_reactivity_cjs()}}),module_exports={},flushPending=(__export(module_exports,{Alpine:()=>src_default,default:()=>module_default}),module.exports=__toCommonJS(module_exports),!1),flushing=!1,queue=[],lastFlushedIndex=-1;function scheduler(e){queueJob(e)}function queueJob(e){queue.includes(e)||queue.push(e),queueFlush()}function dequeueJob(e){e=queue.indexOf(e);-1!==e&&lastFlushedIndex<e&&queue.splice(e,1)}function queueFlush(){flushing||flushPending||(flushPending=!0,queueMicrotask(flushJobs))}function flushJobs(){flushing=!(flushPending=!1);for(let e=0;e<queue.length;e++)queue[e](),lastFlushedIndex=e;queue.length=0,flushing=!(lastFlushedIndex=-1)}var shouldSchedule=!0;function disableEffectScheduling(e){shouldSchedule=!1,e(),shouldSchedule=!0}function setReactivityEngine(t){reactive=t.reactive,release=t.release,effect=e=>t.effect(e,{scheduler:e=>{shouldSchedule?scheduler(e):e()}}),raw=t.raw}function overrideEffect(e){effect=e}function elementBoundEffect(n){let r=()=>{};return[e=>{let t=effect(e);return n._x_effects||(n._x_effects=new Set,n._x_runEffects=()=>{n._x_effects.forEach(e=>e())}),n._x_effects.add(t),r=()=>{void 0!==t&&(n._x_effects.delete(t),release(t))},t},()=>{r()}]}function watch(t,n){let r=!0,i,e=effect(()=>{let e=t();JSON.stringify(e),r?i=e:queueMicrotask(()=>{n(e,i),i=e}),r=!1});return()=>release(e)}var onAttributeAddeds=[],onElRemoveds=[],onElAddeds=[];function onElAdded(e){onElAddeds.push(e)}function onElRemoved(e,t){"function"==typeof t?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):onElRemoveds.push(t=e)}function onAttributesAdded(e){onAttributeAddeds.push(e)}function onAttributeRemoved(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function cleanupAttributes(n,r){n._x_attributeCleanups&&Object.entries(n._x_attributeCleanups).forEach(([e,t])=>{void 0!==r&&!r.includes(e)||(t.forEach(e=>e()),delete n._x_attributeCleanups[e])})}function cleanupElement(e){var t,n;for(null!=(t=e._x_effects)&&t.forEach(dequeueJob);null!=(n=e._x_cleanups)&&n.length;)e._x_cleanups.pop()()}var observer=new MutationObserver(onMutate),currentlyObserving=!1;function startObservingMutations(){observer.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),currentlyObserving=!0}function stopObservingMutations(){flushObserver(),observer.disconnect(),currentlyObserving=!1}var queuedMutations=[];function flushObserver(){let e=observer.takeRecords(),t=(queuedMutations.push(()=>0<e.length&&onMutate(e)),queuedMutations.length);queueMicrotask(()=>{if(queuedMutations.length===t)for(;0<queuedMutations.length;)queuedMutations.shift()()})}function mutateDom(e){if(!currentlyObserving)return e();stopObservingMutations();e=e();return startObservingMutations(),e}var isCollecting=!1,deferredMutations=[];function deferMutations(){isCollecting=!0}function flushAndStopDeferringMutations(){isCollecting=!1,onMutate(deferredMutations),deferredMutations=[]}function onMutate(s){if(isCollecting)deferredMutations=deferredMutations.concat(s);else{let r=[],i=new Set,o=new Map,a=new Map;for(let n=0;n<s.length;n++)if(!s[n].target._x_ignoreMutationObserver&&("childList"===s[n].type&&(s[n].removedNodes.forEach(e=>{1===e.nodeType&&e._x_marker&&i.add(e)}),s[n].addedNodes.forEach(e=>{1===e.nodeType&&(i.has(e)?i.delete(e):e._x_marker||r.push(e))})),"attributes"===s[n].type)){let e=s[n].target,t=s[n].attributeName;var l=s[n].oldValue,c=()=>{o.has(e)||o.set(e,[]),o.get(e).push({name:t,value:e.getAttribute(t)})},u=()=>{a.has(e)||a.set(e,[]),a.get(e).push(t)};e.hasAttribute(t)&&null===l?c():e.hasAttribute(t)?(u(),c()):u()}a.forEach((e,t)=>{cleanupAttributes(t,e)}),o.forEach((t,n)=>{onAttributeAddeds.forEach(e=>e(n,t))});for(let t of i)r.some(e=>e.contains(t))||onElRemoveds.forEach(e=>e(t));for(let t of r)t.isConnected&&onElAddeds.forEach(e=>e(t));r=null,i=null,o=null,a=null}}function scope(e){return mergeProxies(closestDataStack(e))}function addScopeToNode(e,t,n){return e._x_dataStack=[t,...closestDataStack(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(e=>e!==t)}}function closestDataStack(e){return e._x_dataStack||("function"==typeof ShadowRoot&&e instanceof ShadowRoot?closestDataStack(e.host):e.parentNode?closestDataStack(e.parentNode):[])}function mergeProxies(e){return new Proxy({objects:e},mergeProxyTrap)}var mergeProxyTrap={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(e=>Object.keys(e))))},has({objects:e},t){return t!=Symbol.unscopables&&e.some(e=>Object.prototype.hasOwnProperty.call(e,t)||Reflect.has(e,t))},get({objects:e},t,n){return"toJSON"==t?collapseProxies:Reflect.get(e.find(e=>Reflect.has(e,t))||{},t,n)},set({objects:e},t,n,r){e=e.find(e=>Object.prototype.hasOwnProperty.call(e,t))||e[e.length-1];const i=Object.getOwnPropertyDescriptor(e,t);return null!=i&&i.set&&null!=i&&i.get?i.set.call(r,n)||!0:Reflect.set(e,t,n)}};function collapseProxies(){let e=Reflect.ownKeys(this);return e.reduce((e,t)=>(e[t]=Reflect.get(this,t),e),{})}function initInterceptors(o){let a=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([e,{value:t,enumerable:n}])=>{!1===n||void 0===t||"object"==typeof t&&null!==t&&t.__v_skip||(n=""===i?e:i+"."+e,"object"==typeof t&&null!==t&&t._x_interceptor?r[e]=t.initialize(o,n,e):"object"!=typeof(e=t)||Array.isArray(e)||null===e||t===r||t instanceof Element||a(t,n))})};return a(o)}function interceptor(r,e=()=>{}){let a={initialValue:void 0,_x_interceptor:!0,initialize(t,n,e){return r(this.initialValue,()=>get(t,n),e=>set(t,n,e),n,e)}};return e(a),o=>{if("object"==typeof o&&null!==o&&o._x_interceptor){let i=a.initialize.bind(a);a.initialize=(e,t,n)=>{var r=o.initialize(e,t,n);return a.initialValue=r,i(e,t,n)}}else a.initialValue=o;return a}}function get(e,t){return t.split(".").reduce((e,t)=>e[t],e)}function set(e,t,n){if(1!==(t="string"==typeof t?t.split("."):t).length){if(0===t.length)throw error;return e[t[0]]||(e[t[0]]={}),set(e[t[0]],t.slice(1),n)}e[t[0]]=n}var magics={};function magic(e,t){magics[e]=t}function injectMagics(n,r){let i=getUtilities(r);return Object.entries(magics).forEach(([e,t])=>{Object.defineProperty(n,"$"+e,{get(){return t(r,i)},enumerable:!1})}),n}function getUtilities(e){var[t,n]=getElementBoundUtilities(e),t={interceptor:interceptor,...t};return onElRemoved(e,n),t}function tryCatch(t,n,e,...r){try{return e(...r)}catch(e){handleError(e,t,n)}}function handleError(e,t,n=void 0){e=Object.assign(null!=e?e:{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

`+(n?'Expression: "'+n+'"\n\n':""),t),setTimeout(()=>{throw e},0)}var shouldAutoEvaluateFunctions=!0;function dontAutoEvaluateFunctions(e){var t=shouldAutoEvaluateFunctions,e=(shouldAutoEvaluateFunctions=!1,e());return shouldAutoEvaluateFunctions=t,e}function evaluate(e,t,n={}){let r;return evaluateLater(e,t)(e=>r=e,n),r}function evaluateLater(...e){return theEvaluatorFunction(...e)}var theEvaluatorFunction=normalEvaluator;function setEvaluator(e){theEvaluatorFunction=e}function normalEvaluator(e,t){var n={},n=(injectMagics(n,e),[n,...closestDataStack(e)]),n="function"==typeof t?generateEvaluatorFromFunction(n,t):generateEvaluatorFromString(n,t,e);return tryCatch.bind(null,e,t,n)}function generateEvaluatorFromFunction(r,i){return(e=()=>{},{scope:t={},params:n=[]}={})=>{runIfTypeOfFunction(e,i.apply(mergeProxies([t,...r]),n))}}var evaluatorMemo={};function generateFunctionFromString(t,n){if(evaluatorMemo[t])return evaluatorMemo[t];let r=Object.getPrototypeOf(async function(){}).constructor,i=/^[\n\s]*if.*\(.*\)/.test(t.trim())||/^(let|const)\s/.test(t.trim())?`(async()=>{ ${t} })()`:t;var e=(()=>{try{var e=new r(["__self","scope"],`with (scope) { __self.result = ${i} }; __self.finished = true; return __self.result;`);return Object.defineProperty(e,"name",{value:"[Alpine] "+t}),e}catch(e){return handleError(e,n,t),Promise.resolve()}})();return evaluatorMemo[t]=e}function generateEvaluatorFromString(i,o,a){let s=generateFunctionFromString(o,a);return(t=()=>{},{scope:e={},params:n=[]}={})=>{s.result=void 0,s.finished=!1;let r=mergeProxies([e,...i]);if("function"==typeof s){let e=s(s,r).catch(e=>handleError(e,a,o));s.finished?(runIfTypeOfFunction(t,s.result,r,n,a),s.result=void 0):e.then(e=>{runIfTypeOfFunction(t,e,r,n,a)}).catch(e=>handleError(e,a,o)).finally(()=>s.result=void 0)}}}function runIfTypeOfFunction(t,n,r,i,o){if(shouldAutoEvaluateFunctions&&"function"==typeof n){let e=n.apply(r,i);e instanceof Promise?e.then(e=>runIfTypeOfFunction(t,e,r,i)).catch(e=>handleError(e,o,n)):t(e)}else"object"==typeof n&&n instanceof Promise?n.then(e=>t(e)):t(n)}var prefixAsString="x-";function prefix(e=""){return prefixAsString+e}function setPrefix(e){prefixAsString=e}var directiveHandlers={};function directive(n,e){return directiveHandlers[n]=e,{before(e){var t;directiveHandlers[e]?(t=directiveOrder.indexOf(e),directiveOrder.splice(0<=t?t:directiveOrder.indexOf("DEFAULT"),0,n)):console.warn(String.raw`Cannot find directive \`${e}\`. \`${n}\` will use the default order of execution`)}}}function directiveExists(e){return Object.keys(directiveHandlers).includes(e)}function directives(t,r,e){if(r=Array.from(r),t._x_virtualDirectives){let e=Object.entries(t._x_virtualDirectives).map(([e,t])=>({name:e,value:t})),n=attributesOnly(e);e=e.map(t=>n.find(e=>e.name===t.name)?{name:"x-bind:"+t.name,value:`"${t.value}"`}:t),r=r.concat(e)}let n={},i=r.map(toTransformedAttributes((e,t)=>n[e]=t)).filter(outNonAlpineAttributes).map(toParsedDirectives(n,e)).sort(byPriority);return i.map(e=>getDirectiveHandler(t,e))}function attributesOnly(e){return Array.from(e).map(toTransformedAttributes()).filter(e=>!outNonAlpineAttributes(e))}var isDeferringHandlers=!1,directiveHandlerStacks=new Map,currentHandlerStackKey=Symbol();function deferHandlingDirectives(e){isDeferringHandlers=!0;let t=Symbol(),n=(currentHandlerStackKey=t,directiveHandlerStacks.set(t,[]),()=>{for(;directiveHandlerStacks.get(t).length;)directiveHandlerStacks.get(t).shift()();directiveHandlerStacks.delete(t)});e(n),isDeferringHandlers=!1,n()}function getElementBoundUtilities(e){let t=[];var[n,r]=elementBoundEffect(e),r=(t.push(r),{Alpine:alpine_default,effect:n,cleanup:e=>t.push(e),evaluateLater:evaluateLater.bind(evaluateLater,e),evaluate:evaluate.bind(evaluate,e)});return[r,()=>t.forEach(e=>e())]}function getDirectiveHandler(e,t){let n=directiveHandlers[t.type]||(()=>{}),[r,i]=getElementBoundUtilities(e);onAttributeRemoved(e,t.original,i);var o=()=>{e._x_ignore||e._x_ignoreSelf||(n.inline&&n.inline(e,t,r),n=n.bind(n,e,t,r),isDeferringHandlers?directiveHandlerStacks.get(currentHandlerStackKey).push(n):n())};return o.runCleanups=i,o}var startingWith=(n,r)=>({name:e,value:t})=>({name:e=e.startsWith(n)?e.replace(n,r):e,value:t}),into=e=>e;function toTransformedAttributes(r=()=>{}){return({name:e,value:t})=>{var{name:t,value:n}=attributeTransformers.reduce((e,t)=>t(e),{name:e,value:t});return t!==e&&r(t,e),{name:t,value:n}}}var attributeTransformers=[];function mapAttributes(e){attributeTransformers.push(e)}function outNonAlpineAttributes({name:e}){return alpineAttributeRegex().test(e)}var alpineAttributeRegex=()=>new RegExp(`^${prefixAsString}([^:^.]+)\\b`);function toParsedDirectives(o,a){return({name:e,value:t})=>{var n=e.match(alpineAttributeRegex()),r=e.match(/:([a-zA-Z0-9\-_:]+)/);let i=e.match(/\.[^.\]]+(?=[^\]]*$)/g)||[];e=a||o[e]||e;return{type:n?n[1]:null,value:r?r[1]:null,modifiers:i.map(e=>e.replace(".","")),expression:t,original:e}}}var DEFAULT="DEFAULT",directiveOrder=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",DEFAULT,"teleport"];function byPriority(e,t){e=-1===directiveOrder.indexOf(e.type)?DEFAULT:e.type,t=-1===directiveOrder.indexOf(t.type)?DEFAULT:t.type;return directiveOrder.indexOf(e)-directiveOrder.indexOf(t)}function dispatch(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function walk(t,n){if("function"==typeof ShadowRoot&&t instanceof ShadowRoot)Array.from(t.children).forEach(e=>walk(e,n));else{let e=!1;if(n(t,()=>e=!0),!e){let e=t.firstElementChild;for(;e;)walk(e,n,!1),e=e.nextElementSibling}}}function warn(e,...t){console.warn("Alpine Warning: "+e,...t)}var started=!1;function start(){started&&warn("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),started=!0,document.body||warn("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),dispatch(document,"alpine:init"),dispatch(document,"alpine:initializing"),startObservingMutations(),onElAdded(e=>initTree(e,walk)),onElRemoved(e=>destroyTree(e)),onAttributesAdded((e,t)=>{directives(e,t).forEach(e=>e())});Array.from(document.querySelectorAll(allSelectors().join(","))).filter(e=>!closestRoot(e.parentElement,!0)).forEach(e=>{initTree(e)}),dispatch(document,"alpine:initialized"),setTimeout(()=>{warnAboutMissingPlugins()})}var rootSelectorCallbacks=[],initSelectorCallbacks=[];function rootSelectors(){return rootSelectorCallbacks.map(e=>e())}function allSelectors(){return rootSelectorCallbacks.concat(initSelectorCallbacks).map(e=>e())}function addRootSelector(e){rootSelectorCallbacks.push(e)}function addInitSelector(e){initSelectorCallbacks.push(e)}function closestRoot(e,n=!1){return findClosest(e,t=>{const e=(n?allSelectors:rootSelectors)();if(e.some(e=>t.matches(e)))return!0})}function findClosest(e,t){if(e)return t(e)?e:(e=e._x_teleportBack?e._x_teleportBack:e).parentElement?findClosest(e.parentElement,t):void 0}function isRoot(t){return rootSelectors().some(e=>t.matches(e))}var initInterceptors2=[];function interceptInit(e){initInterceptors2.push(e)}var markerDispenser=1;function initTree(e,t=walk,r=()=>{}){findClosest(e,e=>e._x_ignore)||deferHandlingDirectives(()=>{t(e,(t,n)=>{t._x_marker||(r(t,n),initInterceptors2.forEach(e=>e(t,n)),directives(t,t.attributes).forEach(e=>e()),t._x_ignore||(t._x_marker=markerDispenser++),t._x_ignore&&n())})})}function destroyTree(e,t=walk){t(e,e=>{cleanupElement(e),cleanupAttributes(e),delete e._x_marker})}function warnAboutMissingPlugins(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,e,n])=>{directiveExists(e)||n.some(e=>{if(document.querySelector(e))return warn(`found "${e}", but missing ${t} plugin`),!0})})}var tickStack=[],isHolding=!1;function nextTick(t=()=>{}){return queueMicrotask(()=>{isHolding||setTimeout(()=>{releaseNextTicks()})}),new Promise(e=>{tickStack.push(()=>{t(),e()})})}function releaseNextTicks(){for(isHolding=!1;tickStack.length;)tickStack.shift()()}function holdNextTicks(){isHolding=!0}function setClasses(e,t){return Array.isArray(t)?setClassesFromString(e,t.join(" ")):"object"==typeof t&&null!==t?setClassesFromObject(e,t):"function"==typeof t?setClasses(e,t()):setClassesFromString(e,t)}function setClassesFromString(t,e){var n;return n=(e=!0!==e&&e||"").split(" ").filter(e=>!t.classList.contains(e)).filter(Boolean),t.classList.add(...n),()=>{t.classList.remove(...n)}}function setClassesFromObject(t,e){let n=e=>e.split(" ").filter(Boolean),r=Object.entries(e).flatMap(([e,t])=>!!t&&n(e)).filter(Boolean),i=Object.entries(e).flatMap(([e,t])=>!t&&n(e)).filter(Boolean),o=[],a=[];return i.forEach(e=>{t.classList.contains(e)&&(t.classList.remove(e),a.push(e))}),r.forEach(e=>{t.classList.contains(e)||(t.classList.add(e),o.push(e))}),()=>{a.forEach(e=>t.classList.add(e)),o.forEach(e=>t.classList.remove(e))}}function setStyles(e,t){return("object"==typeof t&&null!==t?setStylesFromObject:setStylesFromString)(e,t)}function setStylesFromObject(n,e){let r={};return Object.entries(e).forEach(([e,t])=>{r[e]=n.style[e],e.startsWith("--")||(e=kebabCase(e)),n.style.setProperty(e,t)}),setTimeout(()=>{0===n.style.length&&n.removeAttribute("style")}),()=>{setStyles(n,r)}}function setStylesFromString(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function kebabCase(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function once(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}function registerTransitionsFromClassString(t,e,n){registerTransitionObject(t,setClasses,"");let r={enter:e=>{t._x_transition.enter.during=e},"enter-start":e=>{t._x_transition.enter.start=e},"enter-end":e=>{t._x_transition.enter.end=e},leave:e=>{t._x_transition.leave.during=e},"leave-start":e=>{t._x_transition.leave.start=e},"leave-end":e=>{t._x_transition.leave.end=e}};r[n](e)}function registerTransitionsFromHelper(e,n,t){registerTransitionObject(e,setStyles);var r=!n.includes("in")&&!n.includes("out")&&!t,i=r||n.includes("in")||["enter"].includes(t),t=r||n.includes("out")||["leave"].includes(t),r=!(n=(n=n.includes("in")&&!r?n.filter((e,t)=>t<n.indexOf("out")):n).includes("out")&&!r?n.filter((e,t)=>t>n.indexOf("out")):n).includes("opacity")&&!n.includes("scale"),o=r||n.includes("opacity")?0:1,r=r||n.includes("scale")?modifierValue(n,"scale",95)/100:1,a=modifierValue(n,"delay",0)/1e3,s=modifierValue(n,"origin","center"),l="opacity, transform",c=modifierValue(n,"duration",150)/1e3,u=modifierValue(n,"duration",75)/1e3,d="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:s,transitionDelay:a+"s",transitionProperty:l,transitionDuration:c+"s",transitionTimingFunction:d},e._x_transition.enter.start={opacity:o,transform:`scale(${r})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),t&&(e._x_transition.leave.during={transformOrigin:s,transitionDelay:a+"s",transitionProperty:l,transitionDuration:u+"s",transitionTimingFunction:d},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:o,transform:`scale(${r})`})}function registerTransitionObject(n,r,e={}){n._x_transition||(n._x_transition={enter:{during:e,start:e,end:e},leave:{during:e,start:e,end:e},in(e=()=>{},t=()=>{}){transition(n,r,{during:this.enter.during,start:this.enter.start,end:this.enter.end},e,t)},out(e=()=>{},t=()=>{}){transition(n,r,{during:this.leave.during,start:this.leave.start,end:this.leave.end},e,t)}})}function closestHide(e){e=e.parentNode;if(e)return e._x_hidePromise?e:closestHide(e)}function transition(e,t,{during:n,start:r,end:i}={},o=()=>{},a=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),0===Object.keys(n).length&&0===Object.keys(r).length&&0===Object.keys(i).length)return o(),void a();let s,l,c;performTransition(e,{start(){s=t(e,r)},during(){l=t(e,n)},before:o,end(){s(),c=t(e,i)},after:a,cleanup(){l(),c()}})}function performTransition(n,r){let i,o,a,e=once(()=>{mutateDom(()=>{i=!0,o||r.before(),a||(r.end(),releaseNextTicks()),r.after(),n.isConnected&&r.cleanup(),delete n._x_transitioning})});n._x_transitioning={beforeCancels:[],beforeCancel(e){this.beforeCancels.push(e)},cancel:once(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();e()}),finish:e},mutateDom(()=>{r.start(),r.during()}),holdNextTicks(),requestAnimationFrame(()=>{if(!i){let e=1e3*Number(getComputedStyle(n).transitionDuration.replace(/,.*/,"").replace("s","")),t=1e3*Number(getComputedStyle(n).transitionDelay.replace(/,.*/,"").replace("s",""));0===e&&(e=1e3*Number(getComputedStyle(n).animationDuration.replace("s",""))),mutateDom(()=>{r.before()}),o=!0,requestAnimationFrame(()=>{i||(mutateDom(()=>{r.end()}),releaseNextTicks(),setTimeout(n._x_transitioning.finish,e+t),a=!0)})}})}function modifierValue(e,t,n){if(-1===e.indexOf(t))return n;const r=e[e.indexOf(t)+1];if(!r)return n;if("scale"===t&&isNaN(r))return n;if("duration"===t||"delay"===t){n=r.match(/([0-9]+)ms/);if(n)return n[1]}return"origin"===t&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}directive("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{!1!==(r="function"==typeof r?i(r):r)&&(r&&"boolean"!=typeof r?registerTransitionsFromClassString(e,r,t):registerTransitionsFromHelper(e,n,t))});var isCloning=!(window.Element.prototype._x_toggleAndCascadeWithTransitions=function(r,e,t,n){const i="visible"===document.visibilityState?requestAnimationFrame:setTimeout;var o=()=>i(t);e?r._x_transition&&(r._x_transition.enter||r._x_transition.leave)?r._x_transition.enter&&(Object.entries(r._x_transition.enter.during).length||Object.entries(r._x_transition.enter.start).length||Object.entries(r._x_transition.enter.end).length)?r._x_transition.in(t):o():r._x_transition?r._x_transition.in(t):o():(r._x_hidePromise=r._x_transition?new Promise((e,t)=>{r._x_transition.out(()=>{},()=>e(n)),r._x_transitioning&&r._x_transitioning.beforeCancel(()=>t({isFromCancelledTransition:!0}))}):Promise.resolve(n),queueMicrotask(()=>{let e=closestHide(r);e?(e._x_hideChildren||(e._x_hideChildren=[]),e._x_hideChildren.push(r)):i(()=>{let n=e=>{var t=Promise.all([e._x_hidePromise,...(e._x_hideChildren||[]).map(n)]).then(([e])=>null==e?void 0:e());return delete e._x_hidePromise,delete e._x_hideChildren,t};n(r).catch(e=>{if(!e.isFromCancelledTransition)throw e})})}))});function skipDuringClone(t,n=()=>{}){return(...e)=>(isCloning?n:t)(...e)}function onlyDuringClone(t){return(...e)=>isCloning&&t(...e)}var interceptors=[];function interceptClone(e){interceptors.push(e)}function cloneNode(t,n){interceptors.forEach(e=>e(t,n)),isCloning=!0,dontRegisterReactiveSideEffects(()=>{initTree(n,(e,t)=>{t(e,()=>{})})}),isCloning=!1}var isCloningLegacy=!1;function clone(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),isCloningLegacy=isCloning=!0,dontRegisterReactiveSideEffects(()=>{cloneTree(t)}),isCloningLegacy=isCloning=!1}function cloneTree(e){let r=!1;initTree(e,(e,n)=>{walk(e,(e,t)=>{if(r&&isRoot(e))return t();r=!0,n(e,t)})})}function dontRegisterReactiveSideEffects(e){let n=effect;overrideEffect((e,t)=>{e=n(e);return release(e),()=>{}}),e(),overrideEffect(n)}function bind(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=reactive({})),e._x_bindings[t]=n,t=r.includes("camel")?camelCase(t):t){case"value":bindInputValue(e,n);break;case"style":bindStyles(e,n);break;case"class":bindClasses(e,n);break;case"selected":case"checked":bindAttributeAndProperty(e,t,n);break;default:bindAttribute(e,t,n)}}function bindInputValue(t,e){isRadio(t)?(void 0===t.attributes.value&&(t.value=e),window.fromModel&&(t.checked="boolean"==typeof e?safeParseBoolean(t.value)===e:checkedAttrLooseCompare(t.value,e))):isCheckbox(t)?Number.isInteger(e)?t.value=e:Array.isArray(e)||"boolean"==typeof e||[null,void 0].includes(e)?Array.isArray(e)?t.checked=e.some(e=>checkedAttrLooseCompare(e,t.value)):t.checked=!!e:t.value=String(e):"SELECT"===t.tagName?updateSelect(t,e):t.value!==e&&(t.value=void 0===e?"":e)}function bindClasses(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=setClasses(e,t)}function bindStyles(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=setStyles(e,t)}function bindAttributeAndProperty(e,t,n){bindAttribute(e,t,n),setPropertyIfChanged(e,t,n)}function bindAttribute(e,t,n){[null,void 0,!1].includes(n)&&attributeShouldntBePreservedIfFalsy(t)?e.removeAttribute(t):setIfChanged(e,t,n=isBooleanAttr(t)?t:n)}function setIfChanged(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function setPropertyIfChanged(e,t,n){e[t]!==n&&(e[t]=n)}function updateSelect(e,t){const n=[].concat(t).map(e=>e+"");Array.from(e.options).forEach(e=>{e.selected=n.includes(e.value)})}function camelCase(e){return e.toLowerCase().replace(/-(\w)/g,(e,t)=>t.toUpperCase())}function checkedAttrLooseCompare(e,t){return e==t}function safeParseBoolean(e){return!![1,"1","true","on","yes",!0].includes(e)||![0,"0","false","off","no",!1].includes(e)&&(e?Boolean(e):null)}var booleanAttributes=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function isBooleanAttr(e){return booleanAttributes.has(e)}function attributeShouldntBePreservedIfFalsy(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function getBinding(e,t,n){return e._x_bindings&&void 0!==e._x_bindings[t]?e._x_bindings[t]:getAttributeBinding(e,t,n)}function extractProp(t,n,e,r=!0){if(t._x_bindings&&void 0!==t._x_bindings[n])return t._x_bindings[n];if(t._x_inlineBindings&&void 0!==t._x_inlineBindings[n]){let e=t._x_inlineBindings[n];return e.extract=r,dontAutoEvaluateFunctions(()=>evaluate(t,e.expression))}return getAttributeBinding(t,n,e)}function getAttributeBinding(e,t,n){e=e.getAttribute(t);return null===e?"function"==typeof n?n():n:""===e||(isBooleanAttr(t)?!![t,"true"].includes(e):e)}function isCheckbox(e){return"checkbox"===e.type||"ui-checkbox"===e.localName||"ui-switch"===e.localName}function isRadio(e){return"radio"===e.type||"ui-radio"===e.localName}function debounce(n,r){var i;return function(){var e=this,t=arguments;clearTimeout(i),i=setTimeout(function(){i=null,n.apply(e,t)},r)}}function throttle(t,n){let r;return function(){var e=arguments;r||(t.apply(this,e),r=!0,setTimeout(()=>r=!1,n))}}function entangle({get:i,set:o},{get:a,set:s}){let l=!0,c,u,e=effect(()=>{var e,t,n=i(),r=a();l?(s(cloneIfObject(n)),l=!1):(e=JSON.stringify(n),t=JSON.stringify(r),e!==c?s(cloneIfObject(n)):e!==t&&o(cloneIfObject(r))),c=JSON.stringify(i()),u=JSON.stringify(a())});return()=>{release(e)}}function cloneIfObject(e){return"object"==typeof e?JSON.parse(JSON.stringify(e)):e}function plugin(e){let t=Array.isArray(e)?e:[e];t.forEach(e=>e(alpine_default))}var stores={},isReactive=!1;function store(e,t){if(isReactive||(stores=reactive(stores),isReactive=!0),void 0===t)return stores[e];stores[e]=t,initInterceptors(stores[e]),"object"==typeof t&&null!==t&&t.hasOwnProperty("init")&&"function"==typeof t.init&&stores[e].init()}function getStores(){return stores}var binds={};function bind2(e,t){let n="function"!=typeof t?()=>t:t;return e instanceof Element?applyBindingsObject(e,n()):(binds[e]=n,()=>{})}function injectBindingProviders(n){return Object.entries(binds).forEach(([e,t])=>{Object.defineProperty(n,e,{get(){return(...e)=>t(...e)}})}),n}function applyBindingsObject(e,t,n){let r=[];for(;r.length;)r.pop()();let i=Object.entries(t).map(([e,t])=>({name:e,value:t})),o=attributesOnly(i);return directives(e,i=i.map(t=>o.find(e=>e.name===t.name)?{name:"x-bind:"+t.name,value:`"${t.value}"`}:t),n).map(e=>{r.push(e.runCleanups),e()}),()=>{for(;r.length;)r.pop()()}}var datas={};function data(e,t){datas[e]=t}function injectDataProviders(n,r){return Object.entries(datas).forEach(([e,t])=>{Object.defineProperty(n,e,{get(){return(...e)=>t.bind(r)(...e)},enumerable:!1})}),n}var Alpine={get reactive(){return reactive},get release(){return release},get effect(){return effect},get raw(){return raw},version:"3.14.9",flushAndStopDeferringMutations:flushAndStopDeferringMutations,dontAutoEvaluateFunctions:dontAutoEvaluateFunctions,disableEffectScheduling:disableEffectScheduling,startObservingMutations:startObservingMutations,stopObservingMutations:stopObservingMutations,setReactivityEngine:setReactivityEngine,onAttributeRemoved:onAttributeRemoved,onAttributesAdded:onAttributesAdded,closestDataStack:closestDataStack,skipDuringClone:skipDuringClone,onlyDuringClone:onlyDuringClone,addRootSelector:addRootSelector,addInitSelector:addInitSelector,interceptClone:interceptClone,addScopeToNode:addScopeToNode,deferMutations:deferMutations,mapAttributes:mapAttributes,evaluateLater:evaluateLater,interceptInit:interceptInit,setEvaluator:setEvaluator,mergeProxies:mergeProxies,extractProp:extractProp,findClosest:findClosest,onElRemoved:onElRemoved,closestRoot:closestRoot,destroyTree:destroyTree,interceptor:interceptor,transition:transition,setStyles:setStyles,mutateDom:mutateDom,directive:directive,entangle:entangle,throttle:throttle,debounce:debounce,evaluate:evaluate,initTree:initTree,nextTick:nextTick,prefixed:prefix,prefix:setPrefix,plugin:plugin,magic:magic,store:store,start:start,clone:clone,cloneNode:cloneNode,bound:getBinding,$data:scope,watch:watch,walk:walk,data:data,bind:bind2},alpine_default=Alpine,import_reactivity10=__toESM(require_reactivity());function getArrayOfRefObject(e){let t=[];return findClosest(e,e=>{e._x_refs&&t.push(e._x_refs)}),t}magic("nextTick",()=>nextTick),magic("dispatch",e=>dispatch.bind(dispatch,e)),magic("watch",(e,{evaluateLater:r,cleanup:i})=>(e,t)=>{let n=r(e);e=watch(()=>{let t;return n(e=>t=e),t},t);i(e)}),magic("store",getStores),magic("data",e=>scope(e)),magic("root",e=>closestRoot(e)),magic("refs",e=>e._x_refs_proxy||(e._x_refs_proxy=mergeProxies(getArrayOfRefObject(e)),e._x_refs_proxy));var globalIdMemo={};function findAndIncrementId(e){return globalIdMemo[e]||(globalIdMemo[e]=0),++globalIdMemo[e]}function closestIdRoot(e,t){return findClosest(e,e=>{if(e._x_ids&&e._x_ids[t])return!0})}function setIdRoot(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=findAndIncrementId(t))}function cacheIdByNameOnElement(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];r=r();return e._x_id[t]=r,n(()=>{delete e._x_id[t]}),r}function warnMissingPluginMagic(t,n,r){magic(n,e=>warn(`You can't use [$${n}] without first installing the "${t}" plugin here: https://alpinejs.dev/plugins/`+r,e))}magic("id",(r,{cleanup:e})=>(t,n=null)=>{return cacheIdByNameOnElement(r,t+(n?"-"+n:""),e,()=>{var e=closestIdRoot(r,t),e=e?e._x_ids[t]:findAndIncrementId(t);return n?t+`-${e}-`+n:t+"-"+e})}),interceptClone((e,t)=>{e._x_id&&(t._x_id=e._x_id)}),magic("el",e=>e),warnMissingPluginMagic("Focus","focus","focus"),warnMissingPluginMagic("Persist","persist","persist"),directive("modelable",(r,{expression:e},{evaluateLater:t,cleanup:i})=>{let n=t(e),o=()=>{let t;return n(e=>t=e),t},a=t(e+" = __placeholder"),s=e=>a(()=>{},{scope:{__placeholder:e}});t=o();s(t),queueMicrotask(()=>{if(r._x_model){r._x_removeModelListeners.default();let e=r._x_model.get,t=r._x_model.set;var n=entangle({get(){return e()},set(e){t(e)}},{get(){return o()},set(e){s(e)}});i(n)}})}),directive("teleport",(t,{modifiers:n,expression:r},{cleanup:e})=>{"template"!==t.tagName.toLowerCase()&&warn("x-teleport can only be used on a <template> tag",t);let i=getTarget(r),o=t.content.cloneNode(!0).firstElementChild,a=(((t._x_teleport=o)._x_teleportBack=t).setAttribute("data-teleport-template",!0),o.setAttribute("data-teleport-target",!0),t._x_forwardEvents&&t._x_forwardEvents.forEach(e=>{o.addEventListener(e,e=>{e.stopPropagation(),t.dispatchEvent(new e.constructor(e.type,e))})}),addScopeToNode(o,{},t),(e,t,n)=>{n.includes("prepend")?t.parentNode.insertBefore(e,t):n.includes("append")?t.parentNode.insertBefore(e,t.nextSibling):t.appendChild(e)});mutateDom(()=>{a(o,i,n),skipDuringClone(()=>{initTree(o)})()}),t._x_teleportPutBack=()=>{let e=getTarget(r);mutateDom(()=>{a(t._x_teleport,e,n)})},e(()=>mutateDom(()=>{o.remove(),destroyTree(o)}))});var teleportContainerDuringClone=document.createElement("div");function getTarget(e){var t=skipDuringClone(()=>document.querySelector(e),()=>teleportContainerDuringClone)();return t||warn(`Cannot find x-teleport element for selector: "${e}"`),t}var handler=()=>{};function on(n,r,i,t){let o=n,a=e=>t(e),s={};var e=(t,n)=>e=>n(t,e);if(i.includes("dot")&&(r=dotSyntax(r)),i.includes("camel")&&(r=camelCase2(r)),i.includes("passive")&&(s.passive=!0),i.includes("capture")&&(s.capture=!0),i.includes("window")&&(o=window),i.includes("document")&&(o=document),i.includes("debounce")){let e=i[i.indexOf("debounce")+1]||"invalid-wait";var l=isNumeric(e.split("ms")[0])?Number(e.split("ms")[0]):250;a=debounce(a,l)}if(i.includes("throttle")){let e=i[i.indexOf("throttle")+1]||"invalid-wait";l=isNumeric(e.split("ms")[0])?Number(e.split("ms")[0]):250;a=throttle(a,l)}return i.includes("prevent")&&(a=e(a,(e,t)=>{t.preventDefault(),e(t)})),i.includes("stop")&&(a=e(a,(e,t)=>{t.stopPropagation(),e(t)})),i.includes("once")&&(a=e(a,(e,t)=>{e(t),o.removeEventListener(r,a,s)})),(i.includes("away")||i.includes("outside"))&&(o=document,a=e(a,(e,t)=>{n.contains(t.target)||!1===t.target.isConnected||n.offsetWidth<1&&n.offsetHeight<1||!1!==n._x_isShown&&e(t)})),i.includes("self")&&(a=e(a,(e,t)=>{t.target===n&&e(t)})),(isKeyEvent(r)||isClickEvent(r))&&(a=e(a,(e,t)=>{isListeningForASpecificKeyThatHasntBeenPressed(t,i)||e(t)})),o.addEventListener(r,a,s),()=>{o.removeEventListener(r,a,s)}}function dotSyntax(e){return e.replace(/-/g,".")}function camelCase2(e){return e.toLowerCase().replace(/-(\w)/g,(e,t)=>t.toUpperCase())}function isNumeric(e){return!Array.isArray(e)&&!isNaN(e)}function kebabCase2(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function isKeyEvent(e){return["keydown","keyup"].includes(e)}function isClickEvent(t){return["contextmenu","click","mouse"].some(e=>t.includes(e))}function isListeningForASpecificKeyThatHasntBeenPressed(t,e){let n=e.filter(e=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(e));if(n.includes("debounce")&&(e=n.indexOf("debounce"),n.splice(e,isNumeric((n[e+1]||"invalid-wait").split("ms")[0])?2:1)),n.includes("throttle")&&(e=n.indexOf("throttle"),n.splice(e,isNumeric((n[e+1]||"invalid-wait").split("ms")[0])?2:1)),0===n.length)return!1;if(1===n.length&&keyToModifiers(t.key).includes(n[0]))return!1;const r=["ctrl","shift","alt","meta","cmd","super"].filter(e=>n.includes(e));if((n=n.filter(e=>!r.includes(e)),0<r.length)&&r.filter(e=>t[(e="cmd"!==e&&"super"!==e?e:"meta")+"Key"]).length===r.length){if(isClickEvent(t.type))return!1;if(keyToModifiers(t.key).includes(n[0]))return!1}return!0}function keyToModifiers(t){if(!t)return[];t=kebabCase2(t);let n={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return n[t]=t,Object.keys(n).map(e=>{if(n[e]===t)return e}).filter(e=>e)}function getInputValue(t,n,r,i){return mutateDom(()=>{if(r instanceof CustomEvent&&void 0!==r.detail)return null!==r.detail&&void 0!==r.detail?r.detail:r.target.value;if(isCheckbox(t)){if(Array.isArray(i)){let t=null;return t=n.includes("number")?safeParseNumber(r.target.value):n.includes("boolean")?safeParseBoolean(r.target.value):r.target.value,r.target.checked?i.includes(t)?i:i.concat([t]):i.filter(e=>!checkedAttrLooseCompare2(e,t))}return r.target.checked}if("select"===t.tagName.toLowerCase()&&t.multiple)return n.includes("number")?Array.from(r.target.selectedOptions).map(e=>{return safeParseNumber(e.value||e.text)}):n.includes("boolean")?Array.from(r.target.selectedOptions).map(e=>{return safeParseBoolean(e.value||e.text)}):Array.from(r.target.selectedOptions).map(e=>e.value||e.text);{let e;return e=!isRadio(t)||r.target.checked?r.target.value:i,n.includes("number")?safeParseNumber(e):n.includes("boolean")?safeParseBoolean(e):n.includes("trim")?e.trim():e}})}function safeParseNumber(e){var t=e?parseFloat(e):null;return isNumeric2(t)?t:e}function checkedAttrLooseCompare2(e,t){return e==t}function isNumeric2(e){return!Array.isArray(e)&&!isNaN(e)}function isGetterSetter(e){return null!==e&&"object"==typeof e&&"function"==typeof e.get&&"function"==typeof e.set}handler.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})},directive("ignore",handler),directive("effect",skipDuringClone((e,{expression:t},{effect:n})=>{n(evaluateLater(e,t))})),directive("model",(t,{modifiers:n,expression:r},{effect:e,cleanup:i})=>{let o=t,a=evaluateLater(o=n.includes("parent")?t.parentNode:o,r),s,l=(s="string"==typeof r?evaluateLater(o,r+" = __placeholder"):"function"==typeof r&&"string"==typeof r()?evaluateLater(o,r()+" = __placeholder"):()=>{},()=>{let t;return a(e=>t=e),isGetterSetter(t)?t.get():t}),c=e=>{let t;a(e=>t=e),isGetterSetter(t)?t.set(e):s(()=>{},{scope:{__placeholder:e}})};"string"==typeof r&&"radio"===t.type&&mutateDom(()=>{t.hasAttribute("name")||t.setAttribute("name",r)});var u="select"===t.tagName.toLowerCase()||["checkbox","radio"].includes(t.type)||n.includes("lazy")?"change":"input",u=isCloning?()=>{}:on(t,u,n,e=>{c(getInputValue(t,n,e,l()))});if(n.includes("fill")&&([void 0,null,""].includes(l())||isCheckbox(t)&&Array.isArray(l())||"select"===t.tagName.toLowerCase()&&t.multiple)&&c(getInputValue(t,n,{target:t},l())),t._x_removeModelListeners||(t._x_removeModelListeners={}),t._x_removeModelListeners.default=u,i(()=>t._x_removeModelListeners.default()),t.form){let e=on(t.form,"reset",[],e=>{nextTick(()=>t._x_model&&t._x_model.set(getInputValue(t,n,{target:t},l())))});i(()=>e())}t._x_model={get(){return l()},set(e){c(e)}},t._x_forceModelUpdate=e=>{void 0===e&&"string"==typeof r&&r.match(/\./)&&(e=""),window.fromModel=!0,mutateDom(()=>bind(t,"value",e)),delete window.fromModel},e(()=>{var e=l();n.includes("unintrusive")&&document.activeElement.isSameNode(t)||t._x_forceModelUpdate(e)})}),directive("cloak",e=>queueMicrotask(()=>mutateDom(()=>e.removeAttribute(prefix("cloak"))))),addInitSelector(()=>`[${prefix("init")}]`),directive("init",skipDuringClone((e,{expression:t},{evaluate:n})=>("string"!=typeof t||!!t.trim())&&n(t,{},!1))),directive("text",(t,{expression:e},{effect:n,evaluateLater:r})=>{let i=r(e);n(()=>{i(e=>{mutateDom(()=>{t.textContent=e})})})}),directive("html",(t,{expression:e},{effect:n,evaluateLater:r})=>{let i=r(e);n(()=>{i(e=>{mutateDom(()=>{t.innerHTML=e,t._x_ignoreSelf=!0,initTree(t),delete t._x_ignoreSelf})})})}),mapAttributes(startingWith(":",into(prefix("bind:"))));var handler2=(t,{value:n,modifiers:r,expression:i,original:o},{effect:a,cleanup:s})=>{if(n){if("key"===n)return storeKeyForXFor(t,i);if(!(t._x_inlineBindings&&t._x_inlineBindings[n]&&t._x_inlineBindings[n].extract)){let e=evaluateLater(t,i);a(()=>e(e=>{void 0===e&&"string"==typeof i&&i.match(/\./)&&(e=""),mutateDom(()=>bind(t,n,e,r))})),s(()=>{t._x_undoAddedClasses&&t._x_undoAddedClasses(),t._x_undoAddedStyles&&t._x_undoAddedStyles()})}}else{a={};injectBindingProviders(a);let e=evaluateLater(t,i);void e(e=>{applyBindingsObject(t,e,o)},{scope:a})}};function storeKeyForXFor(e,t){e._x_keyExpression=t}function shouldSkipRegisteringDataDuringClone(e){return!!isCloning&&(!!isCloningLegacy||e.hasAttribute("data-has-alpine-state"))}function loop(b,y,e,x){let w=b;e(n=>{void 0===(n=isNumeric3(n)&&0<=n?Array.from(Array(n).keys(),e=>e+1):n)&&(n=[]);let a=b._x_lookup,t=b._x_prevKeys,s=[],l=[];if("object"!=typeof(e=n)||Array.isArray(e))for(let e=0;e<n.length;e++){var r=getIterationScopeVariables(y,n[e],e,n);x(e=>{l.includes(e)&&warn("Duplicate key on x-for",b),l.push(e)},{scope:{index:e,...r}}),s.push(r)}else n=Object.entries(n).map(([e,t])=>{t=getIterationScopeVariables(y,t,e,n);x(e=>{l.includes(e)&&warn("Duplicate key on x-for",b),l.push(e)},{scope:{index:e,...t}}),s.push(t)});var e;let i=[],c=[],o=[],u=[];for(let e=0;e<t.length;e++){var d=t[e];-1===l.indexOf(d)&&o.push(d)}t=t.filter(e=>!o.includes(e));let f="template";for(let e=0;e<l.length;e++){var p,_,g=l[e],m=t.indexOf(g);-1===m?(t.splice(e,0,g),i.push([f,e])):m!==e?(p=t.splice(e,1)[0],_=t.splice(m-1,1)[0],t.splice(e,0,_),t.splice(m,0,p),c.push([p,_])):u.push(g),f=g}for(let t=0;t<o.length;t++){let e=o[t];e in a&&(mutateDom(()=>{destroyTree(a[e]),a[e].remove()}),delete a[e])}for(let o=0;o<c.length;o++){let[e,t]=c[o],n=a[e],r=a[t],i=document.createElement("div");mutateDom(()=>{r||warn('x-for ":key" is undefined or invalid',w,t,a),r.after(i),n.after(r),r._x_currentIfEl&&r.after(r._x_currentIfEl),i.before(n),n._x_currentIfEl&&n.after(n._x_currentIfEl),i.remove()}),r._x_refreshXForScope(s[l.indexOf(t)])}for(let r=0;r<i.length;r++){var[h,v]=i[r];let e="template"===h?w:a[h];e._x_currentIfEl&&(e=e._x_currentIfEl);h=s[v],v=l[v];let t=document.importNode(w.content,!0).firstElementChild,n=reactive(h);addScopeToNode(t,n,w),t._x_refreshXForScope=e=>{Object.entries(e).forEach(([e,t])=>{n[e]=t})},mutateDom(()=>{e.after(t),skipDuringClone(()=>initTree(t))()}),"object"==typeof v&&warn("x-for key cannot be an object, it must be a string or an integer",w),a[v]=t}for(let e=0;e<u.length;e++)a[u[e]]._x_refreshXForScope(s[l.indexOf(u[e])]);w._x_prevKeys=l})}function parseForExpression(e){var r=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/;let i=e.match(/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/);if(i){let e={},t=(e.items=i[2].trim(),i[1].replace(/^\s*\(|\)\s*$/g,"").trim()),n=t.match(r);return n?(e.item=t.replace(r,"").trim(),e.index=n[1].trim(),n[2]&&(e.collection=n[2].trim())):e.item=t,e}}function getIterationScopeVariables(t,n,e,r){let i={};if(/^\[.*\]$/.test(t.item)&&Array.isArray(n)){let e=t.item.replace("[","").replace("]","").split(",").map(e=>e.trim());e.forEach((e,t)=>{i[e]=n[t]})}else if(/^\{.*\}$/.test(t.item)&&!Array.isArray(n)&&"object"==typeof n){let e=t.item.replace("{","").replace("}","").split(",").map(e=>e.trim());e.forEach(e=>{i[e]=n[e]})}else i[t.item]=n;return t.index&&(i[t.index]=e),t.collection&&(i[t.collection]=r),i}function isNumeric3(e){return!Array.isArray(e)&&!isNaN(e)}function handler3(){}function warnMissingPluginDirective(t,n,r){directive(n,e=>warn(`You can't use [x-${n}] without first installing the "${t}" plugin here: https://alpinejs.dev/plugins/`+r,e))}handler2.inline=(e,{value:t,expression:n})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:n,extract:!1})},directive("bind",handler2),addRootSelector(()=>`[${prefix("data")}]`),directive("data",(r,{expression:i},{cleanup:o})=>{if(!shouldSkipRegisteringDataDuringClone(r)){i=""===i?"{}":i;var a={},s=(injectMagics(a,r),{});injectDataProviders(s,a);let e=evaluate(r,i,{scope:s}),t=(injectMagics(e=void 0!==e&&!0!==e?e:{},r),reactive(e)),n=(initInterceptors(t),addScopeToNode(r,t));t.init&&evaluate(r,t.init),o(()=>{t.destroy&&evaluate(r,t.destroy),n()})}}),interceptClone((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))}),directive("show",(t,{modifiers:n,expression:e},{effect:r})=>{let i=evaluateLater(t,e),o=(t._x_doHide||(t._x_doHide=()=>{mutateDom(()=>{t.style.setProperty("display","none",n.includes("important")?"important":void 0)})}),t._x_doShow||(t._x_doShow=()=>{mutateDom(()=>{1===t.style.length&&"none"===t.style.display?t.removeAttribute("style"):t.style.removeProperty("display")})}),()=>{t._x_doHide(),t._x_isShown=!1}),a=()=>{t._x_doShow(),t._x_isShown=!0},s=()=>setTimeout(a),l=once(e=>(e?a:o)(),e=>{"function"==typeof t._x_toggleAndCascadeWithTransitions?t._x_toggleAndCascadeWithTransitions(t,e,a,o):(e?s:o)()}),c,u=!0;r(()=>i(e=>{!u&&e===c||(n.includes("immediate")&&(e?s:o)(),l(e),c=e,u=!1)}))}),directive("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=parseForExpression(t),o=evaluateLater(e,i.items),a=evaluateLater(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>loop(e,i,o,a)),r(()=>{Object.values(e._x_lookup).forEach(e=>mutateDom(()=>{destroyTree(e),e.remove()})),delete e._x_prevKeys,delete e._x_lookup})}),handler3.inline=(e,{expression:t},{cleanup:n})=>{let r=closestRoot(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])},directive("ref",handler3),directive("if",(t,{expression:e},{effect:n,cleanup:r})=>{"template"!==t.tagName.toLowerCase()&&warn("x-if can only be used on a <template> tag",t);let i=evaluateLater(t,e);n(()=>i(e=>{if(e){if(t._x_currentIfEl)return void t._x_currentIfEl;let e=t.content.cloneNode(!0).firstElementChild;addScopeToNode(e,{},t),mutateDom(()=>{t.after(e),skipDuringClone(()=>initTree(e))()}),t._x_currentIfEl=e,t._x_undoIf=()=>{mutateDom(()=>{destroyTree(e),e.remove()}),delete t._x_currentIfEl},e}else t._x_undoIf&&(t._x_undoIf(),delete t._x_undoIf)})),r(()=>t._x_undoIf&&t._x_undoIf())}),directive("id",(t,{expression:e},{evaluate:n})=>{let r=n(e);r.forEach(e=>setIdRoot(t,e))}),interceptClone((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)}),mapAttributes(startingWith("@",into(prefix("on:")))),directive("on",skipDuringClone((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let o=r?evaluateLater(e,r):()=>{},a=("template"===e.tagName.toLowerCase()&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t)),on(e,t,n,e=>{o(()=>{},{scope:{$event:e},params:[e]})}));i(()=>a())})),warnMissingPluginDirective("Collapse","collapse","collapse"),warnMissingPluginDirective("Intersect","intersect","intersect"),warnMissingPluginDirective("Focus","trap","focus"),warnMissingPluginDirective("Mask","mask","mask"),alpine_default.setEvaluator(normalEvaluator),alpine_default.setReactivityEngine({reactive:import_reactivity10.reactive,effect:import_reactivity10.effect,release:import_reactivity10.stop,raw:import_reactivity10.toRaw});var src_default=alpine_default,module_default=src_default;