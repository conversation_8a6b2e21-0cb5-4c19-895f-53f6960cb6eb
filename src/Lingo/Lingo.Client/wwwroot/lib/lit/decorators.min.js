/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/lit@3.3.0/decorators.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
export*from"@lit/reactive-element/decorators/custom-element.js";export*from"@lit/reactive-element/decorators/property.js";export*from"@lit/reactive-element/decorators/state.js";export*from"@lit/reactive-element/decorators/event-options.js";export*from"@lit/reactive-element/decorators/query.js";export*from"@lit/reactive-element/decorators/query-all.js";export*from"@lit/reactive-element/decorators/query-async.js";export*from"@lit/reactive-element/decorators/query-assigned-elements.js";export*from"@lit/reactive-element/decorators/query-assigned-nodes.js";
//# sourceMappingURL=decorators.js.map
