{"name": "lit", "version": "3.3.0", "publishConfig": {"access": "public"}, "description": "A library for building fast, lightweight web components", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/lit/lit.git", "directory": "packages/lit"}, "author": "Google LLC", "homepage": "https://lit.dev/", "main": "index.js", "module": "index.js", "type": "module", "exports": {".": {"types": "./development/index.d.ts", "default": "./index.js"}, "./async-directive.js": {"types": "./development/async-directive.d.ts", "default": "./async-directive.js"}, "./decorators.js": {"types": "./development/decorators.d.ts", "default": "./decorators.js"}, "./decorators/custom-element.js": {"types": "./development/decorators/custom-element.d.ts", "default": "./decorators/custom-element.js"}, "./decorators/event-options.js": {"types": "./development/decorators/event-options.d.ts", "default": "./decorators/event-options.js"}, "./decorators/property.js": {"types": "./development/decorators/property.d.ts", "default": "./decorators/property.js"}, "./decorators/query-all.js": {"types": "./development/decorators/query-all.d.ts", "default": "./decorators/query-all.js"}, "./decorators/query-assigned-elements.js": {"types": "./development/decorators/query-assigned-elements.d.ts", "default": "./decorators/query-assigned-elements.js"}, "./decorators/query-assigned-nodes.js": {"types": "./development/decorators/query-assigned-nodes.d.ts", "default": "./decorators/query-assigned-nodes.js"}, "./decorators/query-async.js": {"types": "./development/decorators/query-async.d.ts", "default": "./decorators/query-async.js"}, "./decorators/query.js": {"types": "./development/decorators/query.d.ts", "default": "./decorators/query.js"}, "./decorators/state.js": {"types": "./development/decorators/state.d.ts", "default": "./decorators/state.js"}, "./directive-helpers.js": {"types": "./development/directive-helpers.d.ts", "default": "./directive-helpers.js"}, "./directive.js": {"types": "./development/directive.d.ts", "default": "./directive.js"}, "./directives/async-append.js": {"types": "./development/directives/async-append.d.ts", "default": "./directives/async-append.js"}, "./directives/async-replace.js": {"types": "./development/directives/async-replace.d.ts", "default": "./directives/async-replace.js"}, "./directives/cache.js": {"types": "./development/directives/cache.d.ts", "default": "./directives/cache.js"}, "./directives/choose.js": {"types": "./development/directives/choose.d.ts", "default": "./directives/choose.js"}, "./directives/class-map.js": {"types": "./development/directives/class-map.d.ts", "default": "./directives/class-map.js"}, "./directives/guard.js": {"types": "./development/directives/guard.d.ts", "default": "./directives/guard.js"}, "./directives/if-defined.js": {"types": "./development/directives/if-defined.d.ts", "default": "./directives/if-defined.js"}, "./directives/join.js": {"types": "./development/directives/join.d.ts", "default": "./directives/join.js"}, "./directives/keyed.js": {"types": "./development/directives/keyed.d.ts", "default": "./directives/keyed.js"}, "./directives/live.js": {"types": "./development/directives/live.d.ts", "default": "./directives/live.js"}, "./directives/map.js": {"types": "./development/directives/map.d.ts", "default": "./directives/map.js"}, "./directives/range.js": {"types": "./development/directives/range.d.ts", "default": "./directives/range.js"}, "./directives/ref.js": {"types": "./development/directives/ref.d.ts", "default": "./directives/ref.js"}, "./directives/repeat.js": {"types": "./development/directives/repeat.d.ts", "default": "./directives/repeat.js"}, "./directives/style-map.js": {"types": "./development/directives/style-map.d.ts", "default": "./directives/style-map.js"}, "./directives/template-content.js": {"types": "./development/directives/template-content.d.ts", "default": "./directives/template-content.js"}, "./directives/unsafe-html.js": {"types": "./development/directives/unsafe-html.d.ts", "default": "./directives/unsafe-html.js"}, "./directives/unsafe-mathml.js": {"types": "./development/directives/unsafe-mathml.d.ts", "default": "./directives/unsafe-mathml.js"}, "./directives/unsafe-svg.js": {"types": "./development/directives/unsafe-svg.d.ts", "default": "./directives/unsafe-svg.js"}, "./directives/until.js": {"types": "./development/directives/until.d.ts", "default": "./directives/until.js"}, "./directives/when.js": {"types": "./development/directives/when.d.ts", "default": "./directives/when.js"}, "./html.js": {"types": "./development/html.d.ts", "default": "./html.js"}, "./polyfill-support.js": {"types": "./development/polyfill-support.d.ts", "default": "./polyfill-support.js"}, "./static-html.js": {"types": "./development/static-html.d.ts", "default": "./static-html.js"}}, "scripts": {"build": "wireit", "build:ts": "wireit", "build:ts:types": "wireit", "build:rollup": "wireit", "checksize": "wireit", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "test:node": "wireit", "test:node-dev": "wireit"}, "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:types", "../lit-html:build", "../lit-element:build", "../reactive-element:build"]}, "build:ts": {"#comment": "Note this also builds polyfill-support via a TypeScript project reference.", "command": "tsc --build --pretty", "clean": "if-file-deleted", "dependencies": ["../lit-element:build:ts:types", "../lit-html:build:ts:types", "../reactive-element:build:ts:types"], "files": ["src/**/*.ts", "tsconfig.json", "tsconfig.polyfill-support.json"], "output": ["development/**/*.{js,js.map,d.ts,d.ts.map}", "tsconfig.tsbuildinfo", "tsconfig.polyfill-support.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}", "decorators/*.d.ts{,.map}", "directives/*.d.ts{,.map}"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js", "src/test/*_test.html", "src/test/polyfill-support/*_test.html"], "output": ["async-directive.js{,.map}", "decorators.js{,.map}", "directive-helpers.js{,.map}", "directive.js{,.map}", "html.js{,.map}", "index.js{,.map}", "polyfill-support.js{,.map}", "static-html.js{,.map}", "decorators/*.js{,.map}", "directives/*.js{,.map}", "test/*_test.html", "development/test/*_test.html", "test/polyfill-support/*_test.html", "development/test/polyfill-support/*_test.html", "lit-core.min.js{,.map}", "lit-all.min.js{,.map}"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod", "test:node", "test:node-dev"]}, "test:dev": {"#comment": "TODO(aomarks) There is only one lit html test, and it doesn't work", "command": "MODE=dev node ../tests/run-web-tests.js \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:prod": {"command": "MODE=prod node ../tests/run-web-tests.js \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["../lit-element:build:rollup", "../lit-html:build:rollup", "../reactive-element:build:rollup", "build:ts", "build:rollup", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:node": {"command": "node development/test/node-imports.js", "dependencies": ["build:ts", "build:rollup"], "files": [], "output": []}, "test:node-dev": {"command": "node --conditions=development development/test/node-imports.js", "dependencies": ["build:ts", "build:rollup"], "files": [], "output": []}}, "files": ["/async-directive.{d.ts.map,d.ts,js.map,js}", "/decorators.{d.ts.map,d.ts,js.map,js}", "/directive-helpers.{d.ts.map,d.ts,js.map,js}", "/directive.{d.ts.map,d.ts,js.map,js}", "/html.{d.ts.map,d.ts,js.map,js}", "/index.{d.ts.map,d.ts,js.map,js}", "/legacy-decorators.{d.ts.map,d.ts,js.map,js}", "/polyfill-support.{d.ts.map,d.ts,js.map,js}", "/static-html.{d.ts.map,d.ts,js.map,js}", "/decorators/", "/directives/", "/development/", "!/development/test/", "/logo.svg"], "dependencies": {"@lit/reactive-element": "^2.1.0", "lit-element": "^4.2.0", "lit-html": "^3.3.0"}, "devDependencies": {"@lit-internal/scripts": "^1.0.1", "@webcomponents/shadycss": "^1.8.0", "@webcomponents/template": "^1.4.4", "@webcomponents/webcomponentsjs": "^2.8.0", "tslib": "^2.0.3"}, "typings": "index.d.ts"}