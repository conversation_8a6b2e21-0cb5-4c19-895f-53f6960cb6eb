@using Lingo.Core.Models

<div class="course-card" @onclick="() => SelectCourse()">
    <div class="course-image">
        <img src="@Course.ImageUrl" alt="@Course.Language" />
    </div>
    <div class="course-content">
        <h3>@Course.Name</h3>
        <p>@Course.Description</p>
        <div class="course-meta">
            <span class="difficulty @GetDifficultyClass()">@Course.Difficulty</span>
            <span class="lesson-count">@Course.Lessons.Count lessons</span>
        </div>
        @if (Progress != null)
        {
            <div class="progress-bar">
                <div class="progress-fill" style="width: @(Progress.CompletionPercentage)%"></div>
            </div>
            <p class="progress-text">@Progress.CompletedLessons/@Progress.TotalLessons lessons completed</p>
        }
    </div>
</div>




