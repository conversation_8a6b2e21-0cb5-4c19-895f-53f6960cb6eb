using Lingo.Core.Models;
using Microsoft.AspNetCore.Components;

namespace Lingo.Client.Components;

public partial class CourseCard
{
    [Parameter] public Course Course { get; set; } = new();
    [Parameter] public UserCourseProgress? Progress { get; set; }
    [Inject] private NavigationManager Navigation { get; set; } = default!;

    private void SelectCourse()
    {
        Navigation.NavigateTo($"/courses/{Course.Id}");
    }

    private string GetDifficultyClass()
    {
        return Course.Difficulty switch
        {
            DifficultyLevel.Beginner => "difficulty-beginner",
            DifficultyLevel.Intermediate => "difficulty-intermediate",
            DifficultyLevel.Advanced => "difficulty-advanced",
            _ => "difficulty-beginner"
        };
    }
}
