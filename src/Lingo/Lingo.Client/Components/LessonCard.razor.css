.lesson-card {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 8px 0;
    border: 2px solid #e5e5e5;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.lesson-card:hover:not(.lesson-locked) {
    border-color: #58cc02;
    transform: translateX(4px);
}

.lesson-completed {
    border-color: #58cc02;
    background: #f8fff8;
}

.lesson-locked {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #ccc;
}

.lesson-available {
    border-color: #1cb0f6;
}

.lesson-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #1cb0f6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 16px;
    flex-shrink: 0;
}

.lesson-completed .lesson-number {
    background: #58cc02;
}

.lesson-locked .lesson-number {
    background: #ccc;
}

.lesson-content {
    flex: 1;
}

.lesson-content h4 {
    margin: 0 0 8px 0;
    color: #3c3c3c;
    font-size: 1.1em;
    font-weight: 600;
}

.lesson-content p {
    margin: 0 0 12px 0;
    color: #777;
    font-size: 0.9em;
    line-height: 1.4;
}

.lesson-meta {
    display: flex;
    gap: 12px;
    align-items: center;
}

.xp-badge {
    background: #ffc107;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
}

.completion-badge {
    background: #58cc02;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.lesson-status {
    margin-left: 16px;
}

.status-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
}

.status-icon.completed {
    background: #58cc02;
    color: white;
}

.status-icon.locked {
    background: #ccc;
    color: white;
}

.status-icon.available {
    background: #1cb0f6;
    color: white;
}
