@using Lingo.Core.Models

<div class="lesson-card @GetStatusClass()" @onclick="() => StartLesson()">
    <div class="lesson-number">@Lesson.OrderIndex</div>
    <div class="lesson-content">
        <h4>@Lesson.Title</h4>
        <p>@Lesson.Description</p>
        <div class="lesson-meta">
            <span class="xp-badge">+@Lesson.ExperiencePoints XP</span>
            @if (Progress != null && Progress.IsCompleted)
            {
                <span class="completion-badge">
                    <i class="icon-check"></i>
                    @Progress.Score/@Progress.MaxScore
                </span>
            }
        </div>
    </div>
    <div class="lesson-status">
        @if (Progress != null && Progress.IsCompleted)
        {
            <div class="status-icon completed">✓</div>
        }
        else if (IsLocked)
        {
            <div class="status-icon locked">🔒</div>
        }
        else
        {
            <div class="status-icon available">▶</div>
        }
    </div>
</div>




