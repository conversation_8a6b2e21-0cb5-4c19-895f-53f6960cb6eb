.course-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    margin: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    border-color: #58cc02;
}

.course-image img {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
}

.course-content h3 {
    margin: 15px 0 10px 0;
    color: #3c3c3c;
    font-size: 1.2em;
    font-weight: 600;
}

.course-content p {
    color: #777;
    margin-bottom: 15px;
    line-height: 1.4;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.difficulty {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-beginner {
    background: #e8f5e8;
    color: #58cc02;
}

.difficulty-intermediate {
    background: #fff3cd;
    color: #ffc107;
}

.difficulty-advanced {
    background: #f8d7da;
    color: #dc3545;
}

.lesson-count {
    color: #777;
    font-size: 0.9em;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #58cc02, #89e219);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8em;
    color: #777;
    margin: 0;
}
