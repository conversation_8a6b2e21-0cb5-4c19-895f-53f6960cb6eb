using Lingo.Core.Models;
using Microsoft.AspNetCore.Components;

namespace Lingo.Client.Components;

public partial class LessonCard
{
    [Parameter] public Lesson Lesson { get; set; } = new();
    [Parameter] public UserLessonProgress? Progress { get; set; }
    [Parameter] public bool IsLocked { get; set; }
    [Inject] private NavigationManager Navigation { get; set; } = default!;

    private void StartLesson()
    {
        if (!IsLocked)
        {
            Navigation.NavigateTo($"/lessons/{Lesson.Id}");
        }
    }

    private string GetStatusClass()
    {
        if (Progress != null && Progress.IsCompleted)
            return "lesson-completed";
        if (IsLocked)
            return "lesson-locked";
        return "lesson-available";
    }
}
