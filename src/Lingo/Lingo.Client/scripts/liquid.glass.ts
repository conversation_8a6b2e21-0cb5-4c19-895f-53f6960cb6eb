// TypeScript Liquid Glass Effect
// Created by <PERSON> (https://github.com/shuding/liquid-glass) in 2025.
// Converted to TypeScript with full type support

// Type definitions
interface UVCoordinate {
    x: number;
    y: number;
}

interface MousePosition {
    x: number;
    y: number;
}

interface TextureResult {
    type: 't';
    x: number;
    y: number;
}

interface Position {
    x: number;
    y: number;
}

interface ShaderOptions {
    width?: number;
    height?: number;
    fragment?: FragmentShaderFunction;
}

type FragmentShaderFunction = (uv: UVCoordinate, mouse: MousePosition) => TextureResult;

(function(): void {
    'use strict';

    // Check if liquid glass already exists and destroy it
    if ((window as any).liquidGlass) {
        (window as any).liquidGlass.destroy();
        console.log('Previous liquid glass effect removed.');
    }

    // Utility functions
    function smoothStep(a: number, b: number, t: number): number {
        t = Math.max(0, Math.min(1, (t - a) / (b - a)));
        return t * t * (3 - 2 * t);
    }

    function length(x: number, y: number): number {
        return Math.sqrt(x * x + y * y);
    }

    function roundedRectSDF(x: number, y: number, width: number, height: number, radius: number): number {
        const qx: number = Math.abs(x) - width + radius;
        const qy: number = Math.abs(y) - height + radius;
        return Math.min(Math.max(qx, qy), 0) + length(Math.max(qx, 0), Math.max(qy, 0)) - radius;
    }

    function texture(x: number, y: number): TextureResult {
        return { type: 't', x, y };
    }

    // Generate unique ID
    function generateId(): string {
        return 'liquid-glass-' + Math.random().toString(36).substr(2, 9);
    }

    // Main Shader class
    class Shader {
        public readonly width: number;
        public readonly height: number;
        public readonly fragment: FragmentShaderFunction;
        public readonly canvasDPI: number;
        public readonly id: string;
        public readonly offset: number;

        public mouse: MousePosition;
        public mouseUsed: boolean;

        public container!: HTMLDivElement;
        public svg!: SVGSVGElement;
        public canvas!: HTMLCanvasElement;
        public context!: CanvasRenderingContext2D;
        public feImage!: SVGFEImageElement;
        public feDisplacementMap!: SVGFEDisplacementMapElement;

        constructor(options: ShaderOptions = {}) {
            this.width = options.width || 100;
            this.height = options.height || 100;
            this.fragment = options.fragment || ((uv: UVCoordinate): TextureResult => texture(uv.x, uv.y));
            this.canvasDPI = 1;
            this.id = generateId();
            this.offset = 10; // Viewport boundary offset

            this.mouse = { x: 0, y: 0 };
            this.mouseUsed = false;

            this.createElement();
            this.setupEventListeners();
            this.updateShader();
        }

        private createElement(): void {
            // Create container
            this.container = document.createElement('div');
            this.container.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: ${this.width}px;
        height: ${this.height}px;
        overflow: hidden;
        border-radius: 150px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), 0 -10px 25px inset rgba(0, 0, 0, 0.15);
        cursor: grab;
        backdrop-filter: url(#${this.id}_filter) blur(0.25px) contrast(1.2) brightness(1.05) saturate(1.1);
        z-index: 9999;
        pointer-events: auto;
      `;

            // Create SVG filter
            this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg') as SVGSVGElement;
            this.svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
            this.svg.setAttribute('width', '0');
            this.svg.setAttribute('height', '0');
            this.svg.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 9998;
      `;

            const defs: SVGDefsElement = document.createElementNS('http://www.w3.org/2000/svg', 'defs') as SVGDefsElement;
            const filter: SVGFilterElement = document.createElementNS('http://www.w3.org/2000/svg', 'filter') as SVGFilterElement;
            filter.setAttribute('id', `${this.id}_filter`);
            filter.setAttribute('filterUnits', 'userSpaceOnUse');
            filter.setAttribute('colorInterpolationFilters', 'sRGB');
            filter.setAttribute('x', '0');
            filter.setAttribute('y', '0');
            filter.setAttribute('width', this.width.toString());
            filter.setAttribute('height', this.height.toString());

            this.feImage = document.createElementNS('http://www.w3.org/2000/svg', 'feImage') as SVGFEImageElement;
            this.feImage.setAttribute('id', `${this.id}_map`);
            this.feImage.setAttribute('width', this.width.toString());
            this.feImage.setAttribute('height', this.height.toString());

            this.feDisplacementMap = document.createElementNS('http://www.w3.org/2000/svg', 'feDisplacementMap') as SVGFEDisplacementMapElement;
            this.feDisplacementMap.setAttribute('in', 'SourceGraphic');
            this.feDisplacementMap.setAttribute('in2', `${this.id}_map`);
            this.feDisplacementMap.setAttribute('xChannelSelector', 'R');
            this.feDisplacementMap.setAttribute('yChannelSelector', 'G');

            filter.appendChild(this.feImage);
            filter.appendChild(this.feDisplacementMap);
            defs.appendChild(filter);
            this.svg.appendChild(defs);

            // Create canvas for displacement map (hidden)
            this.canvas = document.createElement('canvas');
            this.canvas.width = this.width * this.canvasDPI;
            this.canvas.height = this.height * this.canvasDPI;
            this.canvas.style.display = 'none';

            const context: CanvasRenderingContext2D | null = this.canvas.getContext('2d');
            if (!context) {
                throw new Error('Failed to get 2D rendering context from canvas');
            }
            this.context = context;
        }

        private constrainPosition(x: number, y: number): Position {
            const viewportWidth: number = window.innerWidth;
            const viewportHeight: number = window.innerHeight;

            // Calculate boundaries with offset
            const minX: number = this.offset;
            const maxX: number = viewportWidth - this.width - this.offset;
            const minY: number = this.offset;
            const maxY: number = viewportHeight - this.height - this.offset;

            // Constrain position
            const constrainedX: number = Math.max(minX, Math.min(maxX, x));
            const constrainedY: number = Math.max(minY, Math.min(maxY, y));

            return { x: constrainedX, y: constrainedY };
        }

        private setupEventListeners(): void {
            let isDragging: boolean = false;
            let startX: number;
            let startY: number;
            let initialX: number;
            let initialY: number;

            this.container.addEventListener('mousedown', (e: MouseEvent): void => {
                isDragging = true;
                this.container.style.cursor = 'grabbing';
                startX = e.clientX;
                startY = e.clientY;
                const rect: DOMRect = this.container.getBoundingClientRect();
                initialX = rect.left;
                initialY = rect.top;
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e: MouseEvent): void => {
                if (isDragging) {
                    const deltaX: number = e.clientX - startX;
                    const deltaY: number = e.clientY - startY;

                    // Calculate new position
                    const newX: number = initialX + deltaX;
                    const newY: number = initialY + deltaY;

                    // Constrain position within viewport bounds
                    const constrained: Position = this.constrainPosition(newX, newY);

                    this.container.style.left = constrained.x + 'px';
                    this.container.style.top = constrained.y + 'px';
                    this.container.style.transform = 'none';
                }

                // Update mouse position for shader
                const rect: DOMRect = this.container.getBoundingClientRect();
                this.mouse.x = (e.clientX - rect.left) / rect.width;
                this.mouse.y = (e.clientY - rect.top) / rect.height;

                if (this.mouseUsed) {
                    this.updateShader();
                }
            });

            document.addEventListener('mouseup', (): void => {
                isDragging = false;
                this.container.style.cursor = 'grab';
            });

            // Handle window resize to maintain constraints
            window.addEventListener('resize', (): void => {
                const rect: DOMRect = this.container.getBoundingClientRect();
                const constrained: Position = this.constrainPosition(rect.left, rect.top);

                if (rect.left !== constrained.x || rect.top !== constrained.y) {
                    this.container.style.left = constrained.x + 'px';
                    this.container.style.top = constrained.y + 'px';
                    this.container.style.transform = 'none';
                }
            });
        }

        private updateShader(): void {
            const mouseProxy: MousePosition = new Proxy(this.mouse, {
                get: (target: MousePosition, prop: string | symbol): any => {
                    this.mouseUsed = true;
                    return (target as any)[prop];
                }
            });

            this.mouseUsed = false;

            const w: number = this.width * this.canvasDPI;
            const h: number = this.height * this.canvasDPI;
            const data: Uint8ClampedArray = new Uint8ClampedArray(w * h * 4);

            let maxScale: number = 0;
            const rawValues: number[] = [];

            for (let i: number = 0; i < data.length; i += 4) {
                const x: number = (i / 4) % w;
                const y: number = Math.floor(i / 4 / w);
                const pos: TextureResult = this.fragment(
                    { x: x / w, y: y / h },
                    mouseProxy
                );
                const dx: number = pos.x * w - x;
                const dy: number = pos.y * h - y;
                maxScale = Math.max(maxScale, Math.abs(dx), Math.abs(dy));
                rawValues.push(dx, dy);
            }

            maxScale *= 0.5;

            let index: number = 0;
            for (let i: number = 0; i < data.length; i += 4) {
                const r: number = rawValues[index++] / maxScale + 0.5;
                const g: number = rawValues[index++] / maxScale + 0.5;
                data[i] = r * 255;
                data[i + 1] = g * 255;
                data[i + 2] = 0;
                data[i + 3] = 255;
            }

            this.context.putImageData(new ImageData(data, w, h), 0, 0);
            this.feImage.setAttributeNS('http://www.w3.org/1999/xlink', 'href', this.canvas.toDataURL());
            this.feDisplacementMap.setAttribute('scale', (maxScale / this.canvasDPI).toString());
        }

        public appendTo(parent: HTMLElement): void {
            parent.appendChild(this.svg);
            parent.appendChild(this.container);
        }

        public destroy(): void {
            this.svg.remove();
            this.container.remove();
            this.canvas.remove();
        }
    }

    // Create the liquid glass effect
    function createLiquidGlass(): void {
        // Create shader
        const shader: Shader = new Shader({
            width: 300,
            height: 200,
            fragment: (uv: UVCoordinate, mouse: MousePosition): TextureResult => {
                const ix: number = uv.x - 0.5;
                const iy: number = uv.y - 0.5;
                const distanceToEdge: number = roundedRectSDF(
                    ix,
                    iy,
                    0.3,
                    0.2,
                    0.6
                );
                const displacement: number = smoothStep(0.8, 0, distanceToEdge - 0.15);
                const scaled: number = smoothStep(0, 1, displacement);
                return texture(ix * scaled + 0.5, iy * scaled + 0.5);
            }
        });

        // Add to page
        shader.appendTo(document.body);

        console.log('Liquid Glass effect created! Drag the glass around the page.');

        // Return shader instance so it can be removed if needed
        (window as any).liquidGlass = shader;
    }

    // Initialize
    createLiquidGlass();
})();