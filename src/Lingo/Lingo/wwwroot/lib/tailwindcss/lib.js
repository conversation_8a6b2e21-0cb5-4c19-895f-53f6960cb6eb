"use strict";var Fr=Object.defineProperty;var zr=(t,r)=>{for(var n in r)Fr(t,n,{get:r[n],enumerable:!0})};var Je={};zr(Je,{Features:()=>pe,__unstable__loadDesignSystem:()=>vi,compile:()=>hi,compileAst:()=>Ur,default:()=>Te});var vt="4.0.1";var ye=92,Se=47,Ee=42,Lr=34,Mr=39,Wr=58,Re=59,re=10,be=32,Oe=9,yt=123,Ye=125,Xe=40,bt=41,Br=91,qr=93,wt=45,Ze=64,Hr=33;function ne(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=[],i=null,s=null,l="",f="",d;for(let c=0;c<t.length;c++){let p=t.charCodeAt(c);if(p===ye)l+=t.slice(c,c+2),c+=1;else if(p===Se&&t.charCodeAt(c+1)===Ee){let m=c;for(let y=c+2;y<t.length;y++)if(d=t.charCodeAt(y),d===ye)y+=1;else if(d===Ee&&t.charCodeAt(y+1)===Se){c=y+1;break}let h=t.slice(m,c+1);h.charCodeAt(2)===Hr&&n.push(Ke(h.slice(2,-2)))}else if(p===Mr||p===Lr){let m=c;for(let h=c+1;h<t.length;h++)if(d=t.charCodeAt(h),d===ye)h+=1;else if(d===p){c=h;break}else{if(d===Re&&t.charCodeAt(h+1)===re)throw new Error(`Unterminated string: ${t.slice(m,h+1)+String.fromCharCode(p)}`);if(d===re)throw new Error(`Unterminated string: ${t.slice(m,h)+String.fromCharCode(p)}`)}l+=t.slice(m,c+1)}else{if((p===be||p===re||p===Oe)&&(d=t.charCodeAt(c+1))&&(d===be||d===re||d===Oe))continue;if(p===re){if(l.length===0)continue;d=l.charCodeAt(l.length-1),d!==be&&d!==re&&d!==Oe&&(l+=" ")}else if(p===wt&&t.charCodeAt(c+1)===wt&&l.length===0){let m="",h=c,y=-1;for(let v=c+2;v<t.length;v++)if(d=t.charCodeAt(v),d===ye)v+=1;else if(d===Se&&t.charCodeAt(v+1)===Ee){for(let x=v+2;x<t.length;x++)if(d=t.charCodeAt(x),d===ye)x+=1;else if(d===Ee&&t.charCodeAt(x+1)===Se){v=x+1;break}}else if(y===-1&&d===Wr)y=l.length+v-h;else if(d===Re&&m.length===0){l+=t.slice(h,v),c=v;break}else if(d===Xe)m+=")";else if(d===Br)m+="]";else if(d===yt)m+="}";else if((d===Ye||t.length-1===v)&&m.length===0){c=v-1,l+=t.slice(h,v);break}else(d===bt||d===qr||d===Ye)&&m.length>0&&t[v]===m[m.length-1]&&(m=m.slice(0,-1));let b=Qe(l,y);i?i.nodes.push(b):r.push(b),l=""}else if(p===Re&&l.charCodeAt(0)===Ze)s=we(l),i?i.nodes.push(s):r.push(s),l="",s=null;else if(p===Re&&f[f.length-1]!==")"){let m=Qe(l);i?i.nodes.push(m):r.push(m),l=""}else if(p===yt&&f[f.length-1]!==")")f+="}",s=L(l.trim()),i&&i.nodes.push(s),e.push(i),i=s,l="",s=null;else if(p===Ye&&f[f.length-1]!==")"){if(f==="")throw new Error("Missing opening {");if(f=f.slice(0,-1),l.length>0)if(l.charCodeAt(0)===Ze)s=we(l),i?i.nodes.push(s):r.push(s),l="",s=null;else{let h=l.indexOf(":");i&&i.nodes.push(Qe(l,h))}let m=e.pop()??null;m===null&&i&&r.push(i),i=m,l="",s=null}else if(p===Xe)f+=")",l+="(";else if(p===bt){if(f[f.length-1]!==")")throw new Error("Missing opening (");f=f.slice(0,-1),l+=")"}else{if(l.length===0&&(p===be||p===re||p===Oe))continue;l+=String.fromCharCode(p)}}}if(l.charCodeAt(0)===Ze&&r.push(we(l)),f.length>0&&i){if(i.kind==="rule")throw new Error(`Missing closing } at ${i.selector}`);if(i.kind==="at-rule")throw new Error(`Missing closing } at ${i.name} ${i.params}`)}return n.length>0?n.concat(r):r}function we(t,r=[]){for(let n=5;n<t.length;n++){let e=t.charCodeAt(n);if(e===be||e===Xe){let i=t.slice(0,n).trim(),s=t.slice(n).trim();return P(i,s,r)}}return P(t.trim(),"",r)}function Qe(t,r=t.indexOf(":")){let n=t.indexOf("!important",r+1);return a(t.slice(0,r).trim(),t.slice(r+1,n===-1?t.length:n).trim(),n!==-1)}var Gr=64;function U(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function P(t,r="",n=[]){return{kind:"at-rule",name:t,params:r,nodes:n}}function L(t,r=[]){return t.charCodeAt(0)===Gr?we(t,r):U(t,r)}function a(t,r,n=!1){return{kind:"declaration",property:t,value:r,important:n}}function Ke(t){return{kind:"comment",value:t}}function ie(t,r){return{kind:"context",context:t,nodes:r}}function j(t){return{kind:"at-root",nodes:t}}function D(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let s=t[i],l=n[n.length-1]??null;if(s.kind==="context"){if(D(s.nodes,r,n,{...e,...s.context})===2)return 2;continue}n.push(s);let f=r(s,{parent:l,context:e,path:n,replaceWith(d){Array.isArray(d)?d.length===0?t.splice(i,1):d.length===1?t[i]=d[0]:t.splice(i,1,...d):t[i]=d,i--}})??0;if(n.pop(),f===2)return 2;if(f!==1&&(s.kind==="rule"||s.kind==="at-rule")){n.push(s);let d=D(s.nodes,r,n,e);if(n.pop(),d===2)return 2}}}function _e(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let s=t[i],l=n[n.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")n.push(s),_e(s.nodes,r,n,e),n.pop();else if(s.kind==="context"){_e(s.nodes,r,n,{...e,...s.context});continue}n.push(s),r(s,{parent:l,context:e,path:n,replaceWith(f){Array.isArray(f)?f.length===0?t.splice(i,1):f.length===1?t[i]=f[0]:t.splice(i,1,...f):t[i]=f,i+=f.length-1}}),n.pop()}}function oe(t){let r=[],n=new Set;function e(s,l,f=0){if(s.kind==="declaration"){if(s.property==="--tw-sort"||s.value===void 0||s.value===null)return;l.push(s)}else if(s.kind==="rule")if(s.selector==="&")for(let d of s.nodes){let c=[];e(d,c,f+1),l.push(...c)}else{let d={...s,nodes:[]};for(let c of s.nodes)e(c,d.nodes,f+1);l.push(d)}else if(s.kind==="at-rule"&&s.name==="@property"&&f===0){if(n.has(s.params))return;n.add(s.params);let d={...s,nodes:[]};for(let c of s.nodes)e(c,d.nodes,f+1);l.push(d)}else if(s.kind==="at-rule"){let d={...s,nodes:[]};for(let c of s.nodes)e(c,d.nodes,f+1);l.push(d)}else if(s.kind==="at-root")for(let d of s.nodes){let c=[];e(d,c,0);for(let p of c)r.push(p)}else if(s.kind==="context"){if(s.context.reference)return;for(let d of s.nodes)e(d,l,f)}else s.kind==="comment"&&l.push(s)}let i=[];for(let s of t)e(s,i,0);return i.concat(r)}function G(t){function r(e,i=0){let s="",l="  ".repeat(i);if(e.kind==="declaration")s+=`${l}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){s+=`${l}${e.selector} {
`;for(let f of e.nodes)s+=r(f,i+1);s+=`${l}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${l}${e.name} ${e.params};
`;s+=`${l}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let f of e.nodes)s+=r(f,i+1);s+=`${l}}
`}else if(e.kind==="comment")s+=`${l}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return s}let n="";for(let e of t){let i=r(e);i!==""&&(n+=i)}return n}function tt(t){return{kind:"word",value:t}}function Jr(t,r){return{kind:"function",value:t,nodes:r}}function Yr(t){return{kind:"separator",value:t}}function ce(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],s=r(i,{parent:n,replaceWith(l){Array.isArray(l)?l.length===0?t.splice(e,1):l.length===1?t[e]=l[0]:t.splice(e,1,...l):t[e]=l,e--}})??0;if(s===2)return 2;if(s!==1&&i.kind==="function"&&ce(i.nodes,r,i)===2)return 2}}function q(t){let r="";for(let n of t)switch(n.kind){case"word":case"separator":{r+=n.value;break}case"function":r+=n.value+"("+q(n.nodes)+")"}return r}var Zr=92,Qr=41,kt=58,xt=44,Xr=34,At=61,Ct=62,$t=60,Nt=10,en=40,tn=39,Tt=47,Vt=32,St=9;function M(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",s;for(let l=0;l<t.length;l++){let f=t.charCodeAt(l);switch(f){case kt:case xt:case At:case Ct:case $t:case Nt:case Tt:case Vt:case St:{if(i.length>0){let m=tt(i);e?e.nodes.push(m):r.push(m),i=""}let d=l,c=l+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==kt&&s!==xt&&s!==At&&s!==Ct&&s!==$t&&s!==Nt&&s!==Tt&&s!==Vt&&s!==St));c++);l=c-1;let p=Yr(t.slice(d,c));e?e.nodes.push(p):r.push(p);break}case tn:case Xr:{let d=l;for(let c=l+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Zr)c+=1;else if(s===f){l=c;break}i+=t.slice(d,l+1);break}case en:{let d=Jr(i,[]);i="",e?e.nodes.push(d):r.push(d),n.push(d),e=d;break}case Qr:{let d=n.pop();if(i.length>0){let c=tt(i);d.nodes.push(c),i=""}n.length>0?e=n[n.length-1]:e=null;break}default:i+=String.fromCharCode(f)}}return i.length>0&&r.push(tt(i)),r}var rt=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],De=["anchor-size"],Et=new RegExp(`(${De.join("|")})\\(`,"g");function ke(t){return t.indexOf("(")!==-1&&rt.some(r=>t.includes(`${r}(`))}function Rt(t){if(!rt.some(i=>t.includes(i)))return t;let r=!1;De.some(i=>t.includes(i))&&(Et.lastIndex=0,t=t.replace(Et,(i,s)=>(r=!0,`$${De.indexOf(s)}$(`)));let n="",e=[];for(let i=0;i<t.length;i++){let s=t[i];if(s==="("){n+=s;let l=i;for(let d=i-1;d>=0;d--){let c=t.charCodeAt(d);if(c>=48&&c<=57)l=d;else if(c>=97&&c<=122)l=d;else break}let f=t.slice(l,i);if(rt.includes(f)){e.unshift(!0);continue}else if(e[0]&&f===""){e.unshift(!0);continue}e.unshift(!1);continue}else if(s===")")n+=s,e.shift();else if(s===","&&e[0]){n+=", ";continue}else{if(s===" "&&e[0]&&n[n.length-1]===" ")continue;if((s==="+"||s==="*"||s==="/"||s==="-")&&e[0]){let l=n.trimEnd(),f=l[l.length-1];if(f==="+"||f==="*"||f==="/"||f==="-"){n+=s;continue}else if(f==="("||f===","){n+=s;continue}else t[i-1]===" "?n+=`${s} `:n+=` ${s} `}else if(e[0]&&t.startsWith("to-zero",i)){let l=i;i+=7,n+=t.slice(l,i+1)}else n+=s}}return r?n.replace(/\$(\d+)\$/g,(i,s)=>De[s]??i):n}function X(t){if(t.indexOf("(")===-1)return xe(t);let r=M(t);return nt(r),t=q(r),t=Rt(t),t}function xe(t){let r="";for(let n=0;n<t.length;n++){let e=t[n];e==="\\"&&t[n+1]==="_"?(r+="_",n+=1):e==="_"?r+=" ":r+=e}return r}function nt(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=xe(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=xe(r.value);for(let n=0;n<r.nodes.length;n++)n==0&&r.nodes[n].kind==="word"||nt([r.nodes[n]]);break}r.value=xe(r.value),nt(r.nodes);break}case"separator":case"word":{r.value=xe(r.value);break}default:rn(r)}}function rn(t){throw new Error(`Unexpected value: ${t}`)}var je=new Uint8Array(256);function _(t,r){let n=0,e=[],i=0,s=t.length,l=r.charCodeAt(0);for(let f=0;f<s;f++){let d=t.charCodeAt(f);if(n===0&&d===l){e.push(t.slice(i,f)),i=f+1;continue}switch(d){case 92:f+=1;break;case 39:case 34:for(;++f<s;){let c=t.charCodeAt(f);if(c===92){f+=1;continue}if(c===d)break}break;case 40:je[n]=41,n++;break;case 91:je[n]=93,n++;break;case 123:je[n]=125,n++;break;case 93:case 125:case 41:n>0&&d===je[n-1]&&n--;break}}return e.push(t.slice(i)),e}var nn=58,Ot=45,Kt=97,_t=122;function*Pt(t,r){let n=_(t,":");if(r.theme.prefix){if(n.length===1||n[0]!==r.theme.prefix)return null;n.shift()}let e=n.pop(),i=[];for(let m=n.length-1;m>=0;--m){let h=r.parseVariant(n[m]);if(h===null)return;i.push(h)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:i,important:s,raw:t});let[l,f=null,d]=_(e,"/");if(d)return;let c=f===null?null:it(f);if(f!==null&&c===null)return;if(l[0]==="["){if(l[l.length-1]!=="]")return;let m=l.charCodeAt(1);if(m!==Ot&&!(m>=Kt&&m<=_t))return;l=l.slice(1,-1);let h=l.indexOf(":");if(h===-1||h===0||h===l.length-1)return;let y=l.slice(0,h),b=X(l.slice(h+1));yield{kind:"arbitrary",property:y,value:b,modifier:c,variants:i,important:s,raw:t};return}let p;if(l[l.length-1]==="]"){let m=l.indexOf("-[");if(m===-1)return;let h=l.slice(0,m);if(!r.utilities.has(h,"functional"))return;let y=l.slice(m+1);p=[[h,y]]}else if(l[l.length-1]===")"){let m=l.indexOf("-(");if(m===-1)return;let h=l.slice(0,m);if(!r.utilities.has(h,"functional"))return;let y=l.slice(m+2,-1),b=_(y,":"),v=null;if(b.length===2&&(v=b[0],y=b[1]),y[0]!=="-"&&y[1]!=="-")return;p=[[h,v===null?`[var(${y})]`:`[${v}:var(${y})]`]]}else p=jt(l,m=>r.utilities.has(m,"functional"));for(let[m,h]of p){let y={kind:"functional",root:m,modifier:c,value:null,variants:i,important:s,raw:t};if(h===null){yield y;continue}{let b=h.indexOf("[");if(b!==-1){if(h[h.length-1]!=="]")return;let x=X(h.slice(b+1,-1)),S="";for(let V=0;V<x.length;V++){let R=x.charCodeAt(V);if(R===nn){S=x.slice(0,V),x=x.slice(V+1);break}if(!(R===Ot||R>=Kt&&R<=_t))break}if(x.length===0||x.trim().length===0)continue;y.value={kind:"arbitrary",dataType:S||null,value:x}}else{let x=f===null||y.modifier?.kind==="arbitrary"?null:`${h}/${f}`;y.value={kind:"named",value:h,fraction:x}}}yield y}}function it(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=X(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=X(t.slice(1,-1));return r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function Dt(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let n=X(t.slice(1,-1));if(n.length===0||n.trim().length===0)return null;let e=n[0]===">"||n[0]==="+"||n[0]==="~";return!e&&n[0]!=="@"&&!n.includes("&")&&(n=`&:is(${n})`),{kind:"arbitrary",selector:n,relative:e}}{let[n,e=null,i]=_(t,"/");if(i)return null;let s=jt(n,l=>r.variants.has(l));for(let[l,f]of s)switch(r.variants.kind(l)){case"static":return f!==null||e!==null?null:{kind:"static",root:l};case"functional":{let d=e===null?null:it(e);if(e!==null&&d===null)return null;if(f===null)return{kind:"functional",root:l,modifier:d,value:null};if(f[f.length-1]==="]"){if(f[0]!=="[")continue;let c=X(f.slice(1,-1));return c.length===0||c.trim().length===0?null:{kind:"functional",root:l,modifier:d,value:{kind:"arbitrary",value:c}}}if(f[f.length-1]===")"){if(f[0]!=="(")continue;let c=X(f.slice(1,-1));return c.length===0||c.trim().length===0||c[0]!=="-"&&c[1]!=="-"?null:{kind:"functional",root:l,modifier:d,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:l,modifier:d,value:{kind:"named",value:f}}}case"compound":{if(f===null)return null;let d=r.parseVariant(f);if(d===null||!r.variants.compoundsWith(l,d))return null;let c=e===null?null:it(e);return e!==null&&c===null?null:{kind:"compound",root:l,modifier:c,variant:d}}}}return null}function*jt(t,r){r(t)&&(yield[t,null]);let n=t.lastIndexOf("-");if(n===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,n);if(r(e)){let i=[e,t.slice(n+1)];if(i[1]==="")break;yield i}n=t.lastIndexOf("-",n-1)}while(n>0)}function le(t,r,n){if(t===r)return 0;let e=t.indexOf("("),i=r.indexOf("("),s=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),l=i===-1?r.replace(/[\d.]+/g,""):r.slice(0,i),f=(s===l?0:s<l?-1:1)||(n==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(f)?t<r?-1:1:f}var I=class extends Map{constructor(n){super();this.factory=n}get(n){let e=super.get(n);return e===void 0&&(e=this.factory(n,this),this.set(n,e)),e}};var on=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),ln=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function Ut(t){return t.charCodeAt(0)===35||ln.test(t)||on.has(t.toLowerCase())}var an={color:Ut,length:lt,percentage:ot,ratio:kn,number:yn,integer:N,url:Ft,position:Cn,"bg-size":$n,"line-width":un,image:dn,"family-name":gn,"generic-name":pn,"absolute-size":mn,"relative-size":hn,angle:Vn,vector:En};function z(t,r){if(t.startsWith("var("))return null;for(let n of r)if(an[n]?.(t))return n;return null}var sn=/^url\(.*\)$/;function Ft(t){return sn.test(t)}function un(t){return t==="thin"||t==="medium"||t==="thick"}var cn=/^(?:element|image|cross-fade|image-set)\(/,fn=/^(repeating-)?(conic|linear|radial)-gradient\(/;function dn(t){let r=0;for(let n of _(t,","))if(!n.startsWith("var(")){if(Ft(n)){r+=1;continue}if(fn.test(n)){r+=1;continue}if(cn.test(n)){r+=1;continue}return!1}return r>0}function pn(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function gn(t){let r=0;for(let n of _(t,",")){let e=n.charCodeAt(0);if(e>=48&&e<=57)return!1;n.startsWith("var(")||(r+=1)}return r>0}function mn(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function hn(t){return t==="larger"||t==="smaller"}var Q=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,vn=new RegExp(`^${Q.source}$`);function yn(t){return vn.test(t)||ke(t)}var bn=new RegExp(`^${Q.source}%$`);function ot(t){return bn.test(t)||ke(t)}var wn=new RegExp(`^${Q.source}s*/s*${Q.source}$`);function kn(t){return wn.test(t)||ke(t)}var xn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],An=new RegExp(`^${Q.source}(${xn.join("|")})$`);function lt(t){return An.test(t)||ke(t)}function Cn(t){let r=0;for(let n of _(t," ")){if(n==="center"||n==="top"||n==="right"||n==="bottom"||n==="left"){r+=1;continue}if(!n.startsWith("var(")){if(lt(n)||ot(n)){r+=1;continue}return!1}}return r>0}function $n(t){let r=0;for(let n of _(t,",")){if(n==="cover"||n==="contain"){r+=1;continue}let e=_(n," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(i=>i==="auto"||lt(i)||ot(i))){r+=1;continue}}return r>0}var Nn=["deg","rad","grad","turn"],Tn=new RegExp(`^${Q.source}(${Nn.join("|")})$`);function Vn(t){return Tn.test(t)}var Sn=new RegExp(`^${Q.source} +${Q.source} +${Q.source}$`);function En(t){return Sn.test(t)}function N(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function fe(t){return zt(t,.25)}function Ue(t){return zt(t,.25)}function zt(t,r){let n=Number(t);return n>=0&&n%r===0&&String(n)===String(t)}var Rn=new Set(["inset","inherit","initial","revert","unset"]),It=/^-?(\d+|\.\d+)(.*?)$/g;function ae(t,r){return _(t,",").map(e=>{e=e.trim();let i=_(e," ").filter(c=>c.trim()!==""),s=null,l=null,f=null;for(let c of i)Rn.has(c)||(It.test(c)?(l===null?l=c:f===null&&(f=c),It.lastIndex=0):s===null&&(s=c));if(l===null||f===null)return e;let d=r(s??"currentcolor");return s!==null?e.replace(s,d):`${e} ${d}`}).join(", ")}var On=/^-?[a-z][a-zA-Z0-9/%._-]*$/,Kn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,at=class{utilities=new I(()=>[]);completions=new Map;static(r,n){this.utilities.get(r).push({kind:"static",compileFn:n})}functional(r,n,e){this.utilities.get(r).push({kind:"functional",compileFn:n,options:e})}has(r,n){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===n)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,n){this.completions.set(r,n)}keys(r){let n=[];for(let[e,i]of this.utilities.entries())for(let s of i)if(s.kind===r){n.push(e);break}return n}};function $(t,r,n){return P("@property",t,[a("syntax",n?`"${n}"`:'"*"'),a("inherits","false"),...r?[a("initial-value",r)]:[]])}function J(t,r){if(r===null)return t;let n=Number(r);return Number.isNaN(n)||(r=`${n*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function W(t,r,n){if(!r)return t;if(r.kind==="arbitrary")return J(t,r.value);let e=n.resolve(r.value,["--opacity"]);return e?J(t,e):Ue(r.value)?J(t,`${r.value}%`):null}function H(t,r,n){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentColor";break}default:{e=r.resolve(t.value.value,n);break}}return e?W(e,t.modifier,r):null}function Mt(t){let r=new at;function n(o,u){function*g(k){for(let w of t.keysInNamespaces(k))yield w.replaceAll("_",".")}r.suggest(o,()=>{let k=[];for(let w of u()){if(typeof w=="string"){k.push({values:[w],modifiers:[]});continue}let T=[...w.values??[],...g(w.valueThemeKeys??[])],O=[...w.modifiers??[],...g(w.modifierThemeKeys??[])];w.hasDefaultValue&&T.unshift(null),k.push({supportsNegative:w.supportsNegative,values:T,modifiers:O})}return k})}function e(o,u){r.static(o,()=>u.map(g=>typeof g=="function"?g():a(g[0],g[1])))}function i(o,u){function g({negative:k}){return w=>{let T=null;if(w.value)if(w.value.kind==="arbitrary"){if(w.modifier)return;T=w.value.value}else{if(T=t.resolve(w.value.fraction??w.value.value,u.themeKeys??[]),T===null&&u.supportsFractions&&w.value.fraction){let[O,C]=_(w.value.fraction,"/");if(!N(O)||!N(C))return;T=`calc(${w.value.fraction} * 100%)`}if(T===null&&k&&u.handleNegativeBareValue){if(T=u.handleNegativeBareValue(w.value),!T?.includes("/")&&w.modifier)return;if(T!==null)return u.handle(T)}if(T===null&&u.handleBareValue&&(T=u.handleBareValue(w.value),!T?.includes("/")&&w.modifier))return}else{if(w.modifier)return;T=u.defaultValue!==void 0?u.defaultValue:t.resolve(null,u.themeKeys??[])}if(T!==null)return u.handle(k?`calc(${T} * -1)`:T)}}u.supportsNegative&&r.functional(`-${o}`,g({negative:!0})),r.functional(o,g({negative:!1})),n(o,()=>[{supportsNegative:u.supportsNegative,valueThemeKeys:u.themeKeys??[],hasDefaultValue:u.defaultValue!==void 0&&u.defaultValue!==null}])}function s(o,u){r.functional(o,g=>{if(!g.value)return;let k=null;if(g.value.kind==="arbitrary"?(k=g.value.value,k=W(k,g.modifier,t)):k=H(g,t,u.themeKeys),k!==null)return u.handle(k)}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:u.themeKeys,modifiers:Array.from({length:21},(g,k)=>`${k*5}`)}])}function l(o,u,g,{supportsNegative:k=!1,supportsFractions:w=!1}={}){k&&r.static(`-${o}-px`,()=>g("-1px")),r.static(`${o}-px`,()=>g("1px")),i(o,{themeKeys:u,supportsFractions:w,supportsNegative:k,defaultValue:null,handleBareValue:({value:T})=>{let O=t.resolve(null,["--spacing"]);return!O||!fe(T)?null:`calc(${O} * ${T})`},handleNegativeBareValue:({value:T})=>{let O=t.resolve(null,["--spacing"]);return!O||!fe(T)?null:`calc(${O} * -${T})`},handle:g}),n(o,()=>[{values:t.get(["--spacing"])?["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"]:[],supportsNegative:k,valueThemeKeys:u}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,u]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[u,"auto"]]),e(`${o}-full`,[[u,"100%"]]),e(`-${o}-full`,[[u,"-100%"]]),l(o,["--inset","--spacing"],g=>[a(u,g)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),i("z",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--z-index"],handle:o=>[a("z-index",o)]}),n("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","calc(-infinity)"]]),e("order-last",[["order","calc(infinity)"]]),e("order-none",[["order","0"]]),i("order",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--order"],handle:o=>[a("order",o)]}),n("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),i("col",{themeKeys:["--grid-column"],handle:o=>[a("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),i("col-span",{handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),i("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[a("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),i("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[a("grid-column-end",o)]}),n("col-span",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:[]}]),n("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-column-start"]}]),n("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),i("row",{themeKeys:["--grid-row"],handle:o=>[a("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),i("row-span",{themeKeys:[],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),i("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[a("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),i("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[a("grid-row-end",o)]}),n("row-span",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:[]}]),n("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-row-start"]}]),n("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,u]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[u,"auto"]]),l(o,["--margin","--spacing"],g=>[a(u,g)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),i("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("overflow","hidden"),a("display","-webkit-box"),a("-webkit-box-orient","vertical"),a("-webkit-line-clamp",o)]}),n("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),i("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[u,g]=_(o,"/");return!N(u)||!N(g)?null:o},handle:o=>[a("aspect-ratio",o)]});for(let[o,u]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",u],["height",u]]),e(`w-${o}`,[["width",u]]),e(`min-w-${o}`,[["min-width",u]]),e(`max-w-${o}`,[["max-width",u]]),e(`h-${o}`,[["height",u]]),e(`min-h-${o}`,[["min-height",u]]),e(`max-h-${o}`,[["max-height",u]]);e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),l("size",["--size","--spacing"],o=>[a("--tw-sort","size"),a("width",o),a("height",o)],{supportsFractions:!0});for(let[o,u,g]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])l(o,u,k=>[a(g,k)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((g,k)=>le(g,k,"asc"));let u=[a("--tw-sort","--tw-container-component"),a("width","100%")];for(let g of o)u.push(P("@media",`(width >= ${g})`,[a("max-width",g)]));return u}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[a("flex",o.value.value)];if(o.value.fraction){let[u,g]=_(o.value.fraction,"/");return!N(u)||!N(g)?void 0:[a("flex",`calc(${o.value.fraction} * 100%)`)]}if(N(o.value.value))return o.modifier?void 0:[a("flex",o.value.value)]}}),i("shrink",{defaultValue:"1",handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("flex-shrink",o)]}),i("grow",{defaultValue:"1",handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("flex-grow",o)]}),n("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),n("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),l("basis",["--flex-basis","--spacing","--container"],o=>[a("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let f=()=>j([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);l("border-spacing",["--border-spacing","--spacing"],o=>[f(),a("--tw-border-spacing-x",o),a("--tw-border-spacing-y",o),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-x",["--border-spacing","--spacing"],o=>[f(),a("--tw-border-spacing-x",o),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-y",["--border-spacing","--spacing"],o=>[f(),a("--tw-border-spacing-y",o),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),i("origin",{themeKeys:["--transform-origin"],handle:o=>[a("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),i("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[a("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),i("perspective",{themeKeys:["--perspective"],handle:o=>[a("perspective",o)]});let d=()=>j([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[d,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[d,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l("translate",["--translate","--spacing"],o=>[d(),a("--tw-translate-x",o),a("--tw-translate-y",o),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[d,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[d,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l(`translate-${o}`,["--translate","--spacing"],u=>[d(),a(`--tw-translate-${o}`,u),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});l("translate-z",["--translate","--spacing"],o=>[d(),a("--tw-translate-z",o),a("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("-translate-z-px",[d,["--tw-translate-z","-1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-z-px",[d,["--tw-translate-z","1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-3d",[d,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>j([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function p({negative:o}){return u=>{if(!u.value||u.modifier)return;let g;return u.value.kind==="arbitrary"?(g=u.value.value,[a("scale",g)]):(g=t.resolve(u.value.value,["--scale"]),!g&&N(u.value.value)&&(g=`${u.value.value}%`),g?(g=o?`calc(${g} * -1)`:g,[c(),a("--tw-scale-x",g),a("--tw-scale-y",g),a("--tw-scale-z",g),a("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",p({negative:!0})),r.functional("scale",p({negative:!1})),n("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])i(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:u})=>N(u)?`${u}%`:null,handle:u=>[c(),a(`--tw-scale-${o}`,u),a("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),n(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return u=>{if(!u.value||u.modifier)return;let g;if(u.value.kind==="arbitrary"){g=u.value.value;let k=u.value.dataType??z(g,["angle","vector"]);if(k==="vector")return[a("rotate",`${g} var(--tw-rotate)`)];if(k!=="angle")return[a("rotate",g)]}else if(g=t.resolve(u.value.value,["--rotate"]),!g&&N(u.value.value)&&(g=`${u.value.value}deg`),!g)return;return[a("rotate",o?`calc(${g} * -1)`:g)]}}r.functional("-rotate",m({negative:!0})),r.functional("rotate",m({negative:!1})),n("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),u=()=>j([$("--tw-rotate-x","rotateX(0)"),$("--tw-rotate-y","rotateY(0)"),$("--tw-rotate-z","rotateZ(0)"),$("--tw-skew-x","skewX(0)"),$("--tw-skew-y","skewY(0)")]);for(let g of["x","y","z"])i(`rotate-${g}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:k})=>N(k)?`${k}deg`:null,handle:k=>[u(),a(`--tw-rotate-${g}`,`rotate${g.toUpperCase()}(${k})`),a("transform",o)]}),n(`rotate-${g}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);i("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:g})=>N(g)?`${g}deg`:null,handle:g=>[u(),a("--tw-skew-x",`skewX(${g})`),a("--tw-skew-y",`skewY(${g})`),a("transform",o)]}),i("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:g})=>N(g)?`${g}deg`:null,handle:g=>[u(),a("--tw-skew-x",`skewX(${g})`),a("transform",o)]}),i("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:g})=>N(g)?`${g}deg`:null,handle:g=>[u(),a("--tw-skew-y",`skewY(${g})`),a("transform",o)]}),n("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",g=>{if(g.modifier)return;let k=null;if(g.value?g.value.kind==="arbitrary"&&(k=g.value.value):k=o,k!==null)return[u(),a("transform",k)]}),n("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);i("cursor",{themeKeys:["--cursor"],handle:o=>[a("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let h=()=>j([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[h,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[h,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[h,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let y=()=>j([$("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[y,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[y,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[y,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,u]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])l(o,["--scroll-margin","--spacing"],g=>[a(u,g)],{supportsNegative:!0});for(let[o,u]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])l(o,["--scroll-padding","--spacing"],g=>[a(u,g)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),i("list",{themeKeys:["--list-style-type"],handle:o=>[a("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),i("list-image",{themeKeys:["--list-style-image"],handle:o=>[a("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),i("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[a("columns",o)]}),n("columns",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),i("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[a("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),i("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[a("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),i("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>N(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[a("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),i("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>N(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[a("grid-template-rows",o)]}),n("grid-cols",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-template-columns"]}]),n("grid-rows",()=>[{values:Array.from({length:12},(o,u)=>`${u+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),l("gap",["--gap","--spacing"],o=>[a("gap",o)]),l("gap-x",["--gap","--spacing"],o=>[a("column-gap",o)]),l("gap-y",["--gap","--spacing"],o=>[a("row-gap",o)]),l("space-x",["--space","--spacing"],o=>[j([$("--tw-space-x-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","0"),a("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),a("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),l("space-y",["--space","--spacing"],o=>[j([$("--tw-space-y-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","0"),a("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),a("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>j([$("--tw-space-x-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>j([$("--tw-space-y-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:o=>[a("accent-color",o)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:o=>[a("caret-color",o)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:o=>[U(":where(& > :not(:last-child))",[a("--tw-sort","divide-color"),a("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]);for(let[o,u]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,u.map(g=>[g,"0"])),e(`${o}-full`,u.map(g=>[g,"calc(infinity * 1px)"])),i(o,{themeKeys:["--radius"],handle:g=>u.map(k=>a(k,g))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let u=function(g,k){r.functional(g,w=>{if(!w.value){if(w.modifier)return;let T=t.get(["--default-border-width"])??"1px",O=k.width(T);return O?[o(),...O]:void 0}if(w.value.kind==="arbitrary"){let T=w.value.value;switch(w.value.dataType??z(T,["color","line-width","length"])){case"line-width":case"length":{if(w.modifier)return;let C=k.width(T);return C?[o(),...C]:void 0}default:return T=W(T,w.modifier,t),T===null?void 0:k.color(T)}}{let T=H(w,t,["--border-color","--color"]);if(T)return k.color(T)}{if(w.modifier)return;let T=t.resolve(w.value.value,["--border-width"]);if(T){let O=k.width(T);return O?[o(),...O]:void 0}if(N(w.value.value)){let O=k.width(`${w.value.value}px`);return O?[o(),...O]:void 0}}}),n(g,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(w,T)=>`${T*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var x=u;let o=()=>j([$("--tw-border-style","solid")]);u("border",{width:g=>[a("border-style","var(--tw-border-style)"),a("border-width",g)],color:g=>[a("border-color",g)]}),u("border-x",{width:g=>[a("border-inline-style","var(--tw-border-style)"),a("border-inline-width",g)],color:g=>[a("border-inline-color",g)]}),u("border-y",{width:g=>[a("border-block-style","var(--tw-border-style)"),a("border-block-width",g)],color:g=>[a("border-block-color",g)]}),u("border-s",{width:g=>[a("border-inline-start-style","var(--tw-border-style)"),a("border-inline-start-width",g)],color:g=>[a("border-inline-start-color",g)]}),u("border-e",{width:g=>[a("border-inline-end-style","var(--tw-border-style)"),a("border-inline-end-width",g)],color:g=>[a("border-inline-end-color",g)]}),u("border-t",{width:g=>[a("border-top-style","var(--tw-border-style)"),a("border-top-width",g)],color:g=>[a("border-top-color",g)]}),u("border-r",{width:g=>[a("border-right-style","var(--tw-border-style)"),a("border-right-width",g)],color:g=>[a("border-right-color",g)]}),u("border-b",{width:g=>[a("border-bottom-style","var(--tw-border-style)"),a("border-bottom-width",g)],color:g=>[a("border-bottom-color",g)]}),u("border-l",{width:g=>[a("border-left-style","var(--tw-border-style)"),a("border-left-width",g)],color:g=>[a("border-left-color",g)]}),i("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:g})=>N(g)?`${g}px`:null,handle:g=>[j([$("--tw-divide-x-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","divide-x-width"),o(),a("--tw-divide-x-reverse","0"),a("border-inline-style","var(--tw-border-style)"),a("border-inline-start-width",`calc(${g} * var(--tw-divide-x-reverse))`),a("border-inline-end-width",`calc(${g} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),i("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:g})=>N(g)?`${g}px`:null,handle:g=>[j([$("--tw-divide-y-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","divide-y-width"),o(),a("--tw-divide-y-reverse","0"),a("border-bottom-style","var(--tw-border-style)"),a("border-top-style","var(--tw-border-style)"),a("border-top-width",`calc(${g} * var(--tw-divide-y-reverse))`),a("border-bottom-width",`calc(${g} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),n("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),n("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>j([$("--tw-divide-x-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>j([$("--tw-divide-y-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-divide-y-reverse","1")])]);for(let g of["solid","dashed","dotted","double","none"])e(`divide-${g}`,[()=>U(":where(& > :not(:last-child))",[a("--tw-sort","divide-style"),a("--tw-border-style",g),a("border-style",g)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-center",[["background-position","center"]]),e("bg-top",[["background-position","top"]]),e("bg-right-top",[["background-position","right top"]]),e("bg-right",[["background-position","right"]]),e("bg-right-bottom",[["background-position","right bottom"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-left-bottom",[["background-position","left bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-left-top",[["background-position","left top"]]),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let g=function(T){let O="in oklab";if(T?.kind==="named")switch(T.value){case"longer":case"shorter":case"increasing":case"decreasing":O=`in oklch ${T.value} hue`;break;default:O=`in ${T.value}`}else T?.kind==="arbitrary"&&(O=T.value);return O},k=function({negative:T}){return O=>{if(!O.value)return;if(O.value.kind==="arbitrary"){if(O.modifier)return;let F=O.value.value;switch(O.value.dataType??z(F,["angle"])){case"angle":return F=T?`calc(${F} * -1)`:`${F}`,[a("--tw-gradient-position",`${F},`),a("background-image",`linear-gradient(var(--tw-gradient-stops,${F}))`)];default:return T?void 0:[a("--tw-gradient-position",`${F},`),a("background-image",`linear-gradient(var(--tw-gradient-stops,${F}))`)]}}let C=O.value.value;if(!T&&u.has(C))C=u.get(C);else if(N(C))C=T?`calc(${C}deg * -1)`:`${C}deg`;else return;let A=g(O.modifier);return[a("--tw-gradient-position",`${C} ${A},`),a("background-image","linear-gradient(var(--tw-gradient-stops))")]}},w=function({negative:T}){return O=>{if(O.value?.kind==="arbitrary"){if(O.modifier)return;let F=O.value.value;return[a("--tw-gradient-position",`${F},`),a("background-image",`conic-gradient(var(--tw-gradient-stops,${F}))`)]}let C=g(O.modifier);if(!O.value)return[a("--tw-gradient-position",`${C},`),a("background-image","conic-gradient(var(--tw-gradient-stops))")];let A=O.value.value;if(N(A))return A=T?`calc(${A} * -1)`:`${A}deg`,[a("--tw-gradient-position",`from ${A} ${C},`),a("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var S=g,V=k,R=w;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],u=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",k({negative:!0})),r.functional("bg-linear",k({negative:!1})),n("bg-linear",()=>[{values:[...u.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",w({negative:!0})),r.functional("bg-conic",w({negative:!1})),n("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",T=>{if(!T.value){let O=g(T.modifier);return[a("--tw-gradient-position",`${O},`),a("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(T.value.kind==="arbitrary"){if(T.modifier)return;let O=T.value.value;return[a("--tw-gradient-position",`${O},`),a("background-image",`radial-gradient(var(--tw-gradient-stops,${O}))`)]}}),n("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??z(u,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[a("background-position",u)];case"bg-size":case"length":case"size":return o.modifier?void 0:[a("background-size",u)];case"image":case"url":return o.modifier?void 0:[a("background-image",u)];default:return u=W(u,o.modifier,t),u===null?void 0:[a("background-color",u)]}}{let u=H(o,t,["--background-color","--color"]);if(u)return[a("background-color",u)]}{if(o.modifier)return;let u=t.resolve(o.value.value,["--background-image"]);if(u)return[a("background-image",u)]}}}),n("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let b=()=>j([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function v(o,u){r.functional(o,g=>{if(g.value){if(g.value.kind==="arbitrary"){let k=g.value.value;switch(g.value.dataType??z(k,["color","length","percentage"])){case"length":case"percentage":return g.modifier?void 0:u.position(k);default:return k=W(k,g.modifier,t),k===null?void 0:u.color(k)}}{let k=H(g,t,["--background-color","--color"]);if(k)return u.color(k)}{if(g.modifier)return;let k=t.resolve(g.value.value,["--gradient-color-stop-positions"]);if(k)return u.position(k);if(g.value.value[g.value.value.length-1]==="%"&&N(g.value.value.slice(0,-1)))return u.position(g.value.value)}}}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(g,k)=>`${k*5}`)},{values:Array.from({length:21},(g,k)=>`${k*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}v("from",{color:o=>[b(),a("--tw-sort","--tw-gradient-from"),a("--tw-gradient-from",o),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),a("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),v("via",{color:o=>[b(),a("--tw-sort","--tw-gradient-via"),a("--tw-gradient-via",o),a("--tw-gradient-via-stops","var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),a("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[b(),a("--tw-gradient-via-position",o)]}),v("to",{color:o=>[b(),a("--tw-sort","--tw-gradient-to"),a("--tw-gradient-to",o),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),a("--tw-gradient-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let g=W(o.value.value,o.modifier,t);return g===null?void 0:[a("fill",g)]}let u=H(o,t,["--fill","--color"]);if(u)return[a("fill",u)]}),n("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??z(u,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[a("stroke-width",u)];default:return u=W(o.value.value,o.modifier,t),u===null?void 0:[a("stroke",u)]}}{let u=H(o,t,["--stroke","--color"]);if(u)return[a("stroke",u)]}{let u=t.resolve(o.value.value,["--stroke-width"]);if(u)return[a("stroke-width",u)];if(N(o.value.value))return[a("stroke-width",o.value.value)]}}}),n("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-bottom",[["object-position","bottom"]]),e("object-center",[["object-position","center"]]),e("object-left",[["object-position","left"]]),e("object-left-bottom",[["object-position","left bottom"]]),e("object-left-top",[["object-position","left top"]]),e("object-right",[["object-position","right"]]),e("object-right-bottom",[["object-position","right bottom"]]),e("object-right-top",[["object-position","right top"]]),e("object-top",[["object-position","top"]]),i("object",{themeKeys:["--object-position"],handle:o=>[a("object-position",o)]});for(let[o,u]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])l(o,["--padding","--spacing"],g=>[a(u,g)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),l("indent",["--text-indent","--spacing"],o=>[a("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),i("align",{themeKeys:[],handle:o=>[a("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??z(u,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[a("font-family",u)];default:return[j([$("--tw-font-weight")]),a("--tw-font-weight",u),a("font-weight",u)]}}{let u=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(u){let[g,k={}]=u;return[a("font-family",g),a("font-feature-settings",k["--font-feature-settings"]),a("font-variation-settings",k["--font-variation-settings"])]}}{let u=t.resolve(o.value.value,["--font-weight"]);if(u)return[j([$("--tw-font-weight")]),a("--tw-font-weight",u),a("font-weight",u)]}}}),n("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),i("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let u=Number(o.slice(0,-1));return!N(u)||Number.isNaN(u)||u<50||u>200?null:o},handle:o=>[a("font-stretch",o)]}),n("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[U("&::placeholder",[a("--tw-sort","placeholder-color"),a("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??z(u,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[a("text-decoration-thickness",u)];default:return u=W(u,o.modifier,t),u===null?void 0:[a("text-decoration-color",u)]}}{let u=t.resolve(o.value.value,["--text-decoration-thickness"]);if(u)return o.modifier?void 0:[a("text-decoration-thickness",u)];if(N(o.value.value))return o.modifier?void 0:[a("text-decoration-thickness",`${o.value.value}px`)]}{let u=H(o,t,["--text-decoration-color","--color"]);if(u)return[a("text-decoration-color",u)]}}}),n("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),i("animate",{themeKeys:["--animate"],handle:o=>[a("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),u=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),g=()=>j([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia")]),k=()=>j([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);r.functional("filter",w=>{if(!w.modifier){if(w.value===null)return[g(),a("filter",o)];if(w.value.kind==="arbitrary")return[a("filter",w.value.value)];switch(w.value.value){case"none":return[a("filter","none")]}}}),r.functional("backdrop-filter",w=>{if(!w.modifier){if(w.value===null)return[k(),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)];if(w.value.kind==="arbitrary")return[a("-webkit-backdrop-filter",w.value.value),a("backdrop-filter",w.value.value)];switch(w.value.value){case"none":return[a("-webkit-backdrop-filter","none"),a("backdrop-filter","none")]}}}),i("blur",{themeKeys:["--blur"],handle:w=>[g(),a("--tw-blur",`blur(${w})`),a("filter",o)]}),e("blur-none",[g,["--tw-blur"," "],["filter",o]]),i("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:w=>[k(),a("--tw-backdrop-blur",`blur(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),e("backdrop-blur-none",[k,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",u],["backdrop-filter",u]]),i("brightness",{themeKeys:["--brightness"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[g(),a("--tw-brightness",`brightness(${w})`),a("filter",o)]}),i("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),a("--tw-backdrop-brightness",`brightness(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),n("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),i("contrast",{themeKeys:["--contrast"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[g(),a("--tw-contrast",`contrast(${w})`),a("filter",o)]}),i("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),a("--tw-backdrop-contrast",`contrast(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),n("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),i("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[g(),a("--tw-grayscale",`grayscale(${w})`),a("filter",o)]}),i("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),a("--tw-backdrop-grayscale",`grayscale(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),n("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),i("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:w})=>N(w)?`${w}deg`:null,handle:w=>[g(),a("--tw-hue-rotate",`hue-rotate(${w})`),a("filter",o)]}),i("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:w})=>N(w)?`${w}deg`:null,handle:w=>[k(),a("--tw-backdrop-hue-rotate",`hue-rotate(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),n("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),i("invert",{themeKeys:["--invert"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[g(),a("--tw-invert",`invert(${w})`),a("filter",o)]}),i("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),a("--tw-backdrop-invert",`invert(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),n("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),i("saturate",{themeKeys:["--saturate"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[g(),a("--tw-saturate",`saturate(${w})`),a("filter",o)]}),i("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),a("--tw-backdrop-saturate",`saturate(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),n("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),i("sepia",{themeKeys:["--sepia"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[g(),a("--tw-sepia",`sepia(${w})`),a("filter",o)]}),i("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),a("--tw-backdrop-sepia",`sepia(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),n("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[g,["--tw-drop-shadow"," "],["filter",o]]),i("drop-shadow",{themeKeys:["--drop-shadow"],handle:w=>[g(),a("--tw-drop-shadow",_(w,",").map(T=>`drop-shadow(${T})`).join(" ")),a("filter",o)]}),i("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:w})=>Ue(w)?`${w}%`:null,handle:w=>[k(),a("--tw-backdrop-opacity",`opacity(${w})`),a("-webkit-backdrop-filter",u),a("backdrop-filter",u)]}),n("backdrop-opacity",()=>[{values:Array.from({length:21},(w,T)=>`${T*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,u=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",u]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",u]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",u]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",u]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",u]]),i("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:g=>[a("transition-property",g),a("transition-timing-function",o),a("transition-duration",u)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),i("delay",{handleBareValue:({value:g})=>N(g)?`${g}ms`:null,themeKeys:["--transition-delay"],handle:g=>[a("transition-delay",g)]});{let g=()=>j([$("--tw-duration")]);e("duration-initial",[g,["--tw-duration","initial"]]),r.functional("duration",k=>{if(k.modifier||!k.value)return;let w=null;if(k.value.kind==="arbitrary"?w=k.value.value:(w=t.resolve(k.value.fraction??k.value.value,["--transition-duration"]),w===null&&N(k.value.value)&&(w=`${k.value.value}ms`)),w!==null)return[g(),a("--tw-duration",w),a("transition-duration",w)]})}n("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),n("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>j([$("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),i("ease",{themeKeys:["--ease"],handle:u=>[o(),a("--tw-ease",u),a("transition-timing-function",u)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),i("will-change",{themeKeys:[],handle:o=>[a("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),i("content",{themeKeys:[],handle:o=>[j([$("--tw-content",'""')]),a("--tw-content",o),a("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",u=()=>j([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[u,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[u,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[u,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[u,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[u,["--tw-contain-style","style"],["contain",o]]),i("contain",{themeKeys:[],handle:g=>[a("contain",g)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>j([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),l("leading",["--leading","--spacing"],o=>[j([$("--tw-leading")]),a("--tw-leading",o),a("line-height",o)]),i("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[j([$("--tw-tracking")]),a("--tw-tracking",o),a("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",u=()=>j([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[u,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[u,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[u,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[u,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[u,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[u,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[u,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[u,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>j([$("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[a("outline-style","none"),P("@media","(forced-colors: active)",[a("outline","2px solid transparent"),a("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",u=>{if(u.value===null)return u.modifier?void 0:[o(),a("outline-style","var(--tw-outline-style)"),a("outline-width","1px")];if(u.value.kind==="arbitrary"){let g=u.value.value;switch(u.value.dataType??z(g,["color","length","number","percentage"])){case"length":case"number":case"percentage":return u.modifier?void 0:[o(),a("outline-style","var(--tw-outline-style)"),a("outline-width",g)];default:return g=W(g,u.modifier,t),g===null?void 0:[a("outline-color",g)]}}{let g=H(u,t,["--outline-color","--color"]);if(g)return[a("outline-color",g)]}{if(u.modifier)return;let g=t.resolve(u.value.value,["--outline-width"]);if(g)return[o(),a("outline-style","var(--tw-outline-style)"),a("outline-width",g)];if(N(u.value.value))return[o(),a("outline-style","var(--tw-outline-style)"),a("outline-width",`${u.value.value}px`)]}}),n("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(u,g)=>`${g*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),i("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:u})=>N(u)?`${u}px`:null,handle:u=>[a("outline-offset",u)]}),n("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}i("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>Ue(o)?`${o}%`:null,handle:o=>[a("opacity",o)]}),n("opacity",()=>[{values:Array.from({length:21},(o,u)=>`${u*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),i("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>N(o)?`${o}px`:null,handle:o=>[a("text-underline-offset",o)]}),n("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let u=o.value.value;switch(o.value.dataType??z(u,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let k=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!k&&fe(o.modifier.value)){let w=t.resolve(null,["--spacing"]);if(!w)return null;k=`calc(${w} * ${o.modifier.value})`}return!k&&o.modifier.value==="none"&&(k="1"),k?[a("font-size",u),a("line-height",k)]:null}return[a("font-size",u)]}default:return u=W(u,o.modifier,t),u===null?void 0:[a("color",u)]}}{let u=H(o,t,["--text-color","--color"]);if(u)return[a("color",u)]}{let u=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(u){let[g,k={}]=Array.isArray(u)?u:[u];if(o.modifier){let w=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!w&&fe(o.modifier.value)){let O=t.resolve(null,["--spacing"]);if(!O)return null;w=`calc(${O} * ${o.modifier.value})`}if(!w&&o.modifier.value==="none"&&(w="1"),!w)return null;let T=[a("font-size",g)];return w&&T.push(a("line-height",w)),T}return typeof k=="string"?[a("font-size",g),a("line-height",k)]:[a("font-size",g),a("line-height",k["--line-height"]?`var(--tw-leading, ${k["--line-height"]})`:void 0),a("letter-spacing",k["--letter-spacing"]?`var(--tw-tracking, ${k["--letter-spacing"]})`:void 0),a("font-weight",k["--font-weight"]?`var(--tw-font-weight, ${k["--font-weight"]})`:void 0)]}}}}),n("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let w=function(C){return`var(--tw-ring-inset,) 0 0 0 calc(${C} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${k})`},T=function(C){return`inset 0 0 0 ${C} var(--tw-inset-ring-color, currentColor)`};var E=w,K=T;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),u="0 0 #0000",g=()=>j([$("--tw-shadow",u),$("--tw-shadow-color"),$("--tw-inset-shadow",u),$("--tw-inset-shadow-color"),$("--tw-ring-color"),$("--tw-ring-shadow",u),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",u),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",u)]);e("shadow-initial",[g,["--tw-shadow-color","initial"]]),r.functional("shadow",C=>{if(!C.value){let A=t.get(["--shadow"]);return A===null?void 0:[g(),a("--tw-shadow",ae(A,F=>`var(--tw-shadow-color, ${F})`)),a("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[g(),a("--tw-shadow-color",A)];default:return[g(),a("--tw-shadow",ae(A,Ve=>`var(--tw-shadow-color, ${Ve})`)),a("box-shadow",o)]}}switch(C.value.value){case"none":return C.modifier?void 0:[g(),a("--tw-shadow",u),a("box-shadow",o)]}{let A=t.get([`--shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[g(),a("--tw-shadow",ae(A,F=>`var(--tw-shadow-color, ${F})`)),a("box-shadow",o)]}{let A=H(C,t,["--box-shadow-color","--color"]);if(A)return[g(),a("--tw-shadow-color",A)]}}),n("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),e("inset-shadow-initial",[g,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",C=>{if(!C.value){let A=t.get(["--inset-shadow"]);return A===null?void 0:[g(),a("--tw-inset-shadow",ae(A,F=>`var(--tw-inset-shadow-color, ${F})`)),a("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[g(),a("--tw-inset-shadow-color",A)];default:return[g(),a("--tw-inset-shadow",`inset ${ae(A,Ve=>`var(--tw-inset-shadow-color, ${Ve})`)}`),a("box-shadow",o)]}}switch(C.value.value){case"none":return C.modifier?void 0:[g(),a("--tw-inset-shadow",u),a("box-shadow",o)]}{let A=t.get([`--inset-shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[g(),a("--tw-inset-shadow",ae(A,F=>`var(--tw-inset-shadow-color, ${F})`)),a("box-shadow",o)]}{let A=H(C,t,["--box-shadow-color","--color"]);if(A)return[g(),a("--tw-inset-shadow-color",A)]}}),n("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),e("ring-inset",[g,["--tw-ring-inset","inset"]]);let k=t.get(["--default-ring-color"])??"currentColor";r.functional("ring",C=>{if(!C.value){if(C.modifier)return;let A=t.get(["--default-ring-width"])??"1px";return[g(),a("--tw-ring-shadow",w(A)),a("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[g(),a("--tw-ring-shadow",w(A)),a("box-shadow",o)];default:return A=W(A,C.modifier,t),A===null?void 0:[a("--tw-ring-color",A)]}}{let A=H(C,t,["--ring-color","--color"]);if(A)return[a("--tw-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&N(C.value.value)&&(A=`${C.value.value}px`),A)return[g(),a("--tw-ring-shadow",w(A)),a("box-shadow",o)]}}),n("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",C=>{if(!C.value)return C.modifier?void 0:[g(),a("--tw-inset-ring-shadow",T("1px")),a("box-shadow",o)];if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[g(),a("--tw-inset-ring-shadow",T(A)),a("box-shadow",o)];default:return A=W(A,C.modifier,t),A===null?void 0:[a("--tw-inset-ring-color",A)]}}{let A=H(C,t,["--ring-color","--color"]);if(A)return[a("--tw-inset-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&N(C.value.value)&&(A=`${C.value.value}px`),A)return[g(),a("--tw-inset-ring-shadow",T(A)),a("box-shadow",o)]}}),n("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let O="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",C=>{if(C.value){if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[a("--tw-ring-offset-width",A),a("--tw-ring-offset-shadow",O)];default:return A=W(A,C.modifier,t),A===null?void 0:[a("--tw-ring-offset-color",A)]}}{let A=t.resolve(C.value.value,["--ring-offset-width"]);if(A)return C.modifier?void 0:[a("--tw-ring-offset-width",A),a("--tw-ring-offset-shadow",O)];if(N(C.value.value))return C.modifier?void 0:[a("--tw-ring-offset-width",`${C.value.value}px`),a("--tw-ring-offset-shadow",O)]}{let A=H(C,t,["--ring-offset-color","--color"]);if(A)return[a("--tw-ring-offset-color",A)]}}})}return n("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,u)=>`${u*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let u=null;if(o.value===null?u="inline-size":o.value.kind==="arbitrary"?u=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(u="normal"),u!==null)return o.modifier?[a("container-type",u),a("container-name",o.modifier.value)]:[a("container-type",u)]}),n("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}function Wt(t){let r=t.params;return Kn.test(r)?n=>{let e=new Set,i=new Set;D(t.nodes,s=>{if(s.kind!=="declaration"||!s.value||!s.value.includes("--value(")&&!s.value.includes("--modifier("))return;let l=M(s.value);ce(l,f=>{if(f.kind!=="function"||f.value!=="--value"&&f.value!=="--modifier")return;let d=_(q(f.nodes),",");for(let[c,p]of d.entries())p=p.replace(/\\\*/g,"*"),p=p.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),p=p.replace(/\s+/g,""),p=p.replace(/(-\*){2,}/g,"-*"),p[0]==="-"&&p[1]==="-"&&!p.includes("-*")&&(p+="-*"),d[c]=p;f.nodes=M(d.join(","));for(let c of f.nodes)if(c.kind==="word"&&c.value[0]==="-"&&c.value[1]==="-"){let p=c.value.replace(/-\*.*$/g,"");f.value==="--value"?e.add(p):f.value==="--modifier"&&i.add(p)}}),s.value=q(l)}),n.utilities.functional(r.slice(0,-2),s=>{let l=structuredClone(t),f=s.value,d=s.modifier;if(f===null)return;let c=!1,p=!1,m=!1,h=!1,y=new Map,b=!1;if(D([l],(v,{parent:x,replaceWith:S})=>{if(x?.kind!=="rule"&&x?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let V=M(v.value);(ce(V,(E,{replaceWith:K})=>{if(E.kind==="function"){if(E.value==="--value"){c=!0;let o=Lt(f,E,n);return o?(p=!0,o.ratio?b=!0:y.set(v,x),K(o.nodes),1):(c||=!1,S([]),2)}else if(E.value==="--modifier"){if(d===null)return S([]),1;m=!0;let o=Lt(d,E,n);return o?(h=!0,K(o.nodes),1):(m||=!1,S([]),2)}}})??0)===0&&(v.value=q(V))}),c&&!p||m&&!h||b&&h||d&&!b&&!h)return null;if(b)for(let[v,x]of y){let S=x.nodes.indexOf(v);S!==-1&&x.nodes.splice(S,1)}return l.nodes}),n.utilities.suggest(r.slice(0,-2),()=>[{values:n.theme.keysInNamespaces(e).map(s=>s.replaceAll("_",".")),modifiers:n.theme.keysInNamespaces(i).map(s=>s.replaceAll("_","."))}])}:On.test(r)?n=>{n.utilities.static(r,()=>structuredClone(t.nodes))}:null}function Lt(t,r,n){for(let e of r.nodes)if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let i=e.value;if(i.endsWith("-*")){i=i.slice(0,-2);let s=n.theme.resolve(t.value,[i]);if(s)return{nodes:M(s)}}else{let s=i.split("-*");if(s.length<=1)continue;let l=[s.shift()],f=n.theme.resolveWith(t.value,l,s);if(f){let[,d={}]=f;{let c=d[s.pop()];if(c)return{nodes:M(c)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(e.value!=="number"&&e.value!=="integer"&&e.value!=="ratio"&&e.value!=="percentage")continue;let i=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!i)continue;let s=z(i,[e.value]);if(s===null)continue;if(s==="ratio"){let[l,f]=_(i,"/");if(!N(l)||!N(f))continue}else{if(s==="number"&&!fe(i))continue;if(s==="percentage"&&!N(i.slice(0,-1)))continue}return{nodes:M(i),ratio:s==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let i=e.value.slice(1,-1);if(i==="*")return{nodes:M(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==i)continue;if("dataType"in t&&t.dataType)return{nodes:M(t.value)};if(z(t.value,[i])!==null)return{nodes:M(t.value)}}}var st={"--alpha":_n,"--spacing":Pn,"--theme":Dn,theme:Ht};function _n(t,r,...n){let[e,i]=_(r,"/").map(s=>s.trim());if(!e||!i)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${e||"var(--my-color)"} / ${i||"50%"})\``);if(n.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${e||"var(--my-color)"} / ${i||"50%"})\``);return J(e,i)}function Pn(t,r,...n){if(!r)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(n.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${n.length+1}.`);let e=t.theme.resolve(null,["--spacing"]);if(!e)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${e} * ${r})`}function Dn(t,r,...n){if(!r.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");return Ht(t,r,...n)}function Ht(t,r,...n){r=jn(r);let e=t.resolveThemeValue(r);if(!e&&n.length>0)return n.join(", ");if(!e)throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return e}var Bt=new RegExp(Object.keys(st).map(t=>`${t}\\(`).join("|"));function de(t,r){let n=0;return D(t,e=>{if(e.kind==="declaration"&&e.value&&Bt.test(e.value)){n|=8,e.value=qt(e.value,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&Bt.test(e.params)&&(n|=8,e.params=qt(e.params,r))}),n}function qt(t,r){let n=M(t);return ce(n,(e,{replaceWith:i})=>{if(e.kind==="function"&&e.value in st){let s=_(q(e.nodes).trim(),",").map(f=>f.trim()),l=st[e.value](r,...s);return i(M(l))}}),q(n)}function jn(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",n=t[0];for(let e=1;e<t.length-1;e++){let i=t[e],s=t[e+1];i==="\\"&&(s===n||s==="\\")?(r+=s,e++):r+=i}return r}function Fe(t,r){let n=t.length,e=r.length,i=n<e?n:e;for(let s=0;s<i;s++){let l=t.charCodeAt(s),f=r.charCodeAt(s);if(l!==f){if(l>=48&&l<=57&&f>=48&&f<=57){let d=s,c=s+1,p=s,m=s+1;for(l=t.charCodeAt(c);l>=48&&l<=57;)l=t.charCodeAt(++c);for(f=r.charCodeAt(m);f>=48&&f<=57;)f=r.charCodeAt(++m);let h=t.slice(d,c),y=r.slice(p,m);return Number(h)-Number(y)||(h<y?-1:1)}return l-f}}return t.length-r.length}function Gt(t){let r=[];for(let n of t.utilities.keys("static"))r.push([n,{modifiers:[]}]);for(let n of t.utilities.keys("functional")){let e=t.utilities.getCompletions(n);for(let i of e)for(let s of i.values){let l=s===null?n:`${n}-${s}`;r.push([l,{modifiers:i.modifiers}]),i.supportsNegative&&r.push([`-${l}`,{modifiers:i.modifiers}])}}return r.sort((n,e)=>Fe(n[0],e[0])),r}function Jt(t){let r=[];for(let[e,i]of t.variants.entries()){let f=function({value:d,modifier:c}={}){let p=e;d&&(p+=s?`-${d}`:d),c&&(p+=`/${c}`);let m=t.parseVariant(p);if(!m)return[];let h=U(".__placeholder__",[]);if(ge(h,m,t.variants)===null)return[];let y=[];return _e(h.nodes,(b,{path:v})=>{if(b.kind!=="rule"&&b.kind!=="at-rule"||b.nodes.length>0)return;v.sort((V,R)=>{let E=V.kind==="at-rule",K=R.kind==="at-rule";return E&&!K?-1:!E&&K?1:0});let x=v.flatMap(V=>V.kind==="rule"?V.selector==="&"?[]:[V.selector]:V.kind==="at-rule"?[`${V.name} ${V.params}`]:[]),S="";for(let V=x.length-1;V>=0;V--)S=S===""?x[V]:`${x[V]} { ${S} }`;y.push(S)}),y};var n=f;if(i.kind==="arbitrary")continue;let s=e!=="@",l=t.variants.getCompletions(e);switch(i.kind){case"static":{r.push({name:e,values:l,isArbitrary:!1,hasDash:s,selectors:f});break}case"functional":{r.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:f});break}case"compound":{r.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:f});break}}}return r}function Yt(t,r){let{astNodes:n,nodeSorting:e}=ee(Array.from(r),t),i=new Map(r.map(l=>[l,null])),s=0n;for(let l of n){let f=e.get(l)?.candidate;f&&i.set(f,i.get(f)??s++)}return r.map(l=>[l,i.get(l)??null])}var ze=/^@?[a-zA-Z0-9_-]*$/;var ut=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"static",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}fromAst(r,n){let e=[];D(n,i=>{i.kind==="rule"?e.push(i.selector):i.kind==="at-rule"&&i.name!=="@slot"&&e.push(`${i.name} ${i.params}`)}),this.static(r,i=>{let s=structuredClone(n);ct(s,i.nodes),i.nodes=s},{compounds:se(e)})}functional(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"functional",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}compound(r,n,e,{compounds:i,order:s}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:n,compounds:i??2,order:s})}group(r,n){this.groupOrder=this.nextOrder(),n&&this.compareFns.set(this.groupOrder,n),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,n){let e=this.variants.get(r),i=typeof n=="string"?this.variants.get(n):n.kind==="arbitrary"?{compounds:se([n.selector])}:this.variants.get(n.root);return!(!e||!i||e.kind!=="compound"||i.compounds===0||e.compoundsWith===0||!(e.compoundsWith&i.compounds))}suggest(r,n){this.completions.set(r,n)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,n){if(r===n)return 0;if(r===null)return-1;if(n===null)return 1;if(r.kind==="arbitrary"&&n.kind==="arbitrary")return r.selector<n.selector?-1:1;if(r.kind==="arbitrary")return 1;if(n.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,i=this.variants.get(n.root).order,s=e-i;if(s!==0)return s;if(r.kind==="compound"&&n.kind==="compound"){let c=this.compare(r.variant,n.variant);return c!==0?c:r.modifier&&n.modifier?r.modifier.value<n.modifier.value?-1:1:r.modifier?1:n.modifier?-1:0}let l=this.compareFns.get(e);if(l!==void 0)return l(r,n);if(r.root!==n.root)return r.root<n.root?-1:1;let f=r.value,d=n.value;return f===null?-1:d===null||f.kind==="arbitrary"&&d.kind!=="arbitrary"?1:f.kind!=="arbitrary"&&d.kind==="arbitrary"||f.value<d.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:n,applyFn:e,compounds:i,compoundsWith:s,order:l}){let f=this.variants.get(r);f?Object.assign(f,{kind:n,applyFn:e,compounds:i}):(l===void 0&&(this.lastOrder=this.nextOrder(),l=this.lastOrder),this.variants.set(r,{kind:n,applyFn:e,order:l,compoundsWith:s,compounds:i}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function se(t){let r=0;for(let n of t){if(n[0]==="@"){if(!n.startsWith("@media")&&!n.startsWith("@supports")&&!n.startsWith("@container"))return 0;r|=1;continue}if(n.includes("::"))return 0;r|=2}return r}function Qt(t){let r=new ut;function n(c,p,{compounds:m}={}){m=m??se(p),r.static(c,h=>{h.nodes=p.map(y=>L(y,h.nodes))},{compounds:m})}n("*",[":is(& > *)"],{compounds:0}),n("**",[":is(& *)"],{compounds:0});function e(c,p){return p.map(m=>{m=m.trim();let h=_(m," ");return h[0]==="not"?h.slice(1).join(" "):c==="@container"?h[0][0]==="("?`not ${m}`:h[1]==="not"?`${h[0]} ${h.slice(2).join(" ")}`:`${h[0]} not ${h.slice(1).join(" ")}`:`not ${m}`})}let i=["@media","@supports","@container"];function s(c){for(let p of i){if(p!==c.name)continue;let m=_(c.params,",");return m.length>1?null:(m=e(c.name,m),P(c.name,m.join(", ")))}return null}function l(c){return c.includes("::")?null:`&:not(${_(c,",").map(m=>(m.startsWith("&:is(")&&m.endsWith(")")&&(m=m.slice(5,-1)),m=m.replaceAll("&","*"),m)).join(", ")})`}r.compound("not",3,(c,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative||p.modifier)return null;let m=!1;if(D([c],(h,{path:y})=>{if(h.kind!=="rule"&&h.kind!=="at-rule")return 0;if(h.nodes.length>0)return 0;let b=[],v=[];for(let S of y)S.kind==="at-rule"?b.push(S):S.kind==="rule"&&v.push(S);if(b.length>1)return 2;if(v.length>1)return 2;let x=[];for(let S of v){let V=l(S.selector);if(!V)return m=!1,2;x.push(U(V,[]))}for(let S of b){let V=s(S);if(!V)return m=!1,2;x.push(V)}return Object.assign(c,U("&",x)),m=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!m)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("not",c))),r.compound("group",2,(c,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative)return null;let m=p.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${p.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,h=!1;if(D([c],(y,{path:b})=>{if(y.kind!=="rule")return 0;for(let x of b.slice(0,-1))if(x.kind==="rule")return h=!1,2;let v=y.selector.replaceAll("&",m);_(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} *)`,h=!0}),!h)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("group",c))),r.compound("peer",2,(c,p)=>{if(p.variant.kind==="arbitrary"&&p.variant.relative)return null;let m=p.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${p.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,h=!1;if(D([c],(y,{path:b})=>{if(y.kind!=="rule")return 0;for(let x of b.slice(0,-1))if(x.kind==="rule")return h=!1,2;let v=y.selector.replaceAll("&",m);_(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} ~ *)`,h=!0}),!h)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("peer",c))),n("first-letter",["&::first-letter"]),n("first-line",["&::first-line"]),n("marker",["& *::marker","&::marker"]),n("selection",["& *::selection","&::selection"]),n("file",["&::file-selector-button"]),n("placeholder",["&::placeholder"]),n("backdrop",["&::backdrop"]);{let c=function(){return j([P("@property","--tw-content",[a("syntax",'"*"'),a("initial-value",'""'),a("inherits","false")])])};var f=c;r.static("before",p=>{p.nodes=[U("&::before",[c(),a("content","var(--tw-content)"),...p.nodes])]},{compounds:0}),r.static("after",p=>{p.nodes=[U("&::after",[c(),a("content","var(--tw-content)"),...p.nodes])]},{compounds:0})}n("first",["&:first-child"]),n("last",["&:last-child"]),n("only",["&:only-child"]),n("odd",["&:nth-child(odd)"]),n("even",["&:nth-child(even)"]),n("first-of-type",["&:first-of-type"]),n("last-of-type",["&:last-of-type"]),n("only-of-type",["&:only-of-type"]),n("visited",["&:visited"]),n("target",["&:target"]),n("open",["&:is([open], :popover-open, :open)"]),n("default",["&:default"]),n("checked",["&:checked"]),n("indeterminate",["&:indeterminate"]),n("placeholder-shown",["&:placeholder-shown"]),n("autofill",["&:autofill"]),n("optional",["&:optional"]),n("required",["&:required"]),n("valid",["&:valid"]),n("invalid",["&:invalid"]),n("in-range",["&:in-range"]),n("out-of-range",["&:out-of-range"]),n("read-only",["&:read-only"]),n("empty",["&:empty"]),n("focus-within",["&:focus-within"]),r.static("hover",c=>{c.nodes=[U("&:hover",[P("@media","(hover: hover)",c.nodes)])]}),n("focus",["&:focus"]),n("focus-visible",["&:focus-visible"]),n("active",["&:active"]),n("enabled",["&:enabled"]),n("disabled",["&:disabled"]),n("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(c,p)=>{if(p.modifier)return null;let m=!1;if(D([c],(h,{path:y})=>{if(h.kind!=="rule")return 0;for(let b of y.slice(0,-1))if(b.kind==="rule")return m=!1,2;h.selector=`:where(${h.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("in",c))),r.compound("has",2,(c,p)=>{if(p.modifier)return null;let m=!1;if(D([c],(h,{path:y})=>{if(h.kind!=="rule")return 0;for(let b of y.slice(0,-1))if(b.kind==="rule")return m=!1,2;h.selector=`&:has(${h.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("has",c))),r.functional("aria",(c,p)=>{if(!p.value||p.modifier)return null;p.value.kind==="arbitrary"?c.nodes=[U(`&[aria-${Zt(p.value.value)}]`,c.nodes)]:c.nodes=[U(`&[aria-${p.value.value}="true"]`,c.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(c,p)=>{if(!p.value||p.modifier)return null;c.nodes=[U(`&[data-${Zt(p.value.value)}]`,c.nodes)]}),r.functional("nth",(c,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!N(p.value.value))return null;c.nodes=[U(`&:nth-child(${p.value.value})`,c.nodes)]}),r.functional("nth-last",(c,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!N(p.value.value))return null;c.nodes=[U(`&:nth-last-child(${p.value.value})`,c.nodes)]}),r.functional("nth-of-type",(c,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!N(p.value.value))return null;c.nodes=[U(`&:nth-of-type(${p.value.value})`,c.nodes)]}),r.functional("nth-last-of-type",(c,p)=>{if(!p.value||p.modifier||p.value.kind==="named"&&!N(p.value.value))return null;c.nodes=[U(`&:nth-last-of-type(${p.value.value})`,c.nodes)]}),r.functional("supports",(c,p)=>{if(!p.value||p.modifier)return null;let m=p.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let h=m.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[P("@supports",h,c.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),c.nodes=[P("@supports",m,c.nodes)]},{compounds:1}),n("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),n("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),n("contrast-more",["@media (prefers-contrast: more)"]),n("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(p,m,h,y){if(p===m)return 0;let b=y.get(p);if(b===null)return h==="asc"?-1:1;let v=y.get(m);return v===null?h==="asc"?1:-1:le(b,v,h)};var d=c;{let p=t.namespace("--breakpoint"),m=new I(h=>{switch(h.kind){case"static":return t.resolveValue(h.root,["--breakpoint"])??null;case"functional":{if(!h.value||h.modifier)return null;let y=null;return h.value.kind==="arbitrary"?y=h.value.value:h.value.kind==="named"&&(y=t.resolveValue(h.value.value,["--breakpoint"])),!y||y.includes("var(")?null:y}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(h,y)=>{if(y.modifier)return null;let b=m.get(y);if(b===null)return null;h.nodes=[P("@media",`(width < ${b})`,h.nodes)]},{compounds:1})},(h,y)=>c(h,y,"desc",m)),r.suggest("max",()=>Array.from(p.keys()).filter(h=>h!==null)),r.group(()=>{for(let[h,y]of t.namespace("--breakpoint"))h!==null&&r.static(h,b=>{b.nodes=[P("@media",`(width >= ${y})`,b.nodes)]},{compounds:1});r.functional("min",(h,y)=>{if(y.modifier)return null;let b=m.get(y);if(b===null)return null;h.nodes=[P("@media",`(width >= ${b})`,h.nodes)]},{compounds:1})},(h,y)=>c(h,y,"asc",m)),r.suggest("min",()=>Array.from(p.keys()).filter(h=>h!==null))}{let p=t.namespace("--container"),m=new I(h=>{switch(h.kind){case"functional":{if(h.value===null)return null;let y=null;return h.value.kind==="arbitrary"?y=h.value.value:h.value.kind==="named"&&(y=t.resolveValue(h.value.value,["--container"])),!y||y.includes("var(")?null:y}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(h,y)=>{let b=m.get(y);if(b===null)return null;h.nodes=[P("@container",y.modifier?`${y.modifier.value} (width < ${b})`:`(width < ${b})`,h.nodes)]},{compounds:1})},(h,y)=>c(h,y,"desc",m)),r.suggest("@max",()=>Array.from(p.keys()).filter(h=>h!==null)),r.group(()=>{r.functional("@",(h,y)=>{let b=m.get(y);if(b===null)return null;h.nodes=[P("@container",y.modifier?`${y.modifier.value} (width >= ${b})`:`(width >= ${b})`,h.nodes)]},{compounds:1}),r.functional("@min",(h,y)=>{let b=m.get(y);if(b===null)return null;h.nodes=[P("@container",y.modifier?`${y.modifier.value} (width >= ${b})`:`(width >= ${b})`,h.nodes)]},{compounds:1})},(h,y)=>c(h,y,"asc",m)),r.suggest("@min",()=>Array.from(p.keys()).filter(h=>h!==null)),r.suggest("@",()=>Array.from(p.keys()).filter(h=>h!==null))}}return n("portrait",["@media (orientation: portrait)"]),n("landscape",["@media (orientation: landscape)"]),n("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),n("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),n("dark",["@media (prefers-color-scheme: dark)"]),n("starting",["@starting-style"]),n("print",["@media print"]),n("forced-colors",["@media (forced-colors: active)"]),r}function Zt(t){if(t.includes("=")){let[r,...n]=_(t,"="),e=n.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let i=e[e.length-1];if(e[e.length-2]===" "&&(i==="i"||i==="I"||i==="s"||i==="S"))return`${r}="${e.slice(0,-2)}" ${i}`}return`${r}="${e}"`}return t}function ct(t,r){D(t,(n,{replaceWith:e})=>{if(n.kind==="at-rule"&&n.name==="@slot")e(r);else if(n.kind==="at-rule"&&(n.name==="@keyframes"||n.name==="@property"))return Object.assign(n,j([P(n.name,n.params,n.nodes)])),1})}function Xt(t){let r=Mt(t),n=Qt(t),e=new I(f=>Dt(f,l)),i=new I(f=>Array.from(Pt(f,l))),s=new I(f=>er(f,l)),l={theme:t,utilities:r,variants:n,invalidCandidates:new Set,important:!1,candidatesToCss(f){let d=[];for(let c of f){let p=!1,{astNodes:m}=ee([c],this,{onInvalidCandidate(){p=!0}});m=oe(m),m.length===0||p?d.push(null):d.push(G(m))}return d},getClassOrder(f){return Yt(this,f)},getClassList(){return Gt(this)},getVariants(){return Jt(this)},parseCandidate(f){return i.get(f)},parseVariant(f){return e.get(f)},compileAstNodes(f){return s.get(f)},getVariantOrder(){let f=Array.from(e.values());f.sort((m,h)=>this.variants.compare(m,h));let d=new Map,c,p=0;for(let m of f)m!==null&&(c!==void 0&&this.variants.compare(c,m)!==0&&p++,d.set(m,p),c=m);return d},resolveThemeValue(f){let d=f.lastIndexOf("/"),c=null;d!==-1&&(c=f.slice(d+1).trim(),f=f.slice(0,d).trim());let p=t.get([f])??void 0;return c&&p?J(p,c):p}};return l}var ft=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function me(t){if(arguments.length==0)throw new TypeError("`CSS.escape` requires an argument.");var r=String(t),n=r.length,e=-1,i,s="",l=r.charCodeAt(0);if(n==1&&l==45)return"\\"+r;for(;++e<n;){if(i=r.charCodeAt(e),i==0){s+="\uFFFD";continue}if(i>=1&&i<=31||i==127||e==0&&i>=48&&i<=57||e==1&&i>=48&&i<=57&&l==45){s+="\\"+i.toString(16)+" ";continue}if(i>=128||i==45||i==95||i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122){s+=r.charAt(e);continue}s+="\\"+r.charAt(e)}return s}function tr(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}function ee(t,r,{onInvalidCandidate:n}={}){let e=new Map,i=[],s=new Map;for(let f of t){if(r.invalidCandidates.has(f)){n?.(f);continue}let d=r.parseCandidate(f);if(d.length===0){n?.(f);continue}s.set(f,d)}let l=r.getVariantOrder();for(let[f,d]of s){let c=!1;for(let p of d){let m=r.compileAstNodes(p);if(m.length!==0){try{de(m.map(({node:h})=>h),r)}catch{continue}c=!0;for(let{node:h,propertySort:y}of m){let b=0n;for(let v of p.variants)b|=1n<<BigInt(l.get(v));e.set(h,{properties:y,variants:b,candidate:f}),i.push(h)}}}c||n?.(f)}return i.sort((f,d)=>{let c=e.get(f),p=e.get(d);if(c.variants-p.variants!==0n)return Number(c.variants-p.variants);let m=0;for(;c.properties.length<m&&p.properties.length<m&&c.properties[m]===p.properties[m];)m+=1;return(c.properties[m]??1/0)-(p.properties[m]??1/0)||p.properties.length-c.properties.length||Fe(c.candidate,p.candidate)}),{astNodes:i,nodeSorting:e}}function er(t,r){let n=Un(t,r);if(n.length===0)return[];let e=[],i=`.${me(t.raw)}`;for(let s of n){let l=Fn(s);(t.important||r.important)&&nr(s);let f={kind:"rule",selector:i,nodes:s};for(let d of t.variants)if(ge(f,d,r.variants)===null)return[];e.push({node:f,propertySort:l})}return e}function ge(t,r,n,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[L(r.selector,t.nodes)];return}let{applyFn:i}=n.get(r.root);if(r.kind==="compound"){let l=P("@slot");if(ge(l,r.variant,n,e+1)===null||r.root==="not"&&l.nodes.length>1)return null;for(let d of l.nodes)if(d.kind!=="rule"&&d.kind!=="at-rule"||i(d,r)===null)return null;D(l.nodes,d=>{if((d.kind==="rule"||d.kind==="at-rule")&&d.nodes.length<=0)return d.nodes=t.nodes,1}),t.nodes=l.nodes;return}if(i(t,r)===null)return null}function rr(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function Un(t,r){if(t.kind==="arbitrary"){let l=t.value;return t.modifier&&(l=W(l,t.modifier,r.theme)),l===null?[]:[[a(t.property,l)]]}let n=r.utilities.get(t.root)??[],e=[],i=n.filter(l=>!rr(l));for(let l of i){if(l.kind!==t.kind)continue;let f=l.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}if(e.length>0)return e;let s=n.filter(l=>rr(l));for(let l of s){if(l.kind!==t.kind)continue;let f=l.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}return e}function nr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&nr(r.nodes))}function Fn(t){let r=new Set,n=t.slice();for(;n.length>0;){let e=n.shift();if(e.kind==="declaration"){if(e.property==="--tw-sort"){let s=ft.indexOf(e.value??"");if(s!==-1){r.add(s);break}}let i=ft.indexOf(e.property);i!==-1&&r.add(i)}else if(e.kind==="rule"||e.kind==="at-rule")for(let i of e.nodes)n.push(i)}return Array.from(r).sort((e,i)=>e-i)}function Ce(t,r){let n=0,e=L("&",t),i=new Set,s=new I(()=>new Set),l=new I(()=>new Set);D([e],(m,{parent:h})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return D(m.nodes,y=>{if(y.kind==="at-rule"&&y.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let y=m.params.replace(/-\*$/,"");l.get(y).add(m),D(m.nodes,b=>{if(!(b.kind!=="at-rule"||b.name!=="@apply")){i.add(m);for(let v of ir(b,r))s.get(m).add(v)}});return}if(m.name==="@apply"){if(h===null)return;n|=1,i.add(h);for(let y of ir(m,r))s.get(h).add(y)}}});let f=new Set,d=[],c=new Set;function p(m,h=[]){if(!f.has(m)){if(c.has(m)){let y=h[(h.indexOf(m)+1)%h.length];throw m.kind==="at-rule"&&m.name==="@utility"&&y.kind==="at-rule"&&y.name==="@utility"&&D(m.nodes,b=>{if(b.kind!=="at-rule"||b.name!=="@apply")return;let v=b.params.split(/\s+/g);for(let x of v)for(let S of r.parseCandidate(x))switch(S.kind){case"arbitrary":break;case"static":case"functional":if(y.params.replace(/-\*$/,"")===S.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${G([m])}
Relies on:

${G([y])}`)}c.add(m);for(let y of s.get(m))for(let b of l.get(y))h.push(m),p(b,h),h.pop();f.add(m),c.delete(m),d.push(m)}}for(let m of i)p(m);return D(d,(m,{replaceWith:h})=>{if(m.kind!=="at-rule"||m.name!=="@apply")return;let y=m.params.split(/\s+/g);{let b=ee(y,r,{onInvalidCandidate:x=>{throw new Error(`Cannot apply unknown utility class: ${x}`)}}).astNodes,v=[];for(let x of b)if(x.kind==="rule")for(let S of x.nodes)v.push(S);else v.push(x);h(v)}}),n}function*ir(t,r){for(let n of t.params.split(/\s+/g))for(let e of r.parseCandidate(n))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function dt(t,r,n,e=0){let i=0,s=[];return D(t,(l,{replaceWith:f})=>{if(l.kind==="at-rule"&&(l.name==="@import"||l.name==="@reference")){let d=zn(M(l.params));if(d===null)return;l.name==="@reference"&&(d.media="reference"),i|=2;let{uri:c,layer:p,media:m,supports:h}=d;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let y=ie({},[]);return s.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${c}\` in \`${r}\`)`);let b=await n(c,r),v=ne(b.content);await dt(v,b.base,n,e+1),y.nodes=In([ie({base:b.base},v)],p,m,h)})()),f(y),1}}),s.length>0&&await Promise.all(s),i}function zn(t){let r,n=null,e=null,i=null;for(let s=0;s<t.length;s++){let l=t[s];if(l.kind!=="separator"){if(l.kind==="word"&&!r){if(!l.value||l.value[0]!=='"'&&l.value[0]!=="'")return null;r=l.value.slice(1,-1);continue}if(l.kind==="function"&&l.value.toLowerCase()==="url"||!r)return null;if((l.kind==="word"||l.kind==="function")&&l.value.toLowerCase()==="layer"){if(n)return null;if(i)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in l?n=q(l.nodes):n="";continue}if(l.kind==="function"&&l.value.toLowerCase()==="supports"){if(i)return null;i=q(l.nodes);continue}e=q(t.slice(s));break}}return r?{uri:r,layer:n,media:e,supports:i}:null}function In(t,r,n,e){let i=t;return r!==null&&(i=[P("@layer",r,i)]),n!==null&&(i=[P("@media",n,i)]),e!==null&&(i=[P("@supports",e[0]==="("?e:`(${e})`,i)]),i}var lr=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function or(t,r){return(lr.get(r)??[]).some(n=>t===n||t.startsWith(`${n}-`))}var Ie=class{constructor(r=new Map,n=new Set([])){this.values=r;this.keyframes=n}prefix=null;add(r,n,e=0){if(r.endsWith("\\*")&&(r=r.slice(0,-2)+"*"),r.endsWith("-*")){if(n!=="initial")throw new Error(`Invalid theme value \`${n}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let i=this.values.get(r);if(i&&!(i.options&4))return}n==="initial"?this.values.delete(r):this.values.set(r,{value:n,options:e})}keysInNamespaces(r){let n=[];for(let e of r){let i=`${e}-`;for(let s of this.values.keys())s.startsWith(i)&&s.indexOf("--",2)===-1&&(or(s,e)||n.push(s.slice(i.length)))}return n}get(r){for(let n of r){let e=this.values.get(n);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.#r(r[0]),r)):this.values.entries()}#r(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}clearNamespace(r,n){let e=lr.get(r)??[];e:for(let i of this.values.keys())if(i.startsWith(r)){if(n!==0&&(this.getOptions(i)&n)!==n)continue;for(let s of e)if(i.startsWith(s))continue e;this.values.delete(i)}}#e(r,n){for(let e of n){let i=r!==null?me(`${e}-${r.replaceAll(".","_")}`):e;if(this.values.has(i)&&!or(i,e))return i}return null}#t(r){return this.values.has(r)?`var(${this.#r(r)})`:null}resolve(r,n){let e=this.#e(r,n);if(!e)return null;let i=this.values.get(e);return i.options&1?i.value:this.#t(e)}resolveValue(r,n){let e=this.#e(r,n);return e?this.values.get(e).value:null}resolveWith(r,n,e=[]){let i=this.#e(r,n);if(!i)return null;let s={};for(let f of e){let d=`${i}${f}`,c=this.values.get(d);c&&(c.options&1?s[f]=c.value:s[f]=this.#t(d))}let l=this.values.get(i);return l.options&1?[l.value,s]:[this.#t(i),s]}namespace(r){let n=new Map,e=`${r}-`;for(let[i,s]of this.values)i===r?n.set(null,s.value):i.startsWith(`${e}-`)?n.set(i.slice(r.length),s.value):i.startsWith(e)&&n.set(i.slice(e.length),s.value);return n}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};function he(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function sr(t,{theme:r},n){for(let e of n){let i=Le([e]);i&&t.theme.clearNamespace(`--${i}`,4)}for(let[e,i]of Ln(r)){if(typeof i!="string"&&typeof i!="number")continue;if(typeof i=="string"&&(i=i.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof i=="number"||typeof i=="string")){let l=typeof i=="string"?parseFloat(i):i;l>=0&&l<=1&&(i=l*100+"%")}let s=Le(e);s&&t.theme.add(`--${me(s)}`,""+i,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let i=he(r.fontFamily.sans);i&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",i,e),t.theme.add("--default-font-feature-settings",he(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",he(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let i=he(r.fontFamily.mono);i&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",i,e),t.theme.add("--default-mono-font-feature-settings",he(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",he(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function Ln(t){let r=[];return ur(t,[],(n,e)=>{if(Wn(n))return r.push([e,n]),1;if(Bn(n)){r.push([e,n[0]]);for(let i of Reflect.ownKeys(n[1]))r.push([[...e,`-${i}`],n[1][i]]);return 1}if(Array.isArray(n)&&n.every(i=>typeof i=="string"))return r.push([e,n.join(", ")]),1}),r}var Mn=/^[a-zA-Z0-9-_%/\.]+$/;function Le(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!Mn.test(r))return null;return t.map((r,n,e)=>r==="1"&&n!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(n,e,i)=>`${e}-${i.toLowerCase()}`)).filter((r,n)=>r!=="DEFAULT"||n!==t.length-1).join("-")}function Wn(t){return typeof t=="number"||typeof t=="string"}function Bn(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function ur(t,r=[],n){for(let e of Reflect.ownKeys(t)){let i=t[e];if(i==null)continue;let s=[...r,e],l=n(i,s)??0;if(l!==1){if(l===2)return 2;if(!(!Array.isArray(i)&&typeof i!="object")&&ur(i,s,n)===2)return 2}}}function Me(t){let r=[];for(let n of _(t,".")){if(!n.includes("[")){r.push(n);continue}let e=0;for(;;){let i=n.indexOf("[",e),s=n.indexOf("]",i);if(i===-1||s===-1)break;i>e&&r.push(n.slice(e,i)),r.push(n.slice(i+1,s)),e=s+1}e<=n.length-1&&r.push(n.slice(e))}return r}function ve(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function $e(t,r,n,e=[]){for(let i of r)if(i!=null)for(let s of Reflect.ownKeys(i)){e.push(s);let l=n(t[s],i[s],e);l!==void 0?t[s]=l:!ve(t[s])||!ve(i[s])?t[s]=i[s]:t[s]=$e({},[t[s],i[s]],n,e),e.pop()}return t}function We(t,r,n){return function(i,s){let l=i.lastIndexOf("/"),f=null;l!==-1&&(f=i.slice(l+1).trim(),i=i.slice(0,l).trim());let d=(()=>{let c=Me(i),[p,m]=qn(t.theme,c),h=n(cr(r()??{},c)??null);if(typeof h=="string"&&(h=h.replace("<alpha-value>","1")),typeof p!="object")return typeof m!="object"&&m&4?h??p:p;if(h!==null&&typeof h=="object"&&!Array.isArray(h)){let y=$e({},[h],(b,v)=>v);if(p===null&&Object.hasOwn(h,"__CSS_VALUES__")){let b={};for(let v in h.__CSS_VALUES__)b[v]=h[v],delete y[v];p=b}for(let b in p)b!=="__CSS_VALUES__"&&(h?.__CSS_VALUES__?.[b]&4&&cr(y,b.split("-"))!==void 0||(y[tr(b)]=p[b]));return y}if(Array.isArray(p)&&Array.isArray(m)&&Array.isArray(h)){let y=p[0],b=p[1];m[0]&4&&(y=h[0]??y);for(let v of Object.keys(b))m[1][v]&4&&(b[v]=h[1][v]??b[v]);return[y,b]}return p??h})();return f&&typeof d=="string"&&(d=J(d,f)),d??s}}function qn(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let n=Le(r),e=new Map,i=new I(()=>new Map),s=t.namespace(`--${n}`);if(s.size===0)return[null,0];let l=new Map;for(let[p,m]of s){if(!p||!p.includes("--")){e.set(p,m),l.set(p,t.getOptions(p?`--${n}-${p}`:`--${n}`));continue}let h=p.indexOf("--"),y=p.slice(0,h),b=p.slice(h+2);b=b.replace(/-([a-z])/g,(v,x)=>x.toUpperCase()),i.get(y===""?null:y).set(b,[m,t.getOptions(`--${n}${p}`)])}let f=t.getOptions(`--${n}`);for(let[p,m]of i){let h=e.get(p);if(typeof h!="string")continue;let y={},b={};for(let[v,[x,S]]of m)y[v]=x,b[v]=S;e.set(p,[h,y]),l.set(p,[f,b])}let d={},c={};for(let[p,m]of e)fr(d,[p??"DEFAULT"],m);for(let[p,m]of l)fr(c,[p??"DEFAULT"],m);return r[r.length-1]==="DEFAULT"?[d?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in d&&Object.keys(d).length===1?[d.DEFAULT,c.DEFAULT??0]:(d.__CSS_VALUES__=c,[d,c])}function cr(t,r){for(let n=0;n<r.length;++n){let e=r[n];if(t[e]===void 0){if(r[n+1]===void 0)return;r[n+1]=`${e}-${r[n+1]}`;continue}t=t[e]}return t}function fr(t,r,n){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=n}function Hn(t){return{kind:"combinator",value:t}}function Gn(t,r){return{kind:"function",value:t,nodes:r}}function Ne(t){return{kind:"selector",value:t}}function Jn(t){return{kind:"separator",value:t}}function Yn(t){return{kind:"value",value:t}}function Be(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],s=r(i,{parent:n,replaceWith(l){Array.isArray(l)?l.length===0?t.splice(e,1):l.length===1?t[e]=l[0]:t.splice(e,1,...l):t[e]=l,e--}})??0;if(s===2)return 2;if(s!==1&&i.kind==="function"&&Be(i.nodes,r,i)===2)return 2}}function qe(t){let r="";for(let n of t)switch(n.kind){case"combinator":case"selector":case"separator":case"value":{r+=n.value;break}case"function":r+=n.value+"("+qe(n.nodes)+")"}return r}var dr=92,Zn=93,pr=41,Qn=58,gr=44,Xn=34,ei=46,mr=62,hr=10,ti=35,vr=91,yr=40,br=43,ri=39,wr=32,kr=9,xr=126;function pt(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",s;for(let l=0;l<t.length;l++){let f=t.charCodeAt(l);switch(f){case gr:case mr:case hr:case wr:case br:case kr:case xr:{if(i.length>0){let h=Ne(i);e?e.nodes.push(h):r.push(h),i=""}let d=l,c=l+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==gr&&s!==mr&&s!==hr&&s!==wr&&s!==br&&s!==kr&&s!==xr));c++);l=c-1;let p=t.slice(d,c),m=p.trim()===","?Jn(p):Hn(p);e?e.nodes.push(m):r.push(m);break}case yr:{let d=Gn(i,[]);if(i="",d.value!==":not"&&d.value!==":where"&&d.value!==":has"&&d.value!==":is"){let c=l+1,p=0;for(let h=l+1;h<t.length;h++){if(s=t.charCodeAt(h),s===yr){p++;continue}if(s===pr){if(p===0){l=h;break}p--}}let m=l;d.nodes.push(Yn(t.slice(c,m))),i="",l=m,r.push(d);break}e?e.nodes.push(d):r.push(d),n.push(d),e=d;break}case pr:{let d=n.pop();if(i.length>0){let c=Ne(i);d.nodes.push(c),i=""}n.length>0?e=n[n.length-1]:e=null;break}case ei:case Qn:case ti:{if(i.length>0){let d=Ne(i);e?e.nodes.push(d):r.push(d)}i=String.fromCharCode(f);break}case vr:{if(i.length>0){let p=Ne(i);e?e.nodes.push(p):r.push(p)}i="";let d=l,c=0;for(let p=l+1;p<t.length;p++){if(s=t.charCodeAt(p),s===vr){c++;continue}if(s===Zn){if(c===0){l=p;break}c--}}i+=t.slice(d,l+1);break}case ri:case Xn:{let d=l;for(let c=l+1;c<t.length;c++)if(s=t.charCodeAt(c),s===dr)c+=1;else if(s===f){l=c;break}i+=t.slice(d,l+1);break}case dr:{let d=t.charCodeAt(l+1);i+=String.fromCharCode(f)+String.fromCharCode(d),l+=1;break}default:i+=String.fromCharCode(f)}}return i.length>0&&r.push(Ne(i)),r}var Ar=/^[a-z@][a-zA-Z0-9/%._-]*$/;function gt({designSystem:t,ast:r,resolvedConfig:n,featuresRef:e,referenceMode:i}){let s={addBase(l){if(i)return;let f=Z(l);e.current|=de(f,t),r.push(P("@layer","base",f))},addVariant(l,f){if(!ze.test(l))throw new Error(`\`addVariant('${l}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof f=="string"||Array.isArray(f)?t.variants.static(l,d=>{d.nodes=Cr(f,d.nodes)},{compounds:se(typeof f=="string"?[f]:f)}):typeof f=="object"&&t.variants.fromAst(l,Z(f))},matchVariant(l,f,d){function c(m,h,y){let b=f(m,{modifier:h?.value??null});return Cr(b,y)}let p=Object.keys(d?.values??{});t.variants.group(()=>{t.variants.functional(l,(m,h)=>{if(!h.value){if(d?.values&&"DEFAULT"in d.values){m.nodes=c(d.values.DEFAULT,h.modifier,m.nodes);return}return null}if(h.value.kind==="arbitrary")m.nodes=c(h.value.value,h.modifier,m.nodes);else if(h.value.kind==="named"&&d?.values){let y=d.values[h.value.value];if(typeof y!="string")return;m.nodes=c(y,h.modifier,m.nodes)}})},(m,h)=>{if(m.kind!=="functional"||h.kind!=="functional")return 0;let y=m.value?m.value.value:"DEFAULT",b=h.value?h.value.value:"DEFAULT",v=d?.values?.[y]??y,x=d?.values?.[b]??b;if(d&&typeof d.sort=="function")return d.sort({value:v,modifier:m.modifier?.value??null},{value:x,modifier:h.modifier?.value??null});let S=p.indexOf(y),V=p.indexOf(b);return S=S===-1?p.length:S,V=V===-1?p.length:V,S!==V?S-V:v<x?-1:1})},addUtilities(l){l=Array.isArray(l)?l:[l];let f=l.flatMap(c=>Object.entries(c));f=f.flatMap(([c,p])=>_(c,",").map(m=>[m.trim(),p]));let d=new I(()=>[]);for(let[c,p]of f){if(c.startsWith("@keyframes ")){i||r.push(L(c,Z(p)));continue}let m=pt(c),h=!1;if(Be(m,y=>{if(y.kind==="selector"&&y.value[0]==="."&&Ar.test(y.value.slice(1))){let b=y.value;y.value="&";let v=qe(m),x=b.slice(1),S=v==="&"?Z(p):[L(v,Z(p))];d.get(x).push(...S),h=!0,y.value=b;return}if(y.kind==="function"&&y.value===":not")return 1}),!h)throw new Error(`\`addUtilities({ '${c}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[c,p]of d)t.theme.prefix&&D(p,m=>{if(m.kind==="rule"){let h=pt(m.selector);Be(h,y=>{y.kind==="selector"&&y.value[0]==="."&&(y.value=`.${t.theme.prefix}\\:${y.value.slice(1)}`)}),m.selector=qe(h)}}),t.utilities.static(c,()=>{let m=structuredClone(p);return e.current|=Ce(m,t),m})},matchUtilities(l,f){let d=f?.type?Array.isArray(f?.type)?f.type:[f.type]:["any"];for(let[p,m]of Object.entries(l)){let h=function({negative:y}){return b=>{if(b.value?.kind==="arbitrary"&&d.length>0&&!d.includes("any")&&(b.value.dataType&&!d.includes(b.value.dataType)||!b.value.dataType&&!z(b.value.value,d)))return;let v=d.includes("color"),x=null,S=!1;{let E=f?.values??{};v&&(E=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},E)),b.value?b.value.kind==="arbitrary"?x=b.value.value:b.value.fraction&&E[b.value.fraction]?(x=E[b.value.fraction],S=!0):E[b.value.value]?x=E[b.value.value]:E.__BARE_VALUE__&&(x=E.__BARE_VALUE__(b.value)??null,S=(b.value.fraction!==null&&x?.includes("/"))??!1):x=E.DEFAULT??null}if(x===null)return;let V;{let E=f?.modifiers??null;b.modifier?E==="any"||b.modifier.kind==="arbitrary"?V=b.modifier.value:E?.[b.modifier.value]?V=E[b.modifier.value]:v&&!Number.isNaN(Number(b.modifier.value))?V=`${b.modifier.value}%`:V=null:V=null}if(b.modifier&&V===null&&!S)return b.value?.kind==="arbitrary"?null:void 0;v&&V!==null&&(x=J(x,V)),y&&(x=`calc(${x} * -1)`);let R=Z(m(x,{modifier:V}));return e.current|=Ce(R,t),R}};var c=h;if(!Ar.test(p))throw new Error(`\`matchUtilities({ '${p}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);f?.supportsNegativeValues&&t.utilities.functional(`-${p}`,h({negative:!0}),{types:d}),t.utilities.functional(p,h({negative:!1}),{types:d}),t.utilities.suggest(p,()=>{let y=f?.values??{},b=new Set(Object.keys(y));b.delete("__BARE_VALUE__"),b.has("DEFAULT")&&(b.delete("DEFAULT"),b.add(null));let v=f?.modifiers??{},x=v==="any"?[]:Object.keys(v);return[{supportsNegative:f?.supportsNegativeValues??!1,values:Array.from(b),modifiers:x}]})}},addComponents(l,f){this.addUtilities(l,f)},matchComponents(l,f){this.matchUtilities(l,f)},theme:We(t,()=>n.theme??{},l=>l),prefix(l){return l},config(l,f){let d=n;if(!l)return d;let c=Me(l);for(let p=0;p<c.length;++p){let m=c[p];if(d[m]===void 0)return f;d=d[m]}return d??f}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function Z(t){let r=[];t=Array.isArray(t)?t:[t];let n=t.flatMap(e=>Object.entries(e));for(let[e,i]of n)if(typeof i!="object")!e.startsWith("--")&&i==="@slot"?r.push(L(e,[P("@slot")])):(e=e.replace(/([A-Z])/g,"-$1").toLowerCase(),r.push(a(e,String(i))));else if(Array.isArray(i))for(let s of i)typeof s=="string"?r.push(a(e,s)):r.push(L(e,Z(s)));else i!==null&&r.push(L(e,Z(i)));return r}function Cr(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let i=e.replace("}","{@slot}}"),s=ne(i);return ct(s,r),s}else return L(e,r)})}function $r(t,r,n){for(let e of ii(r))t.theme.addKeyframes(e)}function ii(t){let r=[];if("keyframes"in t.theme)for(let[n,e]of Object.entries(t.theme.keyframes))r.push(P("@keyframes",n,Z(e)));return r}var He={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(0.984 0.003 247.858)",100:"oklch(0.968 0.007 247.896)",200:"oklch(0.929 0.013 255.508)",300:"oklch(0.869 0.022 252.894)",400:"oklch(0.704 0.04 256.788)",500:"oklch(0.554 0.046 257.417)",600:"oklch(0.446 0.043 257.281)",700:"oklch(0.372 0.044 257.287)",800:"oklch(0.279 0.041 260.031)",900:"oklch(0.208 0.042 265.755)",950:"oklch(0.129 0.042 264.695)"},gray:{50:"oklch(0.985 0.002 247.839)",100:"oklch(0.967 0.003 264.542)",200:"oklch(0.928 0.006 264.531)",300:"oklch(0.872 0.01 258.338)",400:"oklch(0.707 0.022 261.325)",500:"oklch(0.551 0.027 264.364)",600:"oklch(0.446 0.03 256.802)",700:"oklch(0.373 0.034 259.733)",800:"oklch(0.278 0.033 256.848)",900:"oklch(0.21 0.034 264.665)",950:"oklch(0.13 0.028 261.692)"},zinc:{50:"oklch(0.985 0 0)",100:"oklch(0.967 0.001 286.375)",200:"oklch(0.92 0.004 286.32)",300:"oklch(0.871 0.006 286.286)",400:"oklch(0.705 0.015 286.067)",500:"oklch(0.552 0.016 285.938)",600:"oklch(0.442 0.017 285.786)",700:"oklch(0.37 0.013 285.805)",800:"oklch(0.274 0.006 286.033)",900:"oklch(0.21 0.006 285.885)",950:"oklch(0.141 0.005 285.823)"},neutral:{50:"oklch(0.985 0 0)",100:"oklch(0.97 0 0)",200:"oklch(0.922 0 0)",300:"oklch(0.87 0 0)",400:"oklch(0.708 0 0)",500:"oklch(0.556 0 0)",600:"oklch(0.439 0 0)",700:"oklch(0.371 0 0)",800:"oklch(0.269 0 0)",900:"oklch(0.205 0 0)",950:"oklch(0.145 0 0)"},stone:{50:"oklch(0.985 0.001 106.423)",100:"oklch(0.97 0.001 106.424)",200:"oklch(0.923 0.003 48.717)",300:"oklch(0.869 0.005 56.366)",400:"oklch(0.709 0.01 56.259)",500:"oklch(0.553 0.013 58.071)",600:"oklch(0.444 0.011 73.639)",700:"oklch(0.374 0.01 67.558)",800:"oklch(0.268 0.007 34.298)",900:"oklch(0.216 0.006 56.043)",950:"oklch(0.147 0.004 49.25)"},red:{50:"oklch(0.971 0.013 17.38)",100:"oklch(0.936 0.032 17.717)",200:"oklch(0.885 0.062 18.334)",300:"oklch(0.808 0.114 19.571)",400:"oklch(0.704 0.191 22.216)",500:"oklch(0.637 0.237 25.331)",600:"oklch(0.577 0.245 27.325)",700:"oklch(0.505 0.213 27.518)",800:"oklch(0.444 0.177 26.899)",900:"oklch(0.396 0.141 25.723)",950:"oklch(0.258 0.092 26.042)"},orange:{50:"oklch(0.98 0.016 73.684)",100:"oklch(0.954 0.038 75.164)",200:"oklch(0.901 0.076 70.697)",300:"oklch(0.837 0.128 66.29)",400:"oklch(0.75 0.183 55.934)",500:"oklch(0.705 0.213 47.604)",600:"oklch(0.646 0.222 41.116)",700:"oklch(0.553 0.195 38.402)",800:"oklch(0.47 0.157 37.304)",900:"oklch(0.408 0.123 38.172)",950:"oklch(0.266 0.079 36.259)"},amber:{50:"oklch(0.987 0.022 95.277)",100:"oklch(0.962 0.059 95.617)",200:"oklch(0.924 0.12 95.746)",300:"oklch(0.879 0.169 91.605)",400:"oklch(0.828 0.189 84.429)",500:"oklch(0.769 0.188 70.08)",600:"oklch(0.666 0.179 58.318)",700:"oklch(0.555 0.163 48.998)",800:"oklch(0.473 0.137 46.201)",900:"oklch(0.414 0.112 45.904)",950:"oklch(0.279 0.077 45.635)"},yellow:{50:"oklch(0.987 0.026 102.212)",100:"oklch(0.973 0.071 103.193)",200:"oklch(0.945 0.129 101.54)",300:"oklch(0.905 0.182 98.111)",400:"oklch(0.852 0.199 91.936)",500:"oklch(0.795 0.184 86.047)",600:"oklch(0.681 0.162 75.834)",700:"oklch(0.554 0.135 66.442)",800:"oklch(0.476 0.114 61.907)",900:"oklch(0.421 0.095 57.708)",950:"oklch(0.286 0.066 53.813)"},lime:{50:"oklch(0.986 0.031 120.757)",100:"oklch(0.967 0.067 122.328)",200:"oklch(0.938 0.127 124.321)",300:"oklch(0.897 0.196 126.665)",400:"oklch(0.841 0.238 128.85)",500:"oklch(0.768 0.233 130.85)",600:"oklch(0.648 0.2 131.684)",700:"oklch(0.532 0.157 131.589)",800:"oklch(0.453 0.124 130.933)",900:"oklch(0.405 0.101 131.063)",950:"oklch(0.274 0.072 132.109)"},green:{50:"oklch(0.982 0.018 155.826)",100:"oklch(0.962 0.044 156.743)",200:"oklch(0.925 0.084 155.995)",300:"oklch(0.871 0.15 154.449)",400:"oklch(0.792 0.209 151.711)",500:"oklch(0.723 0.219 149.579)",600:"oklch(0.627 0.194 149.214)",700:"oklch(0.527 0.154 150.069)",800:"oklch(0.448 0.119 151.328)",900:"oklch(0.393 0.095 152.535)",950:"oklch(0.266 0.065 152.934)"},emerald:{50:"oklch(0.979 0.021 166.113)",100:"oklch(0.95 0.052 163.051)",200:"oklch(0.905 0.093 164.15)",300:"oklch(0.845 0.143 164.978)",400:"oklch(0.765 0.177 163.223)",500:"oklch(0.696 0.17 162.48)",600:"oklch(0.596 0.145 163.225)",700:"oklch(0.508 0.118 165.612)",800:"oklch(0.432 0.095 166.913)",900:"oklch(0.378 0.077 168.94)",950:"oklch(0.262 0.051 172.552)"},teal:{50:"oklch(0.984 0.014 180.72)",100:"oklch(0.953 0.051 180.801)",200:"oklch(0.91 0.096 180.426)",300:"oklch(0.855 0.138 181.071)",400:"oklch(0.777 0.152 181.912)",500:"oklch(0.704 0.14 182.503)",600:"oklch(0.6 0.118 184.704)",700:"oklch(0.511 0.096 186.391)",800:"oklch(0.437 0.078 188.216)",900:"oklch(0.386 0.063 188.416)",950:"oklch(0.277 0.046 192.524)"},cyan:{50:"oklch(0.984 0.019 200.873)",100:"oklch(0.956 0.045 203.388)",200:"oklch(0.917 0.08 205.041)",300:"oklch(0.865 0.127 207.078)",400:"oklch(0.789 0.154 211.53)",500:"oklch(0.715 0.143 215.221)",600:"oklch(0.609 0.126 221.723)",700:"oklch(0.52 0.105 223.128)",800:"oklch(0.45 0.085 224.283)",900:"oklch(0.398 0.07 227.392)",950:"oklch(0.302 0.056 229.695)"},sky:{50:"oklch(0.977 0.013 236.62)",100:"oklch(0.951 0.026 236.824)",200:"oklch(0.901 0.058 230.902)",300:"oklch(0.828 0.111 230.318)",400:"oklch(0.746 0.16 232.661)",500:"oklch(0.685 0.169 237.323)",600:"oklch(0.588 0.158 241.966)",700:"oklch(0.5 0.134 242.749)",800:"oklch(0.443 0.11 240.79)",900:"oklch(0.391 0.09 240.876)",950:"oklch(0.293 0.066 243.157)"},blue:{50:"oklch(0.97 0.014 254.604)",100:"oklch(0.932 0.032 255.585)",200:"oklch(0.882 0.059 254.128)",300:"oklch(0.809 0.105 251.813)",400:"oklch(0.707 0.165 254.624)",500:"oklch(0.623 0.214 259.815)",600:"oklch(0.546 0.245 262.881)",700:"oklch(0.488 0.243 264.376)",800:"oklch(0.424 0.199 265.638)",900:"oklch(0.379 0.146 265.522)",950:"oklch(0.282 0.091 267.935)"},indigo:{50:"oklch(0.962 0.018 272.314)",100:"oklch(0.93 0.034 272.788)",200:"oklch(0.87 0.065 274.039)",300:"oklch(0.785 0.115 274.713)",400:"oklch(0.673 0.182 276.935)",500:"oklch(0.585 0.233 277.117)",600:"oklch(0.511 0.262 276.966)",700:"oklch(0.457 0.24 277.023)",800:"oklch(0.398 0.195 277.366)",900:"oklch(0.359 0.144 278.697)",950:"oklch(0.257 0.09 281.288)"},violet:{50:"oklch(0.969 0.016 293.756)",100:"oklch(0.943 0.029 294.588)",200:"oklch(0.894 0.057 293.283)",300:"oklch(0.811 0.111 293.571)",400:"oklch(0.702 0.183 293.541)",500:"oklch(0.606 0.25 292.717)",600:"oklch(0.541 0.281 293.009)",700:"oklch(0.491 0.27 292.581)",800:"oklch(0.432 0.232 292.759)",900:"oklch(0.38 0.189 293.745)",950:"oklch(0.283 0.141 291.089)"},purple:{50:"oklch(0.977 0.014 308.299)",100:"oklch(0.946 0.033 307.174)",200:"oklch(0.902 0.063 306.703)",300:"oklch(0.827 0.119 306.383)",400:"oklch(0.714 0.203 305.504)",500:"oklch(0.627 0.265 303.9)",600:"oklch(0.558 0.288 302.321)",700:"oklch(0.496 0.265 301.924)",800:"oklch(0.438 0.218 303.724)",900:"oklch(0.381 0.176 304.987)",950:"oklch(0.291 0.149 302.717)"},fuchsia:{50:"oklch(0.977 0.017 320.058)",100:"oklch(0.952 0.037 318.852)",200:"oklch(0.903 0.076 319.62)",300:"oklch(0.833 0.145 321.434)",400:"oklch(0.74 0.238 322.16)",500:"oklch(0.667 0.295 322.15)",600:"oklch(0.591 0.293 322.896)",700:"oklch(0.518 0.253 323.949)",800:"oklch(0.452 0.211 324.591)",900:"oklch(0.401 0.17 325.612)",950:"oklch(0.293 0.136 325.661)"},pink:{50:"oklch(0.971 0.014 343.198)",100:"oklch(0.948 0.028 342.258)",200:"oklch(0.899 0.061 343.231)",300:"oklch(0.823 0.12 346.018)",400:"oklch(0.718 0.202 349.761)",500:"oklch(0.656 0.241 354.308)",600:"oklch(0.592 0.249 0.584)",700:"oklch(0.525 0.223 3.958)",800:"oklch(0.459 0.187 3.815)",900:"oklch(0.408 0.153 2.432)",950:"oklch(0.284 0.109 3.907)"},rose:{50:"oklch(0.969 0.015 12.422)",100:"oklch(0.941 0.03 12.58)",200:"oklch(0.892 0.058 10.001)",300:"oklch(0.81 0.117 11.638)",400:"oklch(0.712 0.194 13.428)",500:"oklch(0.645 0.246 16.439)",600:"oklch(0.586 0.253 17.585)",700:"oklch(0.514 0.222 16.935)",800:"oklch(0.455 0.188 13.697)",900:"oklch(0.41 0.159 10.272)",950:"oklch(0.271 0.105 12.094)"}};function ue(t){return{__BARE_VALUE__:t}}var Y=ue(t=>{if(N(t.value))return t.value}),B=ue(t=>{if(N(t.value))return`${t.value}%`}),te=ue(t=>{if(N(t.value))return`${t.value}px`}),Nr=ue(t=>{if(N(t.value))return`${t.value}ms`}),Ge=ue(t=>{if(N(t.value))return`${t.value}deg`}),oi=ue(t=>{if(t.fraction===null)return;let[r,n]=_(t.fraction,"/");if(!(!N(r)||!N(n)))return t.fraction}),Tr=ue(t=>{if(N(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),Vr={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...oi},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...B}),backdropContrast:({theme:t})=>({...t("contrast"),...B}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...B}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...Ge}),backdropInvert:({theme:t})=>({...t("invert"),...B}),backdropOpacity:({theme:t})=>({...t("opacity"),...B}),backdropSaturate:({theme:t})=>({...t("saturate"),...B}),backdropSepia:({theme:t})=>({...t("sepia"),...B}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...te},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...B},caretColor:({theme:t})=>t("colors"),colors:()=>({...He}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...Y},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...B},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...te}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...Y},flexShrink:{0:"0",DEFAULT:"1",...Y},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...B},grayscale:{0:"0",DEFAULT:"100%",...B},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Tr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Tr},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...Ge},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...B},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...Y},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...B},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...Y},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...Ge},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...B},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...B},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...B},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...Ge},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...Y},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Nr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Nr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...Y}};function Sr(t){return{theme:{...Vr,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var li={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function ht(t,r){let n={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(li)};for(let i of r)mt(n,i);for(let i of n.configs)"darkMode"in i&&i.darkMode!==void 0&&(n.result.darkMode=i.darkMode??null),"prefix"in i&&i.prefix!==void 0&&(n.result.prefix=i.prefix??""),"blocklist"in i&&i.blocklist!==void 0&&(n.result.blocklist=i.blocklist??[]),"important"in i&&i.important!==void 0&&(n.result.important=i.important??!1);let e=si(n);return{resolvedConfig:{...n.result,content:n.content,theme:n.theme,plugins:n.plugins},replacedThemeKeys:e}}function ai(t,r){if(Array.isArray(t)&&ve(t[0]))return t.concat(r);if(Array.isArray(r)&&ve(r[0])&&ve(t))return[t,...r];if(Array.isArray(r))return r}function mt(t,{config:r,base:n,path:e,reference:i}){let s=[];for(let d of r.plugins??[])"__isOptionsFunction"in d?s.push({...d(),reference:i}):"handler"in d?s.push({...d,reference:i}):s.push({handler:d,reference:i});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let d of r.presets??[])mt(t,{path:e,base:n,config:d,reference:i});for(let d of s)t.plugins.push(d),d.config&&mt(t,{path:e,base:n,config:d.config,reference:!!d.reference});let l=r.content??[],f=Array.isArray(l)?l:l.files;for(let d of f)t.content.files.push(typeof d=="object"?d:{base:n,pattern:d});t.configs.push(r)}function si(t){let r=new Set,n=We(t.design,()=>t.theme,i),e=Object.assign(n,{theme:n,colors:He});function i(s){return typeof s=="function"?s(e)??null:s??null}for(let s of t.configs){let l=s.theme??{},f=l.extend??{};for(let d in l)d!=="extend"&&r.add(d);Object.assign(t.theme,l);for(let d in f)t.extend[d]??=[],t.extend[d].push(f[d])}delete t.theme.extend;for(let s in t.extend){let l=[t.theme[s],...t.extend[s]];t.theme[s]=()=>{let f=l.map(i);return $e({},f,ai)}}for(let s in t.theme)t.theme[s]=i(t.theme[s]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let s of Object.keys(t.theme.screens)){let l=t.theme.screens[s];l&&typeof l=="object"&&("raw"in l||"max"in l||"min"in l&&(t.theme.screens[s]=l.min))}return r}function Er(t,r){let n=t.theme.container||{};if(typeof n!="object"||n===null)return;let e=ui(n,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function ui({center:t,padding:r,screens:n},e){let i=[],s=null;if(t&&i.push(a("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&i.push(a("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof n=="object"&&n!==null){s=new Map;let l=Array.from(e.theme.namespace("--breakpoint").entries());if(l.sort((f,d)=>le(f[1],d[1],"asc")),l.length>0){let[f]=l[0];i.push(P("@media",`(width >= --theme(--breakpoint-${f}))`,[a("max-width","none")]))}for(let[f,d]of Object.entries(n)){if(typeof d=="object")if("min"in d)d=d.min;else continue;s.set(f,P("@media",`(width >= ${d})`,[a("max-width",d)]))}}if(typeof r=="object"&&r!==null){let l=Object.entries(r).filter(([f])=>f!=="DEFAULT").map(([f,d])=>[f,e.theme.resolveValue(f,["--breakpoint"]),d]).filter(Boolean);l.sort((f,d)=>le(f[1],d[1],"asc"));for(let[f,,d]of l)if(s&&s.has(f))s.get(f).nodes.push(a("padding-inline",d));else{if(s)continue;i.push(P("@media",`(width >= theme(--breakpoint-${f}))`,[a("padding-inline",d)]))}}if(s)for(let[,l]of s)i.push(l);return i}function Rr({addVariant:t,config:r}){let n=r("darkMode",null),[e,i=".dark"]=Array.isArray(n)?n:[n];if(e==="variant"){let s;if(Array.isArray(i)||typeof i=="function"?s=i:typeof i=="string"&&(s=[i]),Array.isArray(s))for(let l of s)l===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):l.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));i=s}e===null||(e==="selector"?t("dark",`&:where(${i}, ${i} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",i):e==="class"&&t("dark",`&:is(${i} *)`))}function Or(t){for(let[r,n]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[a("--tw-gradient-position",`to ${n} in oklab,`),a("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let n=t.theme.resolve(r.value.value,["--breakpoint"]);if(n)return[a("max-width",n)]}),t.utilities.static("overflow-ellipsis",()=>[a("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[a("-webkit-box-decoration-break","slice"),a("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[a("-webkit-box-decoration-break","clone"),a("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[a("flex-shrink","1")];if(r.value.kind==="arbitrary")return[a("flex-shrink",r.value.value)];if(N(r.value.value))return[a("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[a("flex-grow","1")];if(r.value.kind==="arbitrary")return[a("flex-grow",r.value.value)];if(N(r.value.value))return[a("flex-grow",r.value.value)]}})}function Kr(t,r){let n=t.theme.screens||{},e=r.variants.get("min")?.order??0,i=[];for(let[l,f]of Object.entries(n)){let h=function(y){r.variants.static(l,b=>{b.nodes=[P("@media",m,b.nodes)]},{order:y})};var s=h;let d=r.variants.get(l),c=r.theme.resolveValue(l,["--breakpoint"]);if(d&&c&&!r.theme.hasDefault(`--breakpoint-${l}`))continue;let p=!0;typeof f=="string"&&(p=!1);let m=ci(f);p?i.push(h):h(e)}if(i.length!==0){for(let[,l]of r.variants.variants)l.order>e&&(l.order+=i.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([l,f])=>(l>e&&(l+=i.length),[l,f])));for(let[l,f]of i.entries())f(e+l+1)}}function ci(t){return(Array.isArray(t)?t:[t]).map(n=>typeof n=="string"?{min:n}:n&&typeof n=="object"?n:null).map(n=>{if(n===null)return null;if("raw"in n)return n.raw;let e="";return n.max!==void 0&&(e+=`${n.max} >= `),e+="width",n.min!==void 0&&(e+=` >= ${n.min}`),`(${e})`}).filter(Boolean).join(", ")}function _r(t,r){let n=t.theme.aria||{},e=t.theme.supports||{},i=t.theme.data||{};if(Object.keys(n).length>0){let s=r.variants.get("aria"),l=s?.applyFn,f=s?.compounds;r.variants.functional("aria",(d,c)=>{let p=c.value;return p&&p.kind==="named"&&p.value in n?l?.(d,{...c,value:{kind:"arbitrary",value:n[p.value]}}):l?.(d,c)},{compounds:f})}if(Object.keys(e).length>0){let s=r.variants.get("supports"),l=s?.applyFn,f=s?.compounds;r.variants.functional("supports",(d,c)=>{let p=c.value;return p&&p.kind==="named"&&p.value in e?l?.(d,{...c,value:{kind:"arbitrary",value:e[p.value]}}):l?.(d,c)},{compounds:f})}if(Object.keys(i).length>0){let s=r.variants.get("data"),l=s?.applyFn,f=s?.compounds;r.variants.functional("data",(d,c)=>{let p=c.value;return p&&p.kind==="named"&&p.value in i?l?.(d,{...c,value:{kind:"arbitrary",value:i[p.value]}}):l?.(d,c)},{compounds:f})}}var fi=/^[a-z]+$/;async function Dr({designSystem:t,base:r,ast:n,loadModule:e,globs:i}){let s=0,l=[],f=[];D(n,(m,{parent:h,replaceWith:y,context:b})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(h!==null)throw new Error("`@plugin` cannot be nested.");let v=m.params.slice(1,-1);if(v.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let S of m.nodes??[]){if(S.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${G([S])}

\`@plugin\` options must be a flat list of declarations.`);if(S.value===void 0)continue;let V=S.value,R=_(V,",").map(E=>{if(E=E.trim(),E==="null")return null;if(E==="true")return!0;if(E==="false")return!1;if(Number.isNaN(Number(E))){if(E[0]==='"'&&E[E.length-1]==='"'||E[0]==="'"&&E[E.length-1]==="'")return E.slice(1,-1);if(E[0]==="{"&&E[E.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${G([S]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(E);return E});x[S.property]=R.length===1?R[0]:R}l.push([{id:v,base:b.base,reference:!!b.reference},Object.keys(x).length>0?x:null]),y([]),s|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(h!==null)throw new Error("`@config` cannot be nested.");f.push({id:m.params.slice(1,-1),base:b.base,reference:!!b.reference}),y([]),s|=4;return}}}),Or(t);let d=t.resolveThemeValue;if(t.resolveThemeValue=function(h){return h.startsWith("--")?d(h):(s|=Pr({designSystem:t,base:r,ast:n,globs:i,configs:[],pluginDetails:[]}),t.resolveThemeValue(h))},!l.length&&!f.length)return 0;let[c,p]=await Promise.all([Promise.all(f.map(async({id:m,base:h,reference:y})=>{let b=await e(m,h,"config");return{path:m,base:b.base,config:b.module,reference:y}})),Promise.all(l.map(async([{id:m,base:h,reference:y},b])=>{let v=await e(m,h,"plugin");return{path:m,base:v.base,plugin:v.module,options:b,reference:y}}))]);return s|=Pr({designSystem:t,base:r,ast:n,globs:i,configs:c,pluginDetails:p}),s}function Pr({designSystem:t,base:r,ast:n,globs:e,configs:i,pluginDetails:s}){let l=0,d=[...s.map(v=>{if(!v.options)return{config:{plugins:[v.plugin]},base:v.base,reference:v.reference};if("__isOptionsFunction"in v.plugin)return{config:{plugins:[v.plugin(v.options)]},base:v.base,reference:v.reference};throw new Error(`The plugin "${v.path}" does not accept options`)}),...i],{resolvedConfig:c}=ht(t,[{config:Sr(t.theme),base:r,reference:!0},...d,{config:{plugins:[Rr]},base:r,reference:!0}]),{resolvedConfig:p,replacedThemeKeys:m}=ht(t,d);t.resolveThemeValue=function(x,S){let V=y.theme(x,S);if(Array.isArray(V)&&V.length===2)return V[0];if(Array.isArray(V))return V.join(", ");if(typeof V=="string")return V};let h={designSystem:t,ast:n,resolvedConfig:c,featuresRef:{set current(v){l|=v}}},y=gt({...h,referenceMode:!1}),b;for(let{handler:v,reference:x}of c.plugins)x?(b||=gt({...h,referenceMode:!0}),v(b)):v(y);if(sr(t,p,m),$r(t,p,m),_r(p,t),Kr(p,t),Er(p,t),!t.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!fi.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=c.prefix}if(!t.important&&c.important===!0&&(t.important=!0),typeof c.important=="string"){let v=c.important;D(n,(x,{replaceWith:S,parent:V})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return V?.kind==="rule"&&V.selector===v?2:(S(U(v,[x])),2)})}for(let v of c.blocklist)t.invalidCandidates.add(v);for(let v of c.content.files){if("raw"in v)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(v,null,2)}

This feature is not currently supported.`);e.push(v)}return l}var di=/^[a-z]+$/;function pi(){throw new Error("No `loadModule` function provided to `compile`")}function gi(){throw new Error("No `loadStylesheet` function provided to `compile`")}function mi(t){let r=0,n=null;for(let e of _(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e.startsWith("prefix(")&&e.endsWith(")")&&(n=e.slice(7,-1));return[r,n]}var pe=(f=>(f[f.None=0]="None",f[f.AtApply=1]="AtApply",f[f.AtImport=2]="AtImport",f[f.JsPluginCompat=4]="JsPluginCompat",f[f.ThemeFunction=8]="ThemeFunction",f[f.Utilities=16]="Utilities",f[f.Variants=32]="Variants",f))(pe||{});async function jr(t,{base:r="",loadModule:n=pi,loadStylesheet:e=gi}={}){let i=0;t=[ie({base:r},t)],i|=await dt(t,r,e);let s=null,l=new Ie,f=[],d=[],c=null,p=null,m=[],h=[],y=null;D(t,(v,{parent:x,replaceWith:S,context:V})=>{if(v.kind==="at-rule"){if(v.name==="@tailwind"&&(v.params==="utilities"||v.params.startsWith("utilities"))){if(p!==null){S([]);return}let R=_(v.params," ");for(let E of R)if(E.startsWith("source(")){let K=E.slice(7,-1);if(K==="none"){y=K;continue}if(K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");y={base:V.sourceBase??V.base,pattern:K.slice(1,-1)}}p=v,i|=16}if(v.name==="@utility"){if(x!==null)throw new Error("`@utility` cannot be nested.");if(v.nodes.length===0)throw new Error(`\`@utility ${v.params}\` is empty. Utilities should include at least one property.`);let R=Wt(v);if(R===null)throw new Error(`\`@utility ${v.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);d.push(R)}if(v.name==="@source"){if(v.nodes.length>0)throw new Error("`@source` cannot have a body.");if(x!==null)throw new Error("`@source` cannot be nested.");let R=v.params;if(R[0]==='"'&&R[R.length-1]!=='"'||R[0]==="'"&&R[R.length-1]!=="'"||R[0]!=="'"&&R[0]!=='"')throw new Error("`@source` paths must be quoted.");h.push({base:V.base,pattern:R.slice(1,-1)}),S([]);return}if(v.name==="@variant"&&(x===null?v.nodes.length===0?v.name="@custom-variant":D(v.nodes,R=>{if(R.kind==="at-rule"&&R.name==="@slot")return v.name="@custom-variant",2}):m.push(v)),v.name==="@custom-variant"){if(x!==null)throw new Error("`@custom-variant` cannot be nested.");S([]);let[R,E]=_(v.params," ");if(!ze.test(R))throw new Error(`\`@custom-variant ${R}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(v.nodes.length>0&&E)throw new Error(`\`@custom-variant ${R}\` cannot have both a selector and a body.`);if(v.nodes.length===0){if(!E)throw new Error(`\`@custom-variant ${R}\` has no selector or body.`);let K=_(E.slice(1,-1),",");if(K.length===0||K.some(g=>g.trim()===""))throw new Error(`\`@custom-variant ${R} (${K.join(",")})\` selector is invalid.`);let o=[],u=[];for(let g of K)g=g.trim(),g[0]==="@"?o.push(g):u.push(g);f.push(g=>{g.variants.static(R,k=>{let w=[];u.length>0&&w.push(U(u.join(", "),k.nodes));for(let T of o)w.push(L(T,k.nodes));k.nodes=w},{compounds:se([...u,...o])})});return}else{f.push(K=>{K.variants.fromAst(R,v.nodes)});return}}if(v.name==="@media"){let R=_(v.params," "),E=[];for(let K of R)if(K.startsWith("source(")){let o=K.slice(7,-1);D(v.nodes,(u,{replaceWith:g})=>{if(u.kind==="at-rule"&&u.name==="@tailwind"&&u.params==="utilities")return u.params+=` source(${o})`,g([ie({sourceBase:V.base},[u])]),2})}else if(K.startsWith("theme(")){let o=K.slice(6,-1);D(v.nodes,u=>{if(u.kind!=="at-rule")throw new Error('Files imported with `@import "\u2026" theme(\u2026)` must only contain `@theme` blocks.');if(u.name==="@theme")return u.params+=" "+o,1})}else if(K.startsWith("prefix(")){let o=K.slice(7,-1);D(v.nodes,u=>{if(u.kind==="at-rule"&&u.name==="@theme")return u.params+=` prefix(${o})`,1})}else K==="important"?s=!0:K==="reference"?v.nodes=[ie({reference:!0},v.nodes)]:E.push(K);return E.length>0?v.params=E.join(" "):R.length>0&&S(v.nodes),1}if(v.name==="@theme"){let[R,E]=mi(v.params);if(V.reference&&(R|=2),E){if(!di.test(E))throw new Error(`The prefix "${E}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);l.prefix=E}return D(v.nodes,(K,{replaceWith:o})=>{if(K.kind==="at-rule"&&K.name==="@keyframes")return l.addKeyframes(K),o([]),1;if(K.kind==="comment")return;if(K.kind==="declaration"&&K.property.startsWith("--")){l.add(K.property,K.value??"",R);return}let u=G([P(v.name,v.params,[K])]).split(`
`).map((g,k,w)=>`${k===0||k>=w.length-2?" ":">"} ${g}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${u}`)}),!c&&!(R&2)?(c=U(":root, :host",v.nodes),S([c])):S([]),1}}});let b=Xt(l);s&&(b.important=s),i|=await Dr({designSystem:b,base:r,ast:t,loadModule:n,globs:h});for(let v of f)v(b);for(let v of d)v(b);if(c){let v=[];for(let[S,V]of l.entries())V.options&2||v.push(a(S,V.value));let x=l.getKeyframes();if(x.length>0){let S=[...l.namespace("--animate").values()].flatMap(V=>V.split(" "));for(let V of x){let R=V.params;S.includes(R)&&v.push(j([V]))}}c.nodes=v}if(p){let v=p;v.kind="context",v.context={}}if(m.length>0){for(let v of m){let x=U("&",v.nodes),S=v.params,V=b.parseVariant(S);if(V===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${S}`);if(ge(x,V,b.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${S}`);Object.assign(v,x)}i|=32}return i|=de(t,b),i|=Ce(t,b),D(t,(v,{replaceWith:x})=>{if(v.kind==="at-rule")return v.name==="@utility"&&x([]),1}),{designSystem:b,ast:t,globs:h,root:y,utilitiesNode:p,features:i}}async function Ur(t,r={}){let{designSystem:n,ast:e,globs:i,root:s,utilitiesNode:l,features:f}=await jr(t,r);e.unshift(Ke(`! tailwindcss v${vt} | MIT License | https://tailwindcss.com `));function d(h){n.invalidCandidates.add(h)}let c=new Set,p=null,m=0;return{globs:i,root:s,features:f,build(h){if(f===0)return t;if(!l)return p??=oe(e),p;let y=!1,b=c.size;for(let x of h)n.invalidCandidates.has(x)||(c.add(x),y||=c.size!==b);if(!y)return p??=oe(e),p;let v=ee(c,n,{onInvalidCandidate:d}).astNodes;return m===v.length?(p??=oe(e),p):(m=v.length,l.nodes=v,p=oe(e),p)}}}async function hi(t,r={}){let n=ne(t),e=await Ur(n,r),i=n,s=t;return{...e,build(l){let f=e.build(l);return f===i||(s=G(f),i=f),s}}}async function vi(t,r={}){return(await jr(ne(t),r)).designSystem}function Te(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let t in Je)t!=="default"&&(Te[t]=Je[t]);module.exports=Te;
