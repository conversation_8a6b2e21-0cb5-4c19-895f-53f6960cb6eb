"use strict";var Fr=Object.defineProperty,zr=(e,t)=>{for(var r in t)Fr(e,r,{get:t[r],enumerable:!0})},Je={},vt=(zr(Je,{Features:()=>pe,__unstable__loadDesignSystem:()=>vi,compile:()=>hi,compileAst:()=>Ur,default:()=>Te}),"4.0.1"),ye=92,Se=47,Ee=42,Lr=34,Mr=39,Wr=58,Re=59,re=10,be=32,Oe=9,yt=123,Ye=125,Xe=40,bt=41,Br=91,qr=93,wt=45,Ze=64,Hr=33;function ne(i){i=i.replaceAll(`\r
`,`
`);let t=[],r=[],e=[],o=null,a=null,l="",s="",u;for(let n=0;n<i.length;n++){var c=i.charCodeAt(n);if(c===ye)l+=i.slice(n,n+2),n+=1;else if(c===Se&&i.charCodeAt(n+1)===Ee){var d=n;for(let e=n+2;e<i.length;e++)if((u=i.charCodeAt(e))===ye)e+=1;else if(u===Ee&&i.charCodeAt(e+1)===Se){n=e+1;break}let e=i.slice(d,n+1);e.charCodeAt(2)===Hr&&r.push(Ke(e.slice(2,-2)))}else if(c===Mr||c===Lr){var f=n;for(let e=n+1;e<i.length;e++)if((u=i.charCodeAt(e))===ye)e+=1;else{if(u===c){n=e;break}if(u===Re&&i.charCodeAt(e+1)===re)throw new Error("Unterminated string: "+(i.slice(f,e+1)+String.fromCharCode(c)));if(u===re)throw new Error("Unterminated string: "+(i.slice(f,e)+String.fromCharCode(c)))}l+=i.slice(f,n+1)}else if(c!==be&&c!==re&&c!==Oe||!(u=i.charCodeAt(n+1))||u!==be&&u!==re&&u!==Oe)if(c===re)0!==l.length&&(u=l.charCodeAt(l.length-1))!==be&&u!==re&&u!==Oe&&(l+=" ");else if(c===wt&&i.charCodeAt(n+1)===wt&&0===l.length){let e="",r=n,a=-1;for(let t=n+2;t<i.length;t++)if((u=i.charCodeAt(t))===ye)t+=1;else if(u===Se&&i.charCodeAt(t+1)===Ee){for(let e=t+2;e<i.length;e++)if((u=i.charCodeAt(e))===ye)e+=1;else if(u===Ee&&i.charCodeAt(e+1)===Se){t=e+1;break}}else if(-1===a&&u===Wr)a=l.length+t-r;else{if(u===Re&&0===e.length){l+=i.slice(r,t),n=t;break}if(u===Xe)e+=")";else if(u===Br)e+="]";else if(u===yt)e+="}";else{if((u===Ye||i.length-1===t)&&0===e.length){n=t-1,l+=i.slice(r,t);break}(u===bt||u===qr||u===Ye)&&0<e.length&&i[t]===e[e.length-1]&&(e=e.slice(0,-1))}}d=Qe(l,a);(o?o.nodes:t).push(d),l=""}else if(c===Re&&l.charCodeAt(0)===Ze)a=we(l),(o?o.nodes:t).push(a),l="",a=null;else if(c===Re&&")"!==s[s.length-1]){var p=Qe(l);(o?o.nodes:t).push(p),l=""}else if(c===yt&&")"!==s[s.length-1])s+="}",a=L(l.trim()),o&&o.nodes.push(a),e.push(o),o=a,l="",a=null;else if(c===Ye&&")"!==s[s.length-1]){if(""===s)throw new Error("Missing opening {");s=s.slice(0,-1),0<l.length&&(l.charCodeAt(0)===Ze?(a=we(l),(o?o.nodes:t).push(a),l="",a=null):(p=l.indexOf(":"),o&&o.nodes.push(Qe(l,p))));var h=e.pop()??null;null===h&&o&&t.push(o),o=h,l="",a=null}else if(c===Xe)s+=")",l+="(";else if(c===bt){if(")"!==s[s.length-1])throw new Error("Missing opening (");s=s.slice(0,-1),l+=")"}else(0!==l.length||c!==be&&c!==re&&c!==Oe)&&(l+=String.fromCharCode(c))}if(l.charCodeAt(0)===Ze&&t.push(we(l)),0<s.length&&o){if("rule"===o.kind)throw new Error("Missing closing } at "+o.selector);if("at-rule"===o.kind)throw new Error(`Missing closing } at ${o.name} `+o.params)}return 0<r.length?r.concat(t):t}function we(t,r=[]){for(let e=5;e<t.length;e++){var a=t.charCodeAt(e);if(a===be||a===Xe)return P(t.slice(0,e).trim(),t.slice(e).trim(),r)}return P(t.trim(),"",r)}function Qe(e,t=e.indexOf(":")){var r=e.indexOf("!important",t+1);return a(e.slice(0,t).trim(),e.slice(t+1,-1===r?e.length:r).trim(),-1!==r)}var Gr=64;function U(e,t=[]){return{kind:"rule",selector:e,nodes:t}}function P(e,t="",r=[]){return{kind:"at-rule",name:e,params:t,nodes:r}}function L(e,t=[]){return(e.charCodeAt(0)===Gr?we:U)(e,t)}function a(e,t,r=!1){return{kind:"declaration",property:e,value:t,important:r}}function Ke(e){return{kind:"comment",value:e}}function ie(e,t){return{kind:"context",context:e,nodes:t}}function j(e){return{kind:"at-root",nodes:e}}function D(r,e,a=[],n={}){for(let t=0;t<r.length;t++){var i=r[t],o=a[a.length-1]??null;if("context"===i.kind){if(2===D(i.nodes,e,a,{...n,...i.context}))return 2}else{a.push(i);o=e(i,{parent:o,context:n,path:a,replaceWith(e){Array.isArray(e)?0===e.length?r.splice(t,1):1===e.length?r[t]=e[0]:r.splice(t,1,...e):r[t]=e,t--}})??0;if(a.pop(),2===o)return 2;if(1!==o&&("rule"===i.kind||"at-rule"===i.kind)){a.push(i);o=D(i.nodes,e,a,n);if(a.pop(),2===o)return 2}}}}function _e(r,e,a=[],n={}){for(let t=0;t<r.length;t++){var i=r[t],o=a[a.length-1]??null;if("rule"===i.kind||"at-rule"===i.kind)a.push(i),_e(i.nodes,e,a,n),a.pop();else if("context"===i.kind){_e(i.nodes,e,a,{...n,...i.context});continue}a.push(i),e(i,{parent:o,context:n,path:a,replaceWith(e){Array.isArray(e)?0===e.length?r.splice(t,1):1===e.length?r[t]=e[0]:r.splice(t,1,...e):r[t]=e,t+=e.length-1}}),a.pop()}}function oe(e){let v=[],g=new Set;let t=[];for(var r of e)!function e(t,r,a=0){if("declaration"===t.kind)"--tw-sort"!==t.property&&void 0!==t.value&&null!==t.value&&r.push(t);else if("rule"===t.kind)if("&"===t.selector)for(var n of t.nodes){var i=[];e(n,i,a+1),r.push(...i)}else{var o,l={...t,nodes:[]};for(o of t.nodes)e(o,l.nodes,a+1);r.push(l)}else if("at-rule"===t.kind&&"@property"===t.name&&0===a){if(!g.has(t.params)){g.add(t.params);var s,u={...t,nodes:[]};for(s of t.nodes)e(s,u.nodes,a+1);r.push(u)}}else if("at-rule"===t.kind){var c,d={...t,nodes:[]};for(c of t.nodes)e(c,d.nodes,a+1);r.push(d)}else if("at-root"===t.kind)for(var f of t.nodes){var p,h=[];e(f,h,0);for(p of h)v.push(p)}else if("context"===t.kind){if(!t.context.reference)for(var m of t.nodes)e(m,r,a)}else"comment"===t.kind&&r.push(t)}(r,t,0);return t.concat(v)}function G(e){let t="";for(var r of e){r=function e(t,r=0){let a="",n="  ".repeat(r);if("declaration"===t.kind)a+=""+n+t.property+`: ${t.value}${t.important?" !important":""};
`;else if("rule"===t.kind){a+=""+n+t.selector+` {
`;for(var i of t.nodes)a+=e(i,r+1);a+=n+`}
`}else if("at-rule"===t.kind){if(0===t.nodes.length)return""+n+t.name+` ${t.params};
`;a+=""+n+t.name+(t.params?` ${t.params} `:" ")+`{
`;for(var o of t.nodes)a+=e(o,r+1);a+=n+`}
`}else if("comment"===t.kind)a+=`${n}/*${t.value}*/
`;else if("context"===t.kind||"at-root"===t.kind)return"";return a}(r);""!==r&&(t+=r)}return t}function tt(e){return{kind:"word",value:e}}function Jr(e,t){return{kind:"function",value:e,nodes:t}}function Yr(e){return{kind:"separator",value:e}}function ce(r,e,a=null){for(let t=0;t<r.length;t++){var n=r[t],i=e(n,{parent:a,replaceWith(e){Array.isArray(e)?0===e.length?r.splice(t,1):1===e.length?r[t]=e[0]:r.splice(t,1,...e):r[t]=e,t--}})??0;if(2===i)return 2;if(1!==i&&"function"===n.kind&&2===ce(n.nodes,e,n))return 2}}function q(e){let t="";for(var r of e)switch(r.kind){case"word":case"separator":t+=r.value;break;case"function":t+=r.value+"("+q(r.nodes)+")"}return t}var Zr=92,Qr=41,kt=58,xt=44,Xr=34,At=61,Ct=62,$t=60,Nt=10,en=40,tn=39,Tt=47,Vt=32,St=9;function M(a){a=a.replaceAll(`\r
`,`
`);let n=[],t=[],i=null,o="",l;for(let r=0;r<a.length;r++){var s,u=a.charCodeAt(r);switch(u){case kt:case xt:case At:case Ct:case $t:case Nt:case Tt:case Vt:case St:{0<o.length&&(c=tt(o),(i?i.nodes:n).push(c),o="");let e=r,t=r+1;for(;t<a.length&&((l=a.charCodeAt(t))===kt||l===xt||l===At||l===Ct||l===$t||l===Nt||l===Tt||l===Vt||l===St);t++);r=t-1;var c=Yr(a.slice(e,t));(i?i.nodes:n).push(c);break}case tn:case Xr:var e=r;for(let e=r+1;e<a.length;e++)if((l=a.charCodeAt(e))===Zr)e+=1;else if(l===u){r=e;break}o+=a.slice(e,r+1);break;case en:e=Jr(o,[]);o="",(i?i.nodes:n).push(e),t.push(e),i=e;break;case Qr:{let e=t.pop();0<o.length&&(s=tt(o),e.nodes.push(s),o=""),i=0<t.length?t[t.length-1]:null;break}default:o+=String.fromCharCode(u)}}return 0<o.length&&n.push(tt(o)),n}var rt=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],De=["anchor-size"],Et=new RegExp(`(${De.join("|")})\\(`,"g");function ke(t){return-1!==t.indexOf("(")&&rt.some(e=>t.includes(e+"("))}function Rt(a){if(!rt.some(e=>a.includes(e)))return a;let r=!1,e=(De.some(e=>a.includes(e))&&(Et.lastIndex=0,a=a.replace(Et,(e,t)=>(r=!0,`$${De.indexOf(t)}$(`))),""),n=[];for(let r=0;r<a.length;r++){var t,i=a[r];if("("===i){e+=i;let t=r;for(let e=r-1;0<=e;e--){var o=a.charCodeAt(e);if(48<=o&&o<=57)t=e;else{if(!(97<=o&&o<=122))break;t=e}}var l=a.slice(t,r);rt.includes(l)?n.unshift(!0):n[0]&&""===l?n.unshift(!0):n.unshift(!1)}else")"===i?(e+=i,n.shift()):","===i&&n[0]?e+=", ":" "===i&&n[0]&&" "===e[e.length-1]||("+"!==i&&"*"!==i&&"/"!==i&&"-"!==i||!n[0]?n[0]&&a.startsWith("to-zero",r)?(l=r,r+=7,e+=a.slice(l,r+1)):e+=i:"+"===(t=(t=e.trimEnd())[t.length-1])||"*"===t||"/"===t||"-"===t||"("===t||","===t?e+=i:" "===a[r-1]?e+=i+" ":e+=` ${i} `)}return r?e.replace(/\$(\d+)\$/g,(e,t)=>De[t]??e):e}function X(e){if(-1===e.indexOf("("))return xe(e);var t=M(e);return nt(t),e=Rt(e=q(t))}function xe(t){let r="";for(let e=0;e<t.length;e++){var a=t[e];"\\"===a&&"_"===t[e+1]?(r+="_",e+=1):r+="_"===a?" ":a}return r}function nt(e){for(var t of e)switch(t.kind){case"function":if("url"===t.value||t.value.endsWith("_url")){t.value=xe(t.value);break}if("var"===t.value||t.value.endsWith("_var")||"theme"===t.value||t.value.endsWith("_theme")){t.value=xe(t.value);for(let e=0;e<t.nodes.length;e++)0==e&&"word"===t.nodes[e].kind||nt([t.nodes[e]]);break}t.value=xe(t.value),nt(t.nodes);break;case"separator":case"word":t.value=xe(t.value);break;default:rn(t)}}function rn(e){throw new Error("Unexpected value: "+e)}var je=new Uint8Array(256);function _(t,e){let r=0,a=[],n=0,i=t.length,o=e.charCodeAt(0);for(let e=0;e<i;e++){var l=t.charCodeAt(e);if(0===r&&l===o)a.push(t.slice(n,e)),n=e+1;else switch(l){case 92:e+=1;break;case 39:case 34:for(;++e<i;){var s=t.charCodeAt(e);if(92===s)e+=1;else if(s===l)break}break;case 40:je[r]=41,r++;break;case 91:je[r]=93,r++;break;case 123:je[r]=125,r++;break;case 93:case 125:case 41:0<r&&l===je[r-1]&&r--}}return a.push(t.slice(n)),a}var nn=58,Ot=45,Kt=97,_t=122;function*Pt(t,n){let r=_(t,":");if(n.theme.prefix){if(1===r.length||r[0]!==n.theme.prefix)return null;r.shift()}let e=r.pop(),i=[];for(let e=r.length-1;0<=e;--e){var a=n.parseVariant(r[e]);if(null===a)return;i.push(a)}let o=!1,[l,s=null,u]=("!"===e[e.length-1]?(o=!0,e=e.slice(0,-1)):"!"===e[0]&&(o=!0,e=e.slice(1)),n.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:i,important:o,raw:t}),_(e,"/"));if(!u){var c=null===s?null:it(s);if(null===s||null!==c){if("["===l[0]){if("]"!==l[l.length-1])return;var d=l.charCodeAt(1);if(d!==Ot&&!(Kt<=d&&d<=_t))return;d=(l=l.slice(1,-1)).indexOf(":");return-1===d||0===d||d===l.length-1?void 0:void(yield{kind:"arbitrary",property:l.slice(0,d),value:X(l.slice(d+1)),modifier:c,variants:i,important:o,raw:t})}let a;if("]"===l[l.length-1]){d=l.indexOf("-[");if(-1===d)return;var f=l.slice(0,d);if(!n.utilities.has(f,"functional"))return;d=l.slice(d+1);a=[[f,d]]}else if(")"===l[l.length-1]){f=l.indexOf("-(");if(-1===f)return;d=l.slice(0,f);if(!n.utilities.has(d,"functional"))return;let e=l.slice(f+2,-1),t=_(e,":"),r=null;if(2===t.length&&(r=t[0],e=t[1]),"-"!==e[0]&&"-"!==e[1])return;a=[[d,null===r?`[var(${e})]`:`[${r}:var(${e})]`]]}else a=jt(l,e=>n.utilities.has(e,"functional"));for(var[p,h]of a){let e={kind:"functional",root:p,modifier:c,value:null,variants:i,important:o,raw:t};if(null===h)yield e;else{p=h.indexOf("[");if(-1!==p){if("]"!==h[h.length-1])return;let t=X(h.slice(p+1,-1)),r="";for(let e=0;e<t.length;e++){var m=t.charCodeAt(e);if(m===nn){r=t.slice(0,e),t=t.slice(e+1);break}if(!(m===Ot||Kt<=m&&m<=_t))break}if(0===t.length||0===t.trim().length)continue;e.value={kind:"arbitrary",dataType:r||null,value:t}}else{p=null===s||"arbitrary"===e.modifier?.kind?null:h+"/"+s;e.value={kind:"named",value:h,fraction:p}}yield e}}}}}function it(t){if("["===t[0]&&"]"===t[t.length-1]){let e=X(t.slice(1,-1));return 0===e.length||0===e.trim().length?null:{kind:"arbitrary",value:e}}if("("!==t[0]||")"!==t[t.length-1])return{kind:"named",value:t};{let e=X(t.slice(1,-1));return 0===e.length||0===e.trim().length||"-"!==e[0]&&"-"!==e[1]?null:{kind:"arbitrary",value:`var(${e})`}}}function Dt(t,r){if("["===t[0]&&"]"===t[t.length-1]){if("@"===t[1]&&t.includes("&"))return null;let e=X(t.slice(1,-1));if(0===e.length||0===e.trim().length)return null;var a=">"===e[0]||"+"===e[0]||"~"===e[0];return{kind:"arbitrary",selector:e=a||"@"===e[0]||e.includes("&")?e:`&:is(${e})`,relative:a}}var n,i,[a,e=null,t]=_(t,"/");if(t)return null;for([n,i]of jt(a,e=>r.variants.has(e)))switch(r.variants.kind(n)){case"static":return null!==i||null!==e?null:{kind:"static",root:n};case"functional":var o=null===e?null:it(e);if(null!==e&&null===o)return null;if(null===i)return{kind:"functional",root:n,modifier:o,value:null};if("]"===i[i.length-1]){if("["!==i[0])continue;let e=X(i.slice(1,-1));return 0===e.length||0===e.trim().length?null:{kind:"functional",root:n,modifier:o,value:{kind:"arbitrary",value:e}}}if(")"!==i[i.length-1])return{kind:"functional",root:n,modifier:o,value:{kind:"named",value:i}};{if("("!==i[0])continue;let e=X(i.slice(1,-1));return 0===e.length||0===e.trim().length||"-"!==e[0]&&"-"!==e[1]?null:{kind:"functional",root:n,modifier:o,value:{kind:"arbitrary",value:`var(${e})`}}}case"compound":if(null===i)return null;o=r.parseVariant(i);if(null===o||!r.variants.compoundsWith(n,o))return null;var l=null===e?null:it(e);return null!==e&&null===l?null:{kind:"compound",root:n,modifier:l,variant:o}}return null}function*jt(e,t){t(e)&&(yield[e,null]);let r=e.lastIndexOf("-");if(-1===r)"@"===e[0]&&t("@")&&(yield["@",e.slice(1)]);else do{var a=e.slice(0,r);if(t(a)){a=[a,e.slice(r+1)];if(""===a[1])break;yield a}}while(0<(r=e.lastIndexOf("-",r-1)))}function le(e,t,r){if(e===t)return 0;var a=e.indexOf("("),n=t.indexOf("("),a=-1===a?e.replace(/[\d.]+/g,""):e.slice(0,a),n=-1===n?t.replace(/[\d.]+/g,""):t.slice(0,n),a=(a===n?0:a<n?-1:1)||("asc"===r?parseInt(e)-parseInt(t):parseInt(t)-parseInt(e));return Number.isNaN(a)?e<t?-1:1:a}var I=class extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e,this),this.set(e,t)),t}},on=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),ln=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function Ut(e){return 35===e.charCodeAt(0)||ln.test(e)||on.has(e.toLowerCase())}var an={color:Ut,length:lt,percentage:ot,ratio:kn,number:yn,integer:N,url:Ft,position:Cn,"bg-size":$n,"line-width":un,image:dn,"family-name":gn,"generic-name":pn,"absolute-size":mn,"relative-size":hn,angle:Vn,vector:En};function z(e,t){if(e.startsWith("var("))return null;for(var r of t)if(an[r]?.(e))return r;return null}var sn=/^url\(.*\)$/;function Ft(e){return sn.test(e)}function un(e){return"thin"===e||"medium"===e||"thick"===e}var cn=/^(?:element|image|cross-fade|image-set)\(/,fn=/^(repeating-)?(conic|linear|radial)-gradient\(/;function dn(e){let t=0;for(var r of _(e,","))if(!r.startsWith("var(")){if(Ft(r)){t+=1;continue}if(fn.test(r)){t+=1;continue}if(cn.test(r)){t+=1;continue}return!1}return 0<t}function pn(e){return"serif"===e||"sans-serif"===e||"monospace"===e||"cursive"===e||"fantasy"===e||"system-ui"===e||"ui-serif"===e||"ui-sans-serif"===e||"ui-monospace"===e||"ui-rounded"===e||"math"===e||"emoji"===e||"fangsong"===e}function gn(e){let t=0;for(var r of _(e,",")){var a=r.charCodeAt(0);if(48<=a&&a<=57)return!1;r.startsWith("var(")||(t+=1)}return 0<t}function mn(e){return"xx-small"===e||"x-small"===e||"small"===e||"medium"===e||"large"===e||"x-large"===e||"xx-large"===e||"xxx-large"===e}function hn(e){return"larger"===e||"smaller"===e}var Q=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,vn=new RegExp(`^${Q.source}$`);function yn(e){return vn.test(e)||ke(e)}var bn=new RegExp(`^${Q.source}%$`);function ot(e){return bn.test(e)||ke(e)}var wn=new RegExp(`^${Q.source}s*/s*${Q.source}$`);function kn(e){return wn.test(e)||ke(e)}var xn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],An=new RegExp(`^${Q.source}(${xn.join("|")})$`);function lt(e){return An.test(e)||ke(e)}function Cn(e){let t=0;for(var r of _(e," "))if("center"===r||"top"===r||"right"===r||"bottom"===r||"left"===r)t+=1;else if(!r.startsWith("var(")){if(!lt(r)&&!ot(r))return!1;t+=1}return 0<t}function $n(e){let t=0;for(var r of _(e,","))if("cover"===r||"contain"===r)t+=1;else{let e=_(r," ");if(1!==e.length&&2!==e.length)return!1;e.every(e=>"auto"===e||lt(e)||ot(e))&&(t+=1)}return 0<t}var Nn=["deg","rad","grad","turn"],Tn=new RegExp(`^${Q.source}(${Nn.join("|")})$`);function Vn(e){return Tn.test(e)}var Sn=new RegExp(`^${Q.source} +${Q.source} +${Q.source}$`);function En(e){return Sn.test(e)}function N(e){var t=Number(e);return Number.isInteger(t)&&0<=t&&String(t)===String(e)}function fe(e){return zt(e,.25)}function Ue(e){return zt(e,.25)}function zt(e,t){var r=Number(e);return 0<=r&&r%t==0&&String(r)===String(e)}var Rn=new Set(["inset","inherit","initial","revert","unset"]),It=/^-?(\d+|\.\d+)(.*?)$/g;function ae(e,l){return _(e,",").map(e=>{let t=_(e=e.trim()," ").filter(e=>""!==e.trim()),r=null,a=null,n=null;for(var i of t)Rn.has(i)||(It.test(i)?(null===a?a=i:null===n&&(n=i),It.lastIndex=0):null===r&&(r=i));if(null===a||null===n)return e;var o=l(r??"currentcolor");return null!==r?e.replace(r,o):e+" "+o}).join(", ")}var On=/^-?[a-z][a-zA-Z0-9/%._-]*$/,Kn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,at=class{utilities=new I(()=>[]);completions=new Map;static(e,t){this.utilities.get(e).push({kind:"static",compileFn:t})}functional(e,t,r){this.utilities.get(e).push({kind:"functional",compileFn:t,options:r})}has(e,t){return this.utilities.has(e)&&this.utilities.get(e).some(e=>e.kind===t)}get(e){return this.utilities.has(e)?this.utilities.get(e):[]}getCompletions(e){return this.completions.get(e)?.()??[]}suggest(e,t){this.completions.set(e,t)}keys(e){let t=[];for(var[r,a]of this.utilities.entries())for(var n of a)if(n.kind===e){t.push(r);break}return t}};function $(e,t,r){return P("@property",e,[a("syntax",r?`"${r}"`:'"*"'),a("inherits","false"),...t?[a("initial-value",t)]:[]])}function J(e,t){if(null===t)return e;var r=Number(t);return`color-mix(in oklab, ${e} ${t=Number.isNaN(r)?t:100*r+"%"}, transparent)`}function W(e,t,r){if(!t)return e;if("arbitrary"===t.kind)return J(e,t.value);r=r.resolve(t.value,["--opacity"]);return r?J(e,r):Ue(t.value)?J(e,t.value+"%"):null}function H(e,t,r){let a=null;switch(e.value.value){case"inherit":a="inherit";break;case"transparent":a="transparent";break;case"current":a="currentColor";break;default:a=t.resolve(e.value.value,r)}return a?W(a,e.modifier,t):null}function Mt(u){let c=new at;function d(e,t){function*n(e){for(var t of u.keysInNamespaces(e))yield t.replaceAll("_",".")}c.suggest(e,()=>{let r=[];for(var a of t())if("string"==typeof a)r.push({values:[a],modifiers:[]});else{let e=[...a.values??[],...n(a.valueThemeKeys??[])],t=[...a.modifiers??[],...n(a.modifierThemeKeys??[])];a.hasDefaultValue&&e.unshift(null),r.push({supportsNegative:a.supportsNegative,values:e,modifiers:t})}return r})}function f(e,t){c.static(e,()=>t.map(e=>"function"==typeof e?e():a(e[0],e[1])))}function o(e,i){function t({negative:n}){return e=>{let t=null;if(e.value)if("arbitrary"===e.value.kind){if(e.modifier)return;t=e.value.value}else{if(null===(t=u.resolve(e.value.fraction??e.value.value,i.themeKeys??[]))&&i.supportsFractions&&e.value.fraction){var[r,a]=_(e.value.fraction,"/");if(!N(r)||!N(a))return;t=`calc(${e.value.fraction} * 100%)`}if(null===t&&n&&i.handleNegativeBareValue){if(!(t=i.handleNegativeBareValue(e.value))?.includes("/")&&e.modifier)return;if(null!==t)return i.handle(t)}if(null===t&&i.handleBareValue&&(!(t=i.handleBareValue(e.value))?.includes("/")&&e.modifier))return}else{if(e.modifier)return;t=void 0!==i.defaultValue?i.defaultValue:u.resolve(null,i.themeKeys??[])}if(null!==t)return i.handle(n?`calc(${t} * -1)`:t)}}i.supportsNegative&&c.functional("-"+e,t({negative:!0})),c.functional(e,t({negative:!1})),d(e,()=>[{supportsNegative:i.supportsNegative,valueThemeKeys:i.themeKeys??[],hasDefaultValue:void 0!==i.defaultValue&&null!==i.defaultValue}])}function e(e,r){c.functional(e,t=>{if(t.value){let e=null;return null!==(e="arbitrary"===t.value.kind?W(e=t.value.value,t.modifier,u):H(t,u,r.themeKeys))?r.handle(e):void 0}}),d(e,()=>[{values:["current","inherit","transparent"],valueThemeKeys:r.themeKeys,modifiers:Array.from({length:21},(e,t)=>""+5*t)}])}function n(e,t,r,{supportsNegative:a=!1,supportsFractions:n=!1}={}){a&&c.static(`-${e}-px`,()=>r("-1px")),c.static(e+"-px",()=>r("1px")),o(e,{themeKeys:t,supportsFractions:n,supportsNegative:a,defaultValue:null,handleBareValue:({value:e})=>{var t=u.resolve(null,["--spacing"]);return t&&fe(e)?`calc(${t} * ${e})`:null},handleNegativeBareValue:({value:e})=>{var t=u.resolve(null,["--spacing"]);return t&&fe(e)?`calc(${t} * -${e})`:null},handle:r}),d(e,()=>[{values:u.get(["--spacing"])?["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"]:[],supportsNegative:a,valueThemeKeys:t}])}f("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),f("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),f("pointer-events-none",[["pointer-events","none"]]),f("pointer-events-auto",[["pointer-events","auto"]]),f("visible",[["visibility","visible"]]),f("invisible",[["visibility","hidden"]]),f("collapse",[["visibility","collapse"]]),f("static",[["position","static"]]),f("fixed",[["position","fixed"]]),f("absolute",[["position","absolute"]]),f("relative",[["position","relative"]]),f("sticky",[["position","sticky"]]);for(let[e,t]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])f(e+"-auto",[[t,"auto"]]),f(e+"-full",[[t,"100%"]]),f(`-${e}-full`,[[t,"-100%"]]),n(e,["--inset","--spacing"],e=>[a(t,e)],{supportsNegative:!0,supportsFractions:!0});f("isolate",[["isolation","isolate"]]),f("isolation-auto",[["isolation","auto"]]),f("z-auto",[["z-index","auto"]]),o("z",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--z-index"],handle:e=>[a("z-index",e)]}),d("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),f("order-first",[["order","calc(-infinity)"]]),f("order-last",[["order","calc(infinity)"]]),f("order-none",[["order","0"]]),o("order",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--order"],handle:e=>[a("order",e)]}),d("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:["--order"]}]),f("col-auto",[["grid-column","auto"]]),o("col",{themeKeys:["--grid-column"],handle:e=>[a("grid-column",e)]}),f("col-span-full",[["grid-column","1 / -1"]]),o("col-span",{handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("grid-column",`span ${e} / span `+e)]}),f("col-start-auto",[["grid-column-start","auto"]]),o("col-start",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--grid-column-start"],handle:e=>[a("grid-column-start",e)]}),f("col-end-auto",[["grid-column-end","auto"]]),o("col-end",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--grid-column-end"],handle:e=>[a("grid-column-end",e)]}),d("col-span",()=>[{values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:[]}]),d("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-column-start"]}]),d("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-column-end"]}]),f("row-auto",[["grid-row","auto"]]),o("row",{themeKeys:["--grid-row"],handle:e=>[a("grid-row",e)]}),f("row-span-full",[["grid-row","1 / -1"]]),o("row-span",{themeKeys:[],handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("grid-row",`span ${e} / span `+e)]}),f("row-start-auto",[["grid-row-start","auto"]]),o("row-start",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--grid-row-start"],handle:e=>[a("grid-row-start",e)]}),f("row-end-auto",[["grid-row-end","auto"]]),o("row-end",{supportsNegative:!0,handleBareValue:({value:e})=>N(e)?e:null,themeKeys:["--grid-row-end"],handle:e=>[a("grid-row-end",e)]}),d("row-span",()=>[{values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:[]}]),d("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-row-start"]}]),d("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-row-end"]}]),f("float-start",[["float","inline-start"]]),f("float-end",[["float","inline-end"]]),f("float-right",[["float","right"]]),f("float-left",[["float","left"]]),f("float-none",[["float","none"]]),f("clear-start",[["clear","inline-start"]]),f("clear-end",[["clear","inline-end"]]),f("clear-right",[["clear","right"]]),f("clear-left",[["clear","left"]]),f("clear-both",[["clear","both"]]),f("clear-none",[["clear","none"]]);for(let[e,t]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])f(e+"-auto",[[t,"auto"]]),n(e,["--margin","--spacing"],e=>[a(t,e)],{supportsNegative:!0});f("box-border",[["box-sizing","border-box"]]),f("box-content",[["box-sizing","content-box"]]),f("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),o("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("overflow","hidden"),a("display","-webkit-box"),a("-webkit-box-orient","vertical"),a("-webkit-line-clamp",e)]}),d("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),f("block",[["display","block"]]),f("inline-block",[["display","inline-block"]]),f("inline",[["display","inline"]]),f("hidden",[["display","none"]]),f("inline-flex",[["display","inline-flex"]]),f("table",[["display","table"]]),f("inline-table",[["display","inline-table"]]),f("table-caption",[["display","table-caption"]]),f("table-cell",[["display","table-cell"]]),f("table-column",[["display","table-column"]]),f("table-column-group",[["display","table-column-group"]]),f("table-footer-group",[["display","table-footer-group"]]),f("table-header-group",[["display","table-header-group"]]),f("table-row-group",[["display","table-row-group"]]),f("table-row",[["display","table-row"]]),f("flow-root",[["display","flow-root"]]),f("flex",[["display","flex"]]),f("grid",[["display","grid"]]),f("inline-grid",[["display","inline-grid"]]),f("contents",[["display","contents"]]),f("list-item",[["display","list-item"]]),f("field-sizing-content",[["field-sizing","content"]]),f("field-sizing-fixed",[["field-sizing","fixed"]]),f("aspect-auto",[["aspect-ratio","auto"]]),f("aspect-square",[["aspect-ratio","1 / 1"]]),o("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:e})=>{if(null===e)return null;var[t,r]=_(e,"/");return N(t)&&N(r)?e:null},handle:e=>[a("aspect-ratio",e)]});for(var[t,r]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])f("size-"+t,[["--tw-sort","size"],["width",r],["height",r]]),f("w-"+t,[["width",r]]),f("min-w-"+t,[["min-width",r]]),f("max-w-"+t,[["max-width",r]]),f("h-"+t,[["height",r]]),f("min-h-"+t,[["min-height",r]]),f("max-h-"+t,[["max-height",r]]);f("w-screen",[["width","100vw"]]),f("min-w-screen",[["min-width","100vw"]]),f("max-w-screen",[["max-width","100vw"]]),f("h-screen",[["height","100vh"]]),f("min-h-screen",[["min-height","100vh"]]),f("max-h-screen",[["max-height","100vh"]]),f("max-w-none",[["max-width","none"]]),f("max-h-none",[["max-height","none"]]),n("size",["--size","--spacing"],e=>[a("--tw-sort","size"),a("width",e),a("height",e)],{supportsFractions:!0});for(let[e,t,r]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])n(e,t,e=>[a(r,e)],{supportsFractions:!0});c.static("container",()=>{let e=[...u.namespace("--breakpoint").values()],t=(e.sort((e,t)=>le(e,t,"asc")),[a("--tw-sort","--tw-container-component"),a("width","100%")]);for(var r of e)t.push(P("@media",`(width >= ${r})`,[a("max-width",r)]));return t}),f("flex-auto",[["flex","auto"]]),f("flex-initial",[["flex","0 auto"]]),f("flex-none",[["flex","none"]]),c.functional("flex",e=>{var t,r;if(e.value)return"arbitrary"===e.value.kind?e.modifier?void 0:[a("flex",e.value.value)]:e.value.fraction?([t,r]=_(e.value.fraction,"/"),N(t)&&N(r)?[a("flex",`calc(${e.value.fraction} * 100%)`)]:void 0):!N(e.value.value)||e.modifier?void 0:[a("flex",e.value.value)]}),o("shrink",{defaultValue:"1",handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("flex-shrink",e)]}),o("grow",{defaultValue:"1",handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("flex-grow",e)]}),d("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),d("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),f("basis-auto",[["flex-basis","auto"]]),f("basis-full",[["flex-basis","100%"]]),n("basis",["--flex-basis","--spacing","--container"],e=>[a("flex-basis",e)],{supportsFractions:!0}),f("table-auto",[["table-layout","auto"]]),f("table-fixed",[["table-layout","fixed"]]),f("caption-top",[["caption-side","top"]]),f("caption-bottom",[["caption-side","bottom"]]),f("border-collapse",[["border-collapse","collapse"]]),f("border-separate",[["border-collapse","separate"]]);let i=()=>j([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]),l=(n("border-spacing",["--border-spacing","--spacing"],e=>[i(),a("--tw-border-spacing-x",e),a("--tw-border-spacing-y",e),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),n("border-spacing-x",["--border-spacing","--spacing"],e=>[i(),a("--tw-border-spacing-x",e),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),n("border-spacing-y",["--border-spacing","--spacing"],e=>[i(),a("--tw-border-spacing-y",e),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),f("origin-center",[["transform-origin","center"]]),f("origin-top",[["transform-origin","top"]]),f("origin-top-right",[["transform-origin","top right"]]),f("origin-right",[["transform-origin","right"]]),f("origin-bottom-right",[["transform-origin","bottom right"]]),f("origin-bottom",[["transform-origin","bottom"]]),f("origin-bottom-left",[["transform-origin","bottom left"]]),f("origin-left",[["transform-origin","left"]]),f("origin-top-left",[["transform-origin","top left"]]),o("origin",{themeKeys:["--transform-origin"],handle:e=>[a("transform-origin",e)]}),f("perspective-origin-center",[["perspective-origin","center"]]),f("perspective-origin-top",[["perspective-origin","top"]]),f("perspective-origin-top-right",[["perspective-origin","top right"]]),f("perspective-origin-right",[["perspective-origin","right"]]),f("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),f("perspective-origin-bottom",[["perspective-origin","bottom"]]),f("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),f("perspective-origin-left",[["perspective-origin","left"]]),f("perspective-origin-top-left",[["perspective-origin","top left"]]),o("perspective-origin",{themeKeys:["--perspective-origin"],handle:e=>[a("perspective-origin",e)]}),f("perspective-none",[["perspective","none"]]),o("perspective",{themeKeys:["--perspective"],handle:e=>[a("perspective",e)]}),()=>j([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]));f("translate-none",[["translate","none"]]),f("-translate-full",[l,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),f("translate-full",[l,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),n("translate",["--translate","--spacing"],e=>[l(),a("--tw-translate-x",e),a("--tw-translate-y",e),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let t of["x","y"])f(`-translate-${t}-full`,[l,["--tw-translate-"+t,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),f(`translate-${t}-full`,[l,["--tw-translate-"+t,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),n("translate-"+t,["--translate","--spacing"],e=>[l(),a("--tw-translate-"+t,e),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});n("translate-z",["--translate","--spacing"],e=>[l(),a("--tw-translate-z",e),a("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),f("-translate-z-px",[l,["--tw-translate-z","-1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),f("translate-z-px",[l,["--tw-translate-z","1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),f("translate-3d",[l,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let s=()=>j([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);function p({negative:r}){return t=>{if(t.value&&!t.modifier){let e;return"arbitrary"===t.value.kind?[a("scale",e=t.value.value)]:(e=!(e=u.resolve(t.value.value,["--scale"]))&&N(t.value.value)?t.value.value+"%":e)?(e=r?`calc(${e} * -1)`:e,[s(),a("--tw-scale-x",e),a("--tw-scale-y",e),a("--tw-scale-z",e),a("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0}}}f("scale-none",[["scale","none"]]),c.functional("-scale",p({negative:!0})),c.functional("scale",p({negative:!1})),d("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let t of["x","y","z"])o("scale-"+t,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[s(),a("--tw-scale-"+t,e),a("scale","var(--tw-scale-x) var(--tw-scale-y)"+("z"===t?" var(--tw-scale-z)":""))]}),d("scale-"+t,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);function h({negative:n}){return t=>{if(t.value&&!t.modifier){let e;if("arbitrary"===t.value.kind){e=t.value.value;var r=t.value.dataType??z(e,["angle","vector"]);if("vector"===r)return[a("rotate",e+" var(--tw-rotate)")];if("angle"!==r)return[a("rotate",e)]}else if(!(e=!(e=u.resolve(t.value.value,["--rotate"]))&&N(t.value.value)?t.value.value+"deg":e))return;return[a("rotate",n?`calc(${e} * -1)`:e)]}}}f("scale-3d",[s,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),f("rotate-none",[["rotate","none"]]),c.functional("-rotate",h({negative:!0})),c.functional("rotate",h({negative:!1})),d("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let r=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),n=()=>j([$("--tw-rotate-x","rotateX(0)"),$("--tw-rotate-y","rotateY(0)"),$("--tw-rotate-z","rotateZ(0)"),$("--tw-skew-x","skewX(0)"),$("--tw-skew-y","skewY(0)")]);for(let t of["x","y","z"])o("rotate-"+t,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[n(),a("--tw-rotate-"+t,`rotate${t.toUpperCase()}(${e})`),a("transform",r)]}),d("rotate-"+t,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);o("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[n(),a("--tw-skew-x",`skewX(${e})`),a("--tw-skew-y",`skewY(${e})`),a("transform",r)]}),o("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[n(),a("--tw-skew-x",`skewX(${e})`),a("transform",r)]}),o("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[n(),a("--tw-skew-y",`skewY(${e})`),a("transform",r)]}),d("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),d("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),d("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),c.functional("transform",t=>{if(!t.modifier){let e=null;return t.value?"arbitrary"===t.value.kind&&(e=t.value.value):e=r,null!==e?[n(),a("transform",e)]:void 0}}),d("transform",()=>[{hasDefaultValue:!0}]),f("transform-cpu",[["transform",r]]),f("transform-gpu",[["transform","translateZ(0) "+r]]),f("transform-none",[["transform","none"]])}f("transform-flat",[["transform-style","flat"]]),f("transform-3d",[["transform-style","preserve-3d"]]),f("transform-content",[["transform-box","content-box"]]),f("transform-border",[["transform-box","border-box"]]),f("transform-fill",[["transform-box","fill-box"]]),f("transform-stroke",[["transform-box","stroke-box"]]),f("transform-view",[["transform-box","view-box"]]),f("backface-visible",[["backface-visibility","visible"]]),f("backface-hidden",[["backface-visibility","hidden"]]);for(var m of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])f("cursor-"+m,[["cursor",m]]);o("cursor",{themeKeys:["--cursor"],handle:e=>[a("cursor",e)]});for(var v of["auto","none","manipulation"])f("touch-"+v,[["touch-action",v]]);var g,w,b,y=()=>j([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(g of["x","left","right"])f("touch-pan-"+g,[y,["--tw-pan-x","pan-"+g],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(w of["y","up","down"])f("touch-pan-"+w,[y,["--tw-pan-y","pan-"+w],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);f("touch-pinch-zoom",[y,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(b of["none","text","all","auto"])f("select-"+b,[["-webkit-user-select",b],["user-select",b]]);f("resize-none",[["resize","none"]]),f("resize-x",[["resize","horizontal"]]),f("resize-y",[["resize","vertical"]]),f("resize",[["resize","both"]]),f("snap-none",[["scroll-snap-type","none"]]);var k,x,A,T,C,K,E,V=()=>j([$("--tw-scroll-snap-strictness","proximity","*")]);for(k of["x","y","both"])f("snap-"+k,[V,["scroll-snap-type",k+" var(--tw-scroll-snap-strictness)"]]);f("snap-mandatory",[V,["--tw-scroll-snap-strictness","mandatory"]]),f("snap-proximity",[V,["--tw-scroll-snap-strictness","proximity"]]),f("snap-align-none",[["scroll-snap-align","none"]]),f("snap-start",[["scroll-snap-align","start"]]),f("snap-end",[["scroll-snap-align","end"]]),f("snap-center",[["scroll-snap-align","center"]]),f("snap-normal",[["scroll-snap-stop","normal"]]),f("snap-always",[["scroll-snap-stop","always"]]);for(let[e,t]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])n(e,["--scroll-margin","--spacing"],e=>[a(t,e)],{supportsNegative:!0});for(let[e,t]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])n(e,["--scroll-padding","--spacing"],e=>[a(t,e)]);f("list-inside",[["list-style-position","inside"]]),f("list-outside",[["list-style-position","outside"]]),f("list-none",[["list-style-type","none"]]),f("list-disc",[["list-style-type","disc"]]),f("list-decimal",[["list-style-type","decimal"]]),o("list",{themeKeys:["--list-style-type"],handle:e=>[a("list-style-type",e)]}),f("list-image-none",[["list-style-image","none"]]),o("list-image",{themeKeys:["--list-style-image"],handle:e=>[a("list-style-image",e)]}),f("appearance-none",[["appearance","none"]]),f("appearance-auto",[["appearance","auto"]]),f("scheme-normal",[["color-scheme","normal"]]),f("scheme-dark",[["color-scheme","dark"]]),f("scheme-light",[["color-scheme","light"]]),f("scheme-light-dark",[["color-scheme","light dark"]]),f("scheme-only-dark",[["color-scheme","only dark"]]),f("scheme-only-light",[["color-scheme","only light"]]),f("columns-auto",[["columns","auto"]]),o("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:e})=>N(e)?e:null,handle:e=>[a("columns",e)]}),d("columns",()=>[{values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:["--columns","--container"]}]);for(x of["auto","avoid","all","avoid-page","page","left","right","column"])f("break-before-"+x,[["break-before",x]]);for(A of["auto","avoid","avoid-page","avoid-column"])f("break-inside-"+A,[["break-inside",A]]);for(T of["auto","avoid","all","avoid-page","page","left","right","column"])f("break-after-"+T,[["break-after",T]]);f("grid-flow-row",[["grid-auto-flow","row"]]),f("grid-flow-col",[["grid-auto-flow","column"]]),f("grid-flow-dense",[["grid-auto-flow","dense"]]),f("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),f("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),f("auto-cols-auto",[["grid-auto-columns","auto"]]),f("auto-cols-min",[["grid-auto-columns","min-content"]]),f("auto-cols-max",[["grid-auto-columns","max-content"]]),f("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),o("auto-cols",{themeKeys:["--grid-auto-columns"],handle:e=>[a("grid-auto-columns",e)]}),f("auto-rows-auto",[["grid-auto-rows","auto"]]),f("auto-rows-min",[["grid-auto-rows","min-content"]]),f("auto-rows-max",[["grid-auto-rows","max-content"]]),f("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),o("auto-rows",{themeKeys:["--grid-auto-rows"],handle:e=>[a("grid-auto-rows",e)]}),f("grid-cols-none",[["grid-template-columns","none"]]),f("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),o("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:e})=>N(e)?`repeat(${e}, minmax(0, 1fr))`:null,handle:e=>[a("grid-template-columns",e)]}),f("grid-rows-none",[["grid-template-rows","none"]]),f("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),o("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:e})=>N(e)?`repeat(${e}, minmax(0, 1fr))`:null,handle:e=>[a("grid-template-rows",e)]}),d("grid-cols",()=>[{values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-template-columns"]}]),d("grid-rows",()=>[{values:Array.from({length:12},(e,t)=>""+(t+1)),valueThemeKeys:["--grid-template-rows"]}]),f("flex-row",[["flex-direction","row"]]),f("flex-row-reverse",[["flex-direction","row-reverse"]]),f("flex-col",[["flex-direction","column"]]),f("flex-col-reverse",[["flex-direction","column-reverse"]]),f("flex-wrap",[["flex-wrap","wrap"]]),f("flex-nowrap",[["flex-wrap","nowrap"]]),f("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),f("place-content-center",[["place-content","center"]]),f("place-content-start",[["place-content","start"]]),f("place-content-end",[["place-content","end"]]),f("place-content-between",[["place-content","space-between"]]),f("place-content-around",[["place-content","space-around"]]),f("place-content-evenly",[["place-content","space-evenly"]]),f("place-content-baseline",[["place-content","baseline"]]),f("place-content-stretch",[["place-content","stretch"]]),f("place-items-center",[["place-items","center"]]),f("place-items-start",[["place-items","start"]]),f("place-items-end",[["place-items","end"]]),f("place-items-baseline",[["place-items","baseline"]]),f("place-items-stretch",[["place-items","stretch"]]),f("content-normal",[["align-content","normal"]]),f("content-center",[["align-content","center"]]),f("content-start",[["align-content","flex-start"]]),f("content-end",[["align-content","flex-end"]]),f("content-between",[["align-content","space-between"]]),f("content-around",[["align-content","space-around"]]),f("content-evenly",[["align-content","space-evenly"]]),f("content-baseline",[["align-content","baseline"]]),f("content-stretch",[["align-content","stretch"]]),f("items-center",[["align-items","center"]]),f("items-start",[["align-items","flex-start"]]),f("items-end",[["align-items","flex-end"]]),f("items-baseline",[["align-items","baseline"]]),f("items-stretch",[["align-items","stretch"]]),f("justify-normal",[["justify-content","normal"]]),f("justify-center",[["justify-content","center"]]),f("justify-start",[["justify-content","flex-start"]]),f("justify-end",[["justify-content","flex-end"]]),f("justify-between",[["justify-content","space-between"]]),f("justify-around",[["justify-content","space-around"]]),f("justify-evenly",[["justify-content","space-evenly"]]),f("justify-baseline",[["justify-content","baseline"]]),f("justify-stretch",[["justify-content","stretch"]]),f("justify-items-normal",[["justify-items","normal"]]),f("justify-items-center",[["justify-items","center"]]),f("justify-items-start",[["justify-items","start"]]),f("justify-items-end",[["justify-items","end"]]),f("justify-items-stretch",[["justify-items","stretch"]]),n("gap",["--gap","--spacing"],e=>[a("gap",e)]),n("gap-x",["--gap","--spacing"],e=>[a("column-gap",e)]),n("gap-y",["--gap","--spacing"],e=>[a("row-gap",e)]),n("space-x",["--space","--spacing"],e=>[j([$("--tw-space-x-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","0"),a("margin-inline-start",`calc(${e} * var(--tw-space-x-reverse))`),a("margin-inline-end",`calc(${e} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),n("space-y",["--space","--spacing"],e=>[j([$("--tw-space-y-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","0"),a("margin-block-start",`calc(${e} * var(--tw-space-y-reverse))`),a("margin-block-end",`calc(${e} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),f("space-x-reverse",[()=>j([$("--tw-space-x-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","1")])]),f("space-y-reverse",[()=>j([$("--tw-space-y-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","1")])]),f("accent-auto",[["accent-color","auto"]]),e("accent",{themeKeys:["--accent-color","--color"],handle:e=>[a("accent-color",e)]}),e("caret",{themeKeys:["--caret-color","--color"],handle:e=>[a("caret-color",e)]}),e("divide",{themeKeys:["--divide-color","--color"],handle:e=>[U(":where(& > :not(:last-child))",[a("--tw-sort","divide-color"),a("border-color",e)])]}),f("place-self-auto",[["place-self","auto"]]),f("place-self-start",[["place-self","start"]]),f("place-self-end",[["place-self","end"]]),f("place-self-center",[["place-self","center"]]),f("place-self-stretch",[["place-self","stretch"]]),f("self-auto",[["align-self","auto"]]),f("self-start",[["align-self","flex-start"]]),f("self-end",[["align-self","flex-end"]]),f("self-center",[["align-self","center"]]),f("self-stretch",[["align-self","stretch"]]),f("self-baseline",[["align-self","baseline"]]),f("justify-self-auto",[["justify-self","auto"]]),f("justify-self-start",[["justify-self","flex-start"]]),f("justify-self-end",[["justify-self","flex-end"]]),f("justify-self-center",[["justify-self","center"]]),f("justify-self-stretch",[["justify-self","stretch"]]);for(C of["auto","hidden","clip","visible","scroll"])f("overflow-"+C,[["overflow",C]]),f("overflow-x-"+C,[["overflow-x",C]]),f("overflow-y-"+C,[["overflow-y",C]]);for(K of["auto","contain","none"])f("overscroll-"+K,[["overscroll-behavior",K]]),f("overscroll-x-"+K,[["overscroll-behavior-x",K]]),f("overscroll-y-"+K,[["overscroll-behavior-y",K]]);f("scroll-auto",[["scroll-behavior","auto"]]),f("scroll-smooth",[["scroll-behavior","smooth"]]),f("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),f("text-ellipsis",[["text-overflow","ellipsis"]]),f("text-clip",[["text-overflow","clip"]]),f("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),f("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),f("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),f("whitespace-normal",[["white-space","normal"]]),f("whitespace-nowrap",[["white-space","nowrap"]]),f("whitespace-pre",[["white-space","pre"]]),f("whitespace-pre-line",[["white-space","pre-line"]]),f("whitespace-pre-wrap",[["white-space","pre-wrap"]]),f("whitespace-break-spaces",[["white-space","break-spaces"]]),f("text-wrap",[["text-wrap","wrap"]]),f("text-nowrap",[["text-wrap","nowrap"]]),f("text-balance",[["text-wrap","balance"]]),f("text-pretty",[["text-wrap","pretty"]]),f("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),f("break-words",[["overflow-wrap","break-word"]]),f("break-all",[["word-break","break-all"]]),f("break-keep",[["word-break","keep-all"]]);for(let[e,r]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])f(e+"-none",r.map(e=>[e,"0"])),f(e+"-full",r.map(e=>[e,"calc(infinity * 1px)"])),o(e,{themeKeys:["--radius"],handle:t=>r.map(e=>a(e,t))});f("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),f("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),f("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),f("border-double",[["--tw-border-style","double"],["border-style","double"]]),f("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),f("border-none",[["--tw-border-style","none"],["border-style","none"]]);{var S=function(e,a){c.functional(e,t=>{if(!t.value){if(t.modifier)return;var e=u.get(["--default-border-width"])??"1px",e=a.width(e);return e?[n(),...e]:void 0}if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["color","line-width","length"])){case"line-width":case"length":if(t.modifier)return;var r=a.width(e);return r?[n(),...r]:void 0;default:return null===(e=W(e,t.modifier,u))?void 0:a.color(e)}}var e=H(t,u,["--border-color","--color"]);if(e)return a.color(e);if(!t.modifier)return e=u.resolve(t.value.value,["--border-width"]),e?(e=a.width(e))?[n(),...e]:void 0:N(t.value.value)&&(e=a.width(t.value.value+"px"))?[n(),...e]:void 0}),d(e,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};let n=()=>j([$("--tw-border-style","solid")]);S("border",{width:e=>[a("border-style","var(--tw-border-style)"),a("border-width",e)],color:e=>[a("border-color",e)]}),S("border-x",{width:e=>[a("border-inline-style","var(--tw-border-style)"),a("border-inline-width",e)],color:e=>[a("border-inline-color",e)]}),S("border-y",{width:e=>[a("border-block-style","var(--tw-border-style)"),a("border-block-width",e)],color:e=>[a("border-block-color",e)]}),S("border-s",{width:e=>[a("border-inline-start-style","var(--tw-border-style)"),a("border-inline-start-width",e)],color:e=>[a("border-inline-start-color",e)]}),S("border-e",{width:e=>[a("border-inline-end-style","var(--tw-border-style)"),a("border-inline-end-width",e)],color:e=>[a("border-inline-end-color",e)]}),S("border-t",{width:e=>[a("border-top-style","var(--tw-border-style)"),a("border-top-width",e)],color:e=>[a("border-top-color",e)]}),S("border-r",{width:e=>[a("border-right-style","var(--tw-border-style)"),a("border-right-width",e)],color:e=>[a("border-right-color",e)]}),S("border-b",{width:e=>[a("border-bottom-style","var(--tw-border-style)"),a("border-bottom-width",e)],color:e=>[a("border-bottom-color",e)]}),S("border-l",{width:e=>[a("border-left-style","var(--tw-border-style)"),a("border-left-width",e)],color:e=>[a("border-left-color",e)]}),o("divide-x",{defaultValue:u.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:e})=>N(e)?e+"px":null,handle:e=>[j([$("--tw-divide-x-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","divide-x-width"),n(),a("--tw-divide-x-reverse","0"),a("border-inline-style","var(--tw-border-style)"),a("border-inline-start-width",`calc(${e} * var(--tw-divide-x-reverse))`),a("border-inline-end-width",`calc(${e} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),o("divide-y",{defaultValue:u.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:e})=>N(e)?e+"px":null,handle:e=>[j([$("--tw-divide-y-reverse","0")]),U(":where(& > :not(:last-child))",[a("--tw-sort","divide-y-width"),n(),a("--tw-divide-y-reverse","0"),a("border-bottom-style","var(--tw-border-style)"),a("border-top-style","var(--tw-border-style)"),a("border-top-width",`calc(${e} * var(--tw-divide-y-reverse))`),a("border-bottom-width",`calc(${e} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),d("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),d("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),f("divide-x-reverse",[()=>j([$("--tw-divide-x-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-divide-x-reverse","1")])]),f("divide-y-reverse",[()=>j([$("--tw-divide-y-reverse","0")]),()=>U(":where(& > :not(:last-child))",[a("--tw-divide-y-reverse","1")])]);for(let e of["solid","dashed","dotted","double","none"])f("divide-"+e,[()=>U(":where(& > :not(:last-child))",[a("--tw-sort","divide-style"),a("--tw-border-style",e),a("border-style",e)])])}f("bg-auto",[["background-size","auto"]]),f("bg-cover",[["background-size","cover"]]),f("bg-contain",[["background-size","contain"]]),f("bg-fixed",[["background-attachment","fixed"]]),f("bg-local",[["background-attachment","local"]]),f("bg-scroll",[["background-attachment","scroll"]]),f("bg-center",[["background-position","center"]]),f("bg-top",[["background-position","top"]]),f("bg-right-top",[["background-position","right top"]]),f("bg-right",[["background-position","right"]]),f("bg-right-bottom",[["background-position","right bottom"]]),f("bg-bottom",[["background-position","bottom"]]),f("bg-left-bottom",[["background-position","left bottom"]]),f("bg-left",[["background-position","left"]]),f("bg-left-top",[["background-position","left top"]]),f("bg-repeat",[["background-repeat","repeat"]]),f("bg-no-repeat",[["background-repeat","no-repeat"]]),f("bg-repeat-x",[["background-repeat","repeat-x"]]),f("bg-repeat-y",[["background-repeat","repeat-y"]]),f("bg-repeat-round",[["background-repeat","round"]]),f("bg-repeat-space",[["background-repeat","space"]]),f("bg-none",[["background-image","none"]]);{let i=function(e){let t="in oklab";if("named"===e?.kind)switch(e.value){case"longer":case"shorter":case"increasing":case"decreasing":t=`in oklch ${e.value} hue`;break;default:t="in "+e.value}else"arbitrary"===e?.kind&&(t=e.value);return t},e=function({negative:r}){return t=>{if(t.value){if("arbitrary"===t.value.kind){if(t.modifier)return;let e=t.value.value;return"angle"!==(t.value.dataType??z(e,["angle"]))?r?void 0:[a("--tw-gradient-position",e+","),a("background-image",`linear-gradient(var(--tw-gradient-stops,${e}))`)]:[a("--tw-gradient-position",(e=r?`calc(${e} * -1)`:""+e)+","),a("background-image",`linear-gradient(var(--tw-gradient-stops,${e}))`)]}let e=t.value.value;if(!r&&n.has(e))e=n.get(e);else{if(!N(e))return;e=r?`calc(${e}deg * -1)`:e+"deg"}t=i(t.modifier);return[a("--tw-gradient-position",e+` ${t},`),a("background-image","linear-gradient(var(--tw-gradient-stops))")]}}},t=function({negative:n}){return e=>{if("arbitrary"===e.value?.kind){if(e.modifier)return;var t=e.value.value;return[a("--tw-gradient-position",t+","),a("background-image",`conic-gradient(var(--tw-gradient-stops,${t}))`)]}t=i(e.modifier);if(!e.value)return[a("--tw-gradient-position",t+","),a("background-image","conic-gradient(var(--tw-gradient-stops))")];let r=e.value.value;return N(r)?[a("--tw-gradient-position",`from ${r=n?`calc(${r} * -1)`:r+"deg"} ${t},`),a("background-image","conic-gradient(var(--tw-gradient-stops))")]:void 0}};i;let r=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],n=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);c.functional("-bg-linear",e({negative:!0})),c.functional("bg-linear",e({negative:!1})),d("bg-linear",()=>[{values:[...n.keys()],modifiers:r},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:r}]),c.functional("-bg-conic",t({negative:!0})),c.functional("bg-conic",t({negative:!1})),d("bg-conic",()=>[{hasDefaultValue:!0,modifiers:r},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:r}]),c.functional("bg-radial",e=>{if(!e.value)return[a("--tw-gradient-position",i(e.modifier)+","),a("background-image","radial-gradient(var(--tw-gradient-stops))")];if("arbitrary"===e.value.kind&&!e.modifier)return e=e.value.value,[a("--tw-gradient-position",e+","),a("background-image",`radial-gradient(var(--tw-gradient-stops,${e}))`)]}),d("bg-radial",()=>[{hasDefaultValue:!0,modifiers:r}])}c.functional("bg",t=>{if(t.value){if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return t.modifier?void 0:[a("background-position",e)];case"bg-size":case"length":case"size":return t.modifier?void 0:[a("background-size",e)];case"image":case"url":return t.modifier?void 0:[a("background-image",e)];default:return null===(e=W(e,t.modifier,u))?void 0:[a("background-color",e)]}}var e=H(t,u,["--background-color","--color"]);if(e)return[a("background-color",e)];if(t.modifier);else return e=u.resolve(t.value.value,["--background-image"]),e?[a("background-image",e)]:void 0}}),d("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:[],valueThemeKeys:["--background-image"]}]);let D=()=>j([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function O(e,r){c.functional(e,t=>{if(t.value){if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:r.position(e);default:return null===(e=W(e,t.modifier,u))?void 0:r.color(e)}}var e=H(t,u,["--background-color","--color"]);if(e)return r.color(e);if(t.modifier);else return e=u.resolve(t.value.value,["--gradient-color-stop-positions"]),e?r.position(e):"%"===t.value.value[t.value.value.length-1]&&N(t.value.value.slice(0,-1))?r.position(t.value.value):void 0}}),d(e,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:Array.from({length:21},(e,t)=>5*t+"%"),valueThemeKeys:["--gradient-color-stop-positions"]}])}O("from",{color:e=>[D(),a("--tw-sort","--tw-gradient-from"),a("--tw-gradient-from",e),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[D(),a("--tw-gradient-from-position",e)]}),f("via-none",[["--tw-gradient-via-stops","initial"]]),O("via",{color:e=>[D(),a("--tw-sort","--tw-gradient-via"),a("--tw-gradient-via",e),a("--tw-gradient-via-stops","var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),a("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:e=>[D(),a("--tw-gradient-via-position",e)]}),O("to",{color:e=>[D(),a("--tw-sort","--tw-gradient-to"),a("--tw-gradient-to",e),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position,) var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[D(),a("--tw-gradient-to-position",e)]}),f("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),f("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),f("bg-clip-text",[["background-clip","text"]]),f("bg-clip-border",[["background-clip","border-box"]]),f("bg-clip-padding",[["background-clip","padding-box"]]),f("bg-clip-content",[["background-clip","content-box"]]),f("bg-origin-border",[["background-origin","border-box"]]),f("bg-origin-padding",[["background-origin","padding-box"]]),f("bg-origin-content",[["background-origin","content-box"]]);for(E of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])f("bg-blend-"+E,[["background-blend-mode",E]]),f("mix-blend-"+E,[["mix-blend-mode",E]]);f("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),f("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),f("fill-none",[["fill","none"]]),c.functional("fill",e=>{if(e.value){if("arbitrary"===e.value.kind)return null===(t=W(e.value.value,e.modifier,u))?void 0:[a("fill",t)];var t=H(e,u,["--fill","--color"]);return t?[a("fill",t)]:void 0}}),d("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)}]),f("stroke-none",[["stroke","none"]]),c.functional("stroke",t=>{if(t.value){if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["color","number","length","percentage"])){case"number":case"length":case"percentage":return t.modifier?void 0:[a("stroke-width",e)];default:return null===(e=W(t.value.value,t.modifier,u))?void 0:[a("stroke",e)]}}var e=H(t,u,["--stroke","--color"]);if(e)return[a("stroke",e)];e=u.resolve(t.value.value,["--stroke-width"]);return e?[a("stroke-width",e)]:N(t.value.value)?[a("stroke-width",t.value.value)]:void 0}}),d("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),f("object-contain",[["object-fit","contain"]]),f("object-cover",[["object-fit","cover"]]),f("object-fill",[["object-fit","fill"]]),f("object-none",[["object-fit","none"]]),f("object-scale-down",[["object-fit","scale-down"]]),f("object-bottom",[["object-position","bottom"]]),f("object-center",[["object-position","center"]]),f("object-left",[["object-position","left"]]),f("object-left-bottom",[["object-position","left bottom"]]),f("object-left-top",[["object-position","left top"]]),f("object-right",[["object-position","right"]]),f("object-right-bottom",[["object-position","right bottom"]]),f("object-right-top",[["object-position","right top"]]),f("object-top",[["object-position","top"]]),o("object",{themeKeys:["--object-position"],handle:e=>[a("object-position",e)]});for(let[e,t]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])n(e,["--padding","--spacing"],e=>[a(t,e)]);f("text-left",[["text-align","left"]]),f("text-center",[["text-align","center"]]),f("text-right",[["text-align","right"]]),f("text-justify",[["text-align","justify"]]),f("text-start",[["text-align","start"]]),f("text-end",[["text-align","end"]]),n("indent",["--text-indent","--spacing"],e=>[a("text-indent",e)],{supportsNegative:!0}),f("align-baseline",[["vertical-align","baseline"]]),f("align-top",[["vertical-align","top"]]),f("align-middle",[["vertical-align","middle"]]),f("align-bottom",[["vertical-align","bottom"]]),f("align-text-top",[["vertical-align","text-top"]]),f("align-text-bottom",[["vertical-align","text-bottom"]]),f("align-sub",[["vertical-align","sub"]]),f("align-super",[["vertical-align","super"]]),o("align",{themeKeys:[],handle:e=>[a("vertical-align",e)]}),c.functional("font",e=>{if(e.value&&!e.modifier){if("arbitrary"===e.value.kind){var t=e.value.value;switch(e.value.dataType??z(t,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[a("font-family",t)];default:return[j([$("--tw-font-weight")]),a("--tw-font-weight",t),a("font-weight",t)]}}var r,n=u.resolveWith(e.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(n)return[n,r={}]=n,[a("font-family",n),a("font-feature-settings",r["--font-feature-settings"]),a("font-variation-settings",r["--font-variation-settings"])];n=u.resolve(e.value.value,["--font-weight"]);return n?[j([$("--tw-font-weight")]),a("--tw-font-weight",n),a("font-weight",n)]:void 0}}),d("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),f("uppercase",[["text-transform","uppercase"]]),f("lowercase",[["text-transform","lowercase"]]),f("capitalize",[["text-transform","capitalize"]]),f("normal-case",[["text-transform","none"]]),f("italic",[["font-style","italic"]]),f("not-italic",[["font-style","normal"]]),f("underline",[["text-decoration-line","underline"]]),f("overline",[["text-decoration-line","overline"]]),f("line-through",[["text-decoration-line","line-through"]]),f("no-underline",[["text-decoration-line","none"]]),f("font-stretch-normal",[["font-stretch","normal"]]),f("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),f("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),f("font-stretch-condensed",[["font-stretch","condensed"]]),f("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),f("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),f("font-stretch-expanded",[["font-stretch","expanded"]]),f("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),f("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),o("font-stretch",{handleBareValue:({value:e})=>{if(!e.endsWith("%"))return null;var t=Number(e.slice(0,-1));return!N(t)||Number.isNaN(t)||t<50||200<t?null:e},handle:e=>[a("font-stretch",e)]}),d("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),e("placeholder",{themeKeys:["--background-color","--color"],handle:e=>[U("&::placeholder",[a("--tw-sort","placeholder-color"),a("color",e)])]}),f("decoration-solid",[["text-decoration-style","solid"]]),f("decoration-double",[["text-decoration-style","double"]]),f("decoration-dotted",[["text-decoration-style","dotted"]]),f("decoration-dashed",[["text-decoration-style","dashed"]]),f("decoration-wavy",[["text-decoration-style","wavy"]]),f("decoration-auto",[["text-decoration-thickness","auto"]]),f("decoration-from-font",[["text-decoration-thickness","from-font"]]),c.functional("decoration",t=>{if(t.value){if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:[a("text-decoration-thickness",e)];default:return null===(e=W(e,t.modifier,u))?void 0:[a("text-decoration-color",e)]}}var e=u.resolve(t.value.value,["--text-decoration-thickness"]);if(e)return t.modifier?void 0:[a("text-decoration-thickness",e)];if(N(t.value.value))return t.modifier?void 0:[a("text-decoration-thickness",t.value.value+"px")];e=H(t,u,["--text-decoration-color","--color"]);return e?[a("text-decoration-color",e)]:void 0}}),d("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),f("animate-none",[["animation","none"]]),o("animate",{themeKeys:["--animate"],handle:e=>[a("animation",e)]});{let t=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),r=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),n=()=>j([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia")]),i=()=>j([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);c.functional("filter",e=>{if(!e.modifier)return null===e.value?[n(),a("filter",t)]:"arbitrary"===e.value.kind?[a("filter",e.value.value)]:"none"===e.value.value?[a("filter","none")]:void 0}),c.functional("backdrop-filter",e=>{if(!e.modifier)return null===e.value?[i(),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]:"arbitrary"===e.value.kind?[a("-webkit-backdrop-filter",e.value.value),a("backdrop-filter",e.value.value)]:"none"===e.value.value?[a("-webkit-backdrop-filter","none"),a("backdrop-filter","none")]:void 0}),o("blur",{themeKeys:["--blur"],handle:e=>[n(),a("--tw-blur",`blur(${e})`),a("filter",t)]}),f("blur-none",[n,["--tw-blur"," "],["filter",t]]),o("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:e=>[i(),a("--tw-backdrop-blur",`blur(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),f("backdrop-blur-none",[i,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",r],["backdrop-filter",r]]),o("brightness",{themeKeys:["--brightness"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[n(),a("--tw-brightness",`brightness(${e})`),a("filter",t)]}),o("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[i(),a("--tw-backdrop-brightness",`brightness(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),d("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),o("contrast",{themeKeys:["--contrast"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[n(),a("--tw-contrast",`contrast(${e})`),a("filter",t)]}),o("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[i(),a("--tw-backdrop-contrast",`contrast(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),d("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),o("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[n(),a("--tw-grayscale",`grayscale(${e})`),a("filter",t)]}),o("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[i(),a("--tw-backdrop-grayscale",`grayscale(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),d("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),o("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[n(),a("--tw-hue-rotate",`hue-rotate(${e})`),a("filter",t)]}),o("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:e})=>N(e)?e+"deg":null,handle:e=>[i(),a("--tw-backdrop-hue-rotate",`hue-rotate(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),d("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),o("invert",{themeKeys:["--invert"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[n(),a("--tw-invert",`invert(${e})`),a("filter",t)]}),o("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[i(),a("--tw-backdrop-invert",`invert(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),d("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),o("saturate",{themeKeys:["--saturate"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[n(),a("--tw-saturate",`saturate(${e})`),a("filter",t)]}),o("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:e})=>N(e)?e+"%":null,handle:e=>[i(),a("--tw-backdrop-saturate",`saturate(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),d("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),o("sepia",{themeKeys:["--sepia"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[n(),a("--tw-sepia",`sepia(${e})`),a("filter",t)]}),o("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:e})=>N(e)?e+"%":null,defaultValue:"100%",handle:e=>[i(),a("--tw-backdrop-sepia",`sepia(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),d("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),f("drop-shadow-none",[n,["--tw-drop-shadow"," "],["filter",t]]),o("drop-shadow",{themeKeys:["--drop-shadow"],handle:e=>[n(),a("--tw-drop-shadow",_(e,",").map(e=>`drop-shadow(${e})`).join(" ")),a("filter",t)]}),o("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:e})=>Ue(e)?e+"%":null,handle:e=>[i(),a("--tw-backdrop-opacity",`opacity(${e})`),a("-webkit-backdrop-filter",r),a("backdrop-filter",r)]}),d("backdrop-opacity",()=>[{values:Array.from({length:21},(e,t)=>""+5*t),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let t=`var(--tw-ease, ${u.resolve(null,["--default-transition-timing-function"])??"ease"})`,r=`var(--tw-duration, ${u.resolve(null,["--default-transition-duration"])??"0s"})`;f("transition-none",[["transition-property","none"]]),f("transition-all",[["transition-property","all"],["transition-timing-function",t],["transition-duration",r]]),f("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",t],["transition-duration",r]]),f("transition-opacity",[["transition-property","opacity"],["transition-timing-function",t],["transition-duration",r]]),f("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",t],["transition-duration",r]]),f("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",t],["transition-duration",r]]),o("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:e=>[a("transition-property",e),a("transition-timing-function",t),a("transition-duration",r)]}),f("transition-discrete",[["transition-behavior","allow-discrete"]]),f("transition-normal",[["transition-behavior","normal"]]),o("delay",{handleBareValue:({value:e})=>N(e)?e+"ms":null,themeKeys:["--transition-delay"],handle:e=>[a("transition-delay",e)]});{let r=()=>j([$("--tw-duration")]);f("duration-initial",[r,["--tw-duration","initial"]]),c.functional("duration",t=>{if(!t.modifier&&t.value){let e=null;return"arbitrary"===t.value.kind?e=t.value.value:null===(e=u.resolve(t.value.fraction??t.value.value,["--transition-duration"]))&&N(t.value.value)&&(e=t.value.value+"ms"),null!==e?[r(),a("--tw-duration",e),a("transition-duration",e)]:void 0}})}d("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),d("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let t=()=>j([$("--tw-ease")]);f("ease-initial",[t,["--tw-ease","initial"]]),f("ease-linear",[t,["--tw-ease","linear"],["transition-timing-function","linear"]]),o("ease",{themeKeys:["--ease"],handle:e=>[t(),a("--tw-ease",e),a("transition-timing-function",e)]})}f("will-change-auto",[["will-change","auto"]]),f("will-change-scroll",[["will-change","scroll-position"]]),f("will-change-contents",[["will-change","contents"]]),f("will-change-transform",[["will-change","transform"]]),o("will-change",{themeKeys:[],handle:e=>[a("will-change",e)]}),f("content-none",[["--tw-content","none"],["content","none"]]),o("content",{themeKeys:[],handle:e=>[j([$("--tw-content",'""')]),a("--tw-content",e),a("content","var(--tw-content)")]});var S="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",F=()=>j([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]),F=(f("contain-none",[["contain","none"]]),f("contain-content",[["contain","content"]]),f("contain-strict",[["contain","strict"]]),f("contain-size",[F,["--tw-contain-size","size"],["contain",S]]),f("contain-inline-size",[F,["--tw-contain-size","inline-size"],["contain",S]]),f("contain-layout",[F,["--tw-contain-layout","layout"],["contain",S]]),f("contain-paint",[F,["--tw-contain-paint","paint"],["contain",S]]),f("contain-style",[F,["--tw-contain-style","style"],["contain",S]]),o("contain",{themeKeys:[],handle:e=>[a("contain",e)]}),f("forced-color-adjust-none",[["forced-color-adjust","none"]]),f("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),f("leading-none",[()=>j([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),n("leading",["--leading","--spacing"],e=>[j([$("--tw-leading")]),a("--tw-leading",e),a("line-height",e)]),o("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:e=>[j([$("--tw-tracking")]),a("--tw-tracking",e),a("letter-spacing",e)]}),f("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),f("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]),"var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)"),S=()=>j([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);f("normal-nums",[["font-variant-numeric","normal"]]),f("ordinal",[S,["--tw-ordinal","ordinal"],["font-variant-numeric",F]]),f("slashed-zero",[S,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",F]]),f("lining-nums",[S,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",F]]),f("oldstyle-nums",[S,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",F]]),f("proportional-nums",[S,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",F]]),f("tabular-nums",[S,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",F]]),f("diagonal-fractions",[S,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",F]]),f("stacked-fractions",[S,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",F]]);{let r=()=>j([$("--tw-outline-style","solid")]);c.static("outline-hidden",()=>[a("outline-style","none"),P("@media","(forced-colors: active)",[a("outline","2px solid transparent"),a("outline-offset","2px")])]),f("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),f("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),f("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),f("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),f("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),c.functional("outline",t=>{if(null===t.value)return t.modifier?void 0:[r(),a("outline-style","var(--tw-outline-style)"),a("outline-width","1px")];if("arbitrary"===t.value.kind){let e=t.value.value;switch(t.value.dataType??z(e,["color","length","number","percentage"])){case"length":case"number":case"percentage":return t.modifier?void 0:[r(),a("outline-style","var(--tw-outline-style)"),a("outline-width",e)];default:return null===(e=W(e,t.modifier,u))?void 0:[a("outline-color",e)]}}var e=H(t,u,["--outline-color","--color"]);if(e)return[a("outline-color",e)];if(!t.modifier)return e=u.resolve(t.value.value,["--outline-width"]),e?[r(),a("outline-style","var(--tw-outline-style)"),a("outline-width",e)]:N(t.value.value)?[r(),a("outline-style","var(--tw-outline-style)"),a("outline-width",t.value.value+"px")]:void 0}),d("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),o("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:e})=>N(e)?e+"px":null,handle:e=>[a("outline-offset",e)]}),d("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}o("opacity",{themeKeys:["--opacity"],handleBareValue:({value:e})=>Ue(e)?e+"%":null,handle:e=>[a("opacity",e)]}),d("opacity",()=>[{values:Array.from({length:21},(e,t)=>""+5*t),valueThemeKeys:["--opacity"]}]),f("underline-offset-auto",[["text-underline-offset","auto"]]),o("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:e})=>N(e)?e+"px":null,handle:e=>[a("text-underline-offset",e)]}),d("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),c.functional("text",r=>{if(r.value){if("arbitrary"===r.value.kind){let t=r.value.value;switch(r.value.dataType??z(t,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":if(r.modifier){let e="arbitrary"===r.modifier.kind?r.modifier.value:u.resolve(r.modifier.value,["--leading"]);if(!e&&fe(r.modifier.value)){var n=u.resolve(null,["--spacing"]);if(!n)return null;e=`calc(${n} * ${r.modifier.value})`}return(e=e||"none"!==r.modifier.value?e:"1")?[a("font-size",t),a("line-height",e)]:null}return[a("font-size",t)];default:return null===(t=W(t,r.modifier,u))?void 0:[a("color",t)]}}var i=H(r,u,["--text-color","--color"]);if(i)return[a("color",i)];i=u.resolveWith(r.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(i){var[i,e={}]=Array.isArray(i)?i:[i];if(r.modifier){let e="arbitrary"===r.modifier.kind?r.modifier.value:u.resolve(r.modifier.value,["--leading"]);if(!e&&fe(r.modifier.value)){var o=u.resolve(null,["--spacing"]);if(!o)return null;e=`calc(${o} * ${r.modifier.value})`}if(!(e=e||"none"!==r.modifier.value?e:"1"))return null;let t=[a("font-size",i)];return e&&t.push(a("line-height",e)),t}return"string"==typeof e?[a("font-size",i),a("line-height",e)]:[a("font-size",i),a("line-height",e["--line-height"]?`var(--tw-leading, ${e["--line-height"]})`:void 0),a("letter-spacing",e["--letter-spacing"]?`var(--tw-tracking, ${e["--letter-spacing"]})`:void 0),a("font-weight",e["--font-weight"]?`var(--tw-font-weight, ${e["--font-weight"]})`:void 0)]}}}),d("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let r=function(e){return`var(--tw-ring-inset,) 0 0 0 calc(${e} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${t})`},n=function(e){return`inset 0 0 0 ${e} var(--tw-inset-ring-color, currentColor)`};r,n;let i=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),o="0 0 #0000",l=()=>j([$("--tw-shadow",o),$("--tw-shadow-color"),$("--tw-inset-shadow",o),$("--tw-inset-shadow-color"),$("--tw-ring-color"),$("--tw-ring-shadow",o),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",o),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",o)]),t=(f("shadow-initial",[l,["--tw-shadow-color","initial"]]),c.functional("shadow",t=>{if(!t.value)return null===(e=u.get(["--shadow"]))?void 0:[l(),a("--tw-shadow",ae(e,e=>`var(--tw-shadow-color, ${e})`)),a("box-shadow",i)];if("arbitrary"===t.value.kind){let e=t.value.value;return"color"!==(t.value.dataType??z(e,["color"]))?[l(),a("--tw-shadow",ae(e,e=>`var(--tw-shadow-color, ${e})`)),a("box-shadow",i)]:null===(e=W(e,t.modifier,u))?void 0:[l(),a("--tw-shadow-color",e)]}if("none"===t.value.value)return t.modifier?void 0:[l(),a("--tw-shadow",o),a("box-shadow",i)];var e=u.get(["--shadow-"+t.value.value]);if(e)return t.modifier?void 0:[l(),a("--tw-shadow",ae(e,e=>`var(--tw-shadow-color, ${e})`)),a("box-shadow",i)];e=H(t,u,["--box-shadow-color","--color"]);return e?[l(),a("--tw-shadow-color",e)]:void 0}),d("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),f("inset-shadow-initial",[l,["--tw-inset-shadow-color","initial"]]),c.functional("inset-shadow",t=>{if(!t.value)return null===(e=u.get(["--inset-shadow"]))?void 0:[l(),a("--tw-inset-shadow",ae(e,e=>`var(--tw-inset-shadow-color, ${e})`)),a("box-shadow",i)];if("arbitrary"===t.value.kind){let e=t.value.value;return"color"!==(t.value.dataType??z(e,["color"]))?[l(),a("--tw-inset-shadow","inset "+ae(e,e=>`var(--tw-inset-shadow-color, ${e})`)),a("box-shadow",i)]:null===(e=W(e,t.modifier,u))?void 0:[l(),a("--tw-inset-shadow-color",e)]}if("none"===t.value.value)return t.modifier?void 0:[l(),a("--tw-inset-shadow",o),a("box-shadow",i)];var e=u.get(["--inset-shadow-"+t.value.value]);if(e)return t.modifier?void 0:[l(),a("--tw-inset-shadow",ae(e,e=>`var(--tw-inset-shadow-color, ${e})`)),a("box-shadow",i)];e=H(t,u,["--box-shadow-color","--color"]);return e?[l(),a("--tw-inset-shadow-color",e)]:void 0}),d("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),f("ring-inset",[l,["--tw-ring-inset","inset"]]),u.get(["--default-ring-color"])??"currentColor"),s=(c.functional("ring",t=>{if(!t.value){if(t.modifier)return;var e=u.get(["--default-ring-width"])??"1px";return[l(),a("--tw-ring-shadow",r(e)),a("box-shadow",i)]}if("arbitrary"===t.value.kind){let e=t.value.value;return"length"!==(t.value.dataType??z(e,["color","length"]))?null===(e=W(e,t.modifier,u))?void 0:[a("--tw-ring-color",e)]:t.modifier?void 0:[l(),a("--tw-ring-shadow",r(e)),a("box-shadow",i)]}e=H(t,u,["--ring-color","--color"]);if(e)return[a("--tw-ring-color",e)];{if(t.modifier)return;let e=u.resolve(t.value.value,["--ring-width"]);if(e=null===e&&N(t.value.value)?t.value.value+"px":e)return[l(),a("--tw-ring-shadow",r(e)),a("box-shadow",i)]}}),d("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),c.functional("inset-ring",t=>{if(!t.value)return t.modifier?void 0:[l(),a("--tw-inset-ring-shadow",n("1px")),a("box-shadow",i)];if("arbitrary"===t.value.kind){let e=t.value.value;return"length"!==(t.value.dataType??z(e,["color","length"]))?null===(e=W(e,t.modifier,u))?void 0:[a("--tw-inset-ring-color",e)]:t.modifier?void 0:[l(),a("--tw-inset-ring-shadow",n(e)),a("box-shadow",i)]}var e=H(t,u,["--ring-color","--color"]);if(e)return[a("--tw-inset-ring-color",e)];{if(t.modifier)return;let e=u.resolve(t.value.value,["--ring-width"]);if(e=null===e&&N(t.value.value)?t.value.value+"px":e)return[l(),a("--tw-inset-ring-shadow",n(e)),a("box-shadow",i)]}}),d("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),"var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)");c.functional("ring-offset",t=>{if(t.value){if("arbitrary"===t.value.kind){let e=t.value.value;return"length"!==(t.value.dataType??z(e,["color","length"]))?null===(e=W(e,t.modifier,u))?void 0:[a("--tw-ring-offset-color",e)]:t.modifier?void 0:[a("--tw-ring-offset-width",e),a("--tw-ring-offset-shadow",s)]}var e=u.resolve(t.value.value,["--ring-offset-width"]);if(e)return t.modifier?void 0:[a("--tw-ring-offset-width",e),a("--tw-ring-offset-shadow",s)];if(N(t.value.value))return t.modifier?void 0:[a("--tw-ring-offset-width",t.value.value+"px"),a("--tw-ring-offset-shadow",s)];e=H(t,u,["--ring-offset-color","--color"]);return e?[a("--tw-ring-offset-color",e)]:void 0}})}return d("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(e,t)=>""+5*t)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),c.functional("@container",e=>{let t=null;if(null===e.value?t="inline-size":"arbitrary"===e.value.kind?t=e.value.value:"named"===e.value.kind&&"normal"===e.value.value&&(t="normal"),null!==t)return e.modifier?[a("container-type",t),a("container-name",e.modifier.value)]:[a("container-type",t)]}),d("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),c}function Wt(n){let t=n.params;return Kn.test(t)?h=>{let i=new Set,o=new Set;D(n.nodes,e=>{var t;"declaration"===e.kind&&e.value&&(e.value.includes("--value(")||e.value.includes("--modifier("))&&(ce(t=M(e.value),t=>{if("function"===t.kind&&("--value"===t.value||"--modifier"===t.value)){let e=_(q(t.nodes),",");for(var[r,a]of e.entries())a=a.replace(/\\\*/g,"*"),a=a.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),a=a.replace(/\s+/g,""),a=a.replace(/(-\*){2,}/g,"-*"),"-"!==a[0]||"-"!==a[1]||a.includes("-*")||(a+="-*"),e[r]=a;t.nodes=M(e.join(","));for(var n of t.nodes)"word"===n.kind&&"-"===n.value[0]&&"-"===n.value[1]&&(n=n.value.replace(/-\*.*$/g,""),"--value"===t.value?i.add(n):"--modifier"===t.value&&o.add(n))}}),e.value=q(t))}),h.utilities.functional(t.slice(0,-2),e=>{let t=structuredClone(n),f=e.value,p=e.modifier;if(null!==f){let o=!1,l=!1,s=!1,u=!1,c=new Map,d=!1;if(D([t],(a,{parent:n,replaceWith:i})=>{var e;"rule"!==n?.kind&&"at-rule"!==n?.kind||"declaration"!==a.kind||!a.value||0===(ce(e=M(a.value),(e,{replaceWith:t})=>{if("function"===e.kind){if("--value"===e.value)return o=!0,(r=Lt(f,e,h))?(l=!0,r.ratio?d=!0:c.set(a,n),t(r.nodes),1):(o||=!1,i([]),2);if("--modifier"===e.value){if(null===p)return i([]),1;s=!0;var r=Lt(p,e,h);return r?(u=!0,t(r.nodes),1):(s||=!1,i([]),2)}}})??0)&&(a.value=q(e))}),o&&!l||s&&!u||d&&u||p&&!d&&!u)return null;if(d)for(var[r,a]of c){r=a.nodes.indexOf(r);-1!==r&&a.nodes.splice(r,1)}return t.nodes}}),h.utilities.suggest(t.slice(0,-2),()=>[{values:h.theme.keysInNamespaces(i).map(e=>e.replaceAll("_",".")),modifiers:h.theme.keysInNamespaces(o).map(e=>e.replaceAll("_","."))}])}:On.test(t)?e=>{e.utilities.static(t,()=>structuredClone(n.nodes))}:null}function Lt(r,e,a){for(var n of e.nodes)if("named"===r.kind&&"word"===n.kind&&"-"===n.value[0]&&"-"===n.value[1]){let t=n.value;if(t.endsWith("-*")){t=t.slice(0,-2);var i=a.theme.resolve(r.value,[t]);if(i)return{nodes:M(i)}}else{let e=t.split("-*");if(e.length<=1)continue;var i=[e.shift()],o=a.theme.resolveWith(r.value,i,e);if(o){var[,o={}]=o,o=o[e.pop()];if(o)return{nodes:M(o)}}}}else{if("named"===r.kind&&"word"===n.kind){if("number"!==n.value&&"integer"!==n.value&&"ratio"!==n.value&&"percentage"!==n.value)continue;let e="ratio"===n.value&&"fraction"in r?r.fraction:r.value;if(!e)continue;o=z(e,[n.value]);if(null===o)continue;if("ratio"===o){var[t,l]=_(e,"/");if(!N(t)||!N(l))continue}else{if("number"===o&&!fe(e))continue;if("percentage"===o&&!N(e.slice(0,-1)))continue}return{nodes:M(e),ratio:"ratio"===o}}if("arbitrary"===r.kind&&"word"===n.kind&&"["===n.value[0]&&"]"===n.value[n.value.length-1]){t=n.value.slice(1,-1);if("*"===t)return{nodes:M(r.value)};if("dataType"in r&&r.dataType&&r.dataType!==t)continue;if("dataType"in r&&r.dataType)return{nodes:M(r.value)};if(null!==z(r.value,[t]))return{nodes:M(r.value)}}}}var st={"--alpha":_n,"--spacing":Pn,"--theme":Dn,theme:Ht};function _n(e,t,...r){var[t,a]=_(t,"/").map(e=>e.trim());if(!t||!a)throw new Error(`The --alpha(…) function requires a color and an alpha value, e.g.: \`--alpha(${t||"var(--my-color)"} / ${a||"50%"})\``);if(0<r.length)throw new Error(`The --alpha(…) function only accepts one argument, e.g.: \`--alpha(${t||"var(--my-color)"} / ${a||"50%"})\``);return J(t,a)}function Pn(e,t,...r){if(!t)throw new Error("The --spacing(…) function requires an argument, but received none.");if(0<r.length)throw new Error(`The --spacing(…) function only accepts a single argument, but received ${r.length+1}.`);r=e.theme.resolve(null,["--spacing"]);if(r)return`calc(${r} * ${t})`;throw new Error("The --spacing(…) function requires that the `--spacing` theme variable exists, but it was not found.")}function Dn(e,t,...r){if(t.startsWith("--"))return Ht(e,t,...r);throw new Error("The --theme(…) function can only be used with CSS variables from your theme.")}function Ht(e,t,...r){t=jn(t);e=e.resolveThemeValue(t);if(!e&&0<r.length)return r.join(", ");if(e)return e;throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`)}var Bt=new RegExp(Object.keys(st).map(e=>e+"\\(").join("|"));function de(e,t){let r=0;return D(e,e=>{if("declaration"===e.kind&&e.value&&Bt.test(e.value))return r|=8,void(e.value=qt(e.value,t));"at-rule"!==e.kind||"@media"!==e.name&&"@custom-media"!==e.name&&"@container"!==e.name&&"@supports"!==e.name||!Bt.test(e.params)||(r|=8,e.params=qt(e.params,t))}),r}function qt(e,a){e=M(e);return ce(e,(e,{replaceWith:t})=>{var r;if("function"===e.kind&&e.value in st)return r=_(q(e.nodes).trim(),",").map(e=>e.trim()),t(M(st[e.value](a,...r)))}),q(e)}function jn(t){if("'"!==t[0]&&'"'!==t[0])return t;let r="",a=t[0];for(let e=1;e<t.length-1;e++){var n=t[e],i=t[e+1];"\\"!==n||i!==a&&"\\"!==i?r+=n:(r+=i,e++)}return r}function Fe(l,s){var e=l.length,t=s.length,r=e<t?e:t;for(let o=0;o<r;o++){let n=l.charCodeAt(o),i=s.charCodeAt(o);if(n!==i){if(48<=n&&n<=57&&48<=i&&i<=57){let e=o,t=o+1,r=o,a=o+1;for(n=l.charCodeAt(t);48<=n&&n<=57;)n=l.charCodeAt(++t);for(i=s.charCodeAt(a);48<=i&&i<=57;)i=s.charCodeAt(++a);var u=l.slice(e,t),c=s.slice(r,a);return Number(u)-Number(c)||(u<c?-1:1)}return n-i}}return l.length-s.length}function Gt(e){let t=[];for(var r of e.utilities.keys("static"))t.push([r,{modifiers:[]}]);for(var a of e.utilities.keys("functional")){var n;for(n of e.utilities.getCompletions(a))for(var i of n.values){i=null===i?a:a+"-"+i;t.push([i,{modifiers:n.modifiers}]),n.supportsNegative&&t.push(["-"+i,{modifiers:n.modifiers}])}}return t.sort((e,t)=>Fe(e[0],t[0])),t}function Jt(o){let r=[];for(let[i,t]of o.variants.entries()){var n=function({value:e,modifier:t}={}){let r=i;e&&(r+=a?"-"+e:e),t&&(r+="/"+t);e=o.parseVariant(r);if(!e)return[];t=U(".__placeholder__",[]);if(null===ge(t,e,o.variants))return[];let n=[];return _e(t.nodes,(e,{path:a})=>{if(!("rule"!==e.kind&&"at-rule"!==e.kind||0<e.nodes.length)){a.sort((e,t)=>{e="at-rule"===e.kind,t="at-rule"===t.kind;return e&&!t?-1:!e&&t?1:0});let t=a.flatMap(e=>"rule"===e.kind?"&"===e.selector?[]:[e.selector]:"at-rule"===e.kind?[e.name+" "+e.params]:[]),r="";for(let e=t.length-1;0<=e;e--)r=""===r?t[e]:`${t[e]} { ${r} }`;n.push(r)}}),n};if("arbitrary"===t.kind)continue;let a="@"!==i,e=o.variants.getCompletions(i);switch(t.kind){case"static":r.push({name:i,values:e,isArbitrary:!1,hasDash:a,selectors:n});break;case"functional":case"compound":r.push({name:i,values:e,isArbitrary:!0,hasDash:a,selectors:n})}}return r}function Yt(e,t){let{astNodes:r,nodeSorting:a}=ee(Array.from(t),e),n=new Map(t.map(e=>[e,null])),i=0n;for(var o of r){o=a.get(o)?.candidate;o&&n.set(o,n.get(o)??i++)}return t.map(e=>[e,n.get(e)??null])}var ze=/^@?[a-zA-Z0-9_-]*$/,ut=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(e,t,{compounds:r,order:a}={}){this.set(e,{kind:"static",applyFn:t,compoundsWith:0,compounds:r??2,order:a})}fromAst(e,r){let t=[];D(r,e=>{"rule"===e.kind?t.push(e.selector):"at-rule"===e.kind&&"@slot"!==e.name&&t.push(e.name+" "+e.params)}),this.static(e,e=>{var t=structuredClone(r);ct(t,e.nodes),e.nodes=t},{compounds:se(t)})}functional(e,t,{compounds:r,order:a}={}){this.set(e,{kind:"functional",applyFn:t,compoundsWith:0,compounds:r??2,order:a})}compound(e,t,r,{compounds:a,order:n}={}){this.set(e,{kind:"compound",applyFn:r,compoundsWith:t,compounds:a??2,order:n})}group(e,t){this.groupOrder=this.nextOrder(),t&&this.compareFns.set(this.groupOrder,t),e(),this.groupOrder=null}has(e){return this.variants.has(e)}get(e){return this.variants.get(e)}kind(e){return this.variants.get(e)?.kind}compoundsWith(e,t){e=this.variants.get(e),t="string"==typeof t?this.variants.get(t):"arbitrary"===t.kind?{compounds:se([t.selector])}:this.variants.get(t.root);return!!(e&&t&&"compound"===e.kind&&0!==t.compounds&&0!==e.compoundsWith&&e.compoundsWith&t.compounds)}suggest(e,t){this.completions.set(e,t)}getCompletions(e){return this.completions.get(e)?.()??[]}compare(e,t){if(e===t)return 0;if(null===e)return-1;if(null===t)return 1;if("arbitrary"===e.kind&&"arbitrary"===t.kind)return e.selector<t.selector?-1:1;if("arbitrary"===e.kind)return 1;if("arbitrary"===t.kind)return-1;var r=this.variants.get(e.root).order,a=r-this.variants.get(t.root).order;if(0!=a)return a;if("compound"===e.kind&&"compound"===t.kind)return 0!==(a=this.compare(e.variant,t.variant))?a:e.modifier&&t.modifier?e.modifier.value<t.modifier.value?-1:1:e.modifier?1:t.modifier?-1:0;let n=this.compareFns.get(r);if(void 0!==n)return n(e,t);if(e.root!==t.root)return e.root<t.root?-1:1;a=e.value,r=t.value;return null===a||null!==r&&("arbitrary"!==a.kind||"arbitrary"===r.kind)&&("arbitrary"!==a.kind&&"arbitrary"===r.kind||a.value<r.value)?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(e,{kind:t,applyFn:r,compounds:a,compoundsWith:n,order:i}){var o=this.variants.get(e);o?Object.assign(o,{kind:t,applyFn:r,compounds:a}):(void 0===i&&(this.lastOrder=this.nextOrder(),i=this.lastOrder),this.variants.set(e,{kind:t,applyFn:r,order:i,compoundsWith:n,compounds:a}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function se(e){let t=0;for(var r of e)if("@"===r[0]){if(!r.startsWith("@media")&&!r.startsWith("@supports")&&!r.startsWith("@container"))return 0;t|=1}else{if(r.includes("::"))return 0;t|=2}return t}function Qt(o){let i=new ut;function e(e,r,{compounds:t}={}){t=t??se(r),i.static(e,t=>{t.nodes=r.map(e=>L(e,t.nodes))},{compounds:t})}e("*",[":is(& > *)"],{compounds:0}),e("**",[":is(& *)"],{compounds:0});let r=["@media","@supports","@container"];function d(t){for(var e of r)if(e===t.name){let e=_(t.params,",");return 1<e.length?null:(e=function(r,e){return e.map(e=>{let t=_(e=e.trim()," ");return"not"===t[0]?t.slice(1).join(" "):"@container"!==r||"("===t[0][0]?"not "+e:"not"===t[1]?t[0]+" "+t.slice(2).join(" "):t[0]+" not "+t.slice(1).join(" ")})}(t.name,e),P(t.name,e.join(", ")))}return null}i.compound("not",3,(u,e)=>{if("arbitrary"===e.variant.kind&&e.variant.relative||e.modifier)return null;let c=!1;return D([u],(e,{path:t})=>{if("rule"!==e.kind&&"at-rule"!==e.kind)return 0;if(0<e.nodes.length)return 0;let r=[],a=[];for(var n of t)"at-rule"===n.kind?r.push(n):"rule"===n.kind&&a.push(n);if(1<r.length)return 2;if(1<a.length)return 2;let i=[];for(let e of a){o=(o=e.selector).includes("::")?null:`&:not(${_(o,",").map(e=>e=(e=e.startsWith("&:is(")&&e.endsWith(")")?e.slice(5,-1):e).replaceAll("&","*")).join(", ")})`;if(!o)return c=!1,2;i.push(U(o,[]))}var o,l;for(l of r){var s=d(l);if(!s)return c=!1,2;i.push(s)}return Object.assign(u,U("&",i)),c=!0,1}),"rule"===u.kind&&"&"===u.selector&&1===u.nodes.length&&Object.assign(u,u.nodes[0]),c?void 0:null}),i.suggest("not",()=>Array.from(i.keys()).filter(e=>i.compoundsWith("not",e))),i.compound("group",2,(e,t)=>{if("arbitrary"===t.variant.kind&&t.variant.relative)return null;let n=t.modifier?`:where(.${o.prefix?o.prefix+"\\:":""}group\\/${t.modifier.value})`:`:where(.${o.prefix?o.prefix+"\\:":""}group)`,i=!1;return D([e],(e,{path:t})=>{if("rule"!==e.kind)return 0;for(var r of t.slice(0,-1))if("rule"===r.kind)return i=!1,2;let a=e.selector.replaceAll("&",n);1<_(a,",").length&&(a=`:is(${a})`),e.selector=`&:is(${a} *)`,i=!0}),i?void 0:null}),i.suggest("group",()=>Array.from(i.keys()).filter(e=>i.compoundsWith("group",e))),i.compound("peer",2,(e,t)=>{if("arbitrary"===t.variant.kind&&t.variant.relative)return null;let n=t.modifier?`:where(.${o.prefix?o.prefix+"\\:":""}peer\\/${t.modifier.value})`:`:where(.${o.prefix?o.prefix+"\\:":""}peer)`,i=!1;return D([e],(e,{path:t})=>{if("rule"!==e.kind)return 0;for(var r of t.slice(0,-1))if("rule"===r.kind)return i=!1,2;let a=e.selector.replaceAll("&",n);1<_(a,",").length&&(a=`:is(${a})`),e.selector=`&:is(${a} ~ *)`,i=!0}),i?void 0:null}),i.suggest("peer",()=>Array.from(i.keys()).filter(e=>i.compoundsWith("peer",e))),e("first-letter",["&::first-letter"]),e("first-line",["&::first-line"]),e("marker",["& *::marker","&::marker"]),e("selection",["& *::selection","&::selection"]),e("file",["&::file-selector-button"]),e("placeholder",["&::placeholder"]),e("backdrop",["&::backdrop"]);{let t=function(){return j([P("@property","--tw-content",[a("syntax",'"*"'),a("initial-value",'""'),a("inherits","false")])])};t;i.static("before",e=>{e.nodes=[U("&::before",[t(),a("content","var(--tw-content)"),...e.nodes])]},{compounds:0}),i.static("after",e=>{e.nodes=[U("&::after",[t(),a("content","var(--tw-content)"),...e.nodes])]},{compounds:0})}e("first",["&:first-child"]),e("last",["&:last-child"]),e("only",["&:only-child"]),e("odd",["&:nth-child(odd)"]),e("even",["&:nth-child(even)"]),e("first-of-type",["&:first-of-type"]),e("last-of-type",["&:last-of-type"]),e("only-of-type",["&:only-of-type"]),e("visited",["&:visited"]),e("target",["&:target"]),e("open",["&:is([open], :popover-open, :open)"]),e("default",["&:default"]),e("checked",["&:checked"]),e("indeterminate",["&:indeterminate"]),e("placeholder-shown",["&:placeholder-shown"]),e("autofill",["&:autofill"]),e("optional",["&:optional"]),e("required",["&:required"]),e("valid",["&:valid"]),e("invalid",["&:invalid"]),e("in-range",["&:in-range"]),e("out-of-range",["&:out-of-range"]),e("read-only",["&:read-only"]),e("empty",["&:empty"]),e("focus-within",["&:focus-within"]),i.static("hover",e=>{e.nodes=[U("&:hover",[P("@media","(hover: hover)",e.nodes)])]}),e("focus",["&:focus"]),e("focus-visible",["&:focus-visible"]),e("active",["&:active"]),e("enabled",["&:enabled"]),e("disabled",["&:disabled"]),e("inert",["&:is([inert], [inert] *)"]),i.compound("in",2,(e,t)=>{if(t.modifier)return null;let a=!1;return D([e],(e,{path:t})=>{if("rule"!==e.kind)return 0;for(var r of t.slice(0,-1))if("rule"===r.kind)return a=!1,2;e.selector=`:where(${e.selector.replaceAll("&","*")}) &`,a=!0}),a?void 0:null}),i.suggest("in",()=>Array.from(i.keys()).filter(e=>i.compoundsWith("in",e))),i.compound("has",2,(e,t)=>{if(t.modifier)return null;let a=!1;return D([e],(e,{path:t})=>{if("rule"!==e.kind)return 0;for(var r of t.slice(0,-1))if("rule"===r.kind)return a=!1,2;e.selector=`&:has(${e.selector.replaceAll("&","*")})`,a=!0}),a?void 0:null}),i.suggest("has",()=>Array.from(i.keys()).filter(e=>i.compoundsWith("has",e))),i.functional("aria",(e,t)=>{if(!t.value||t.modifier)return null;"arbitrary"===t.value.kind?e.nodes=[U(`&[aria-${Zt(t.value.value)}]`,e.nodes)]:e.nodes=[U(`&[aria-${t.value.value}="true"]`,e.nodes)]}),i.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),i.functional("data",(e,t)=>{if(!t.value||t.modifier)return null;e.nodes=[U(`&[data-${Zt(t.value.value)}]`,e.nodes)]}),i.functional("nth",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[U(`&:nth-child(${t.value.value})`,e.nodes)]}),i.functional("nth-last",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[U(`&:nth-last-child(${t.value.value})`,e.nodes)]}),i.functional("nth-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[U(`&:nth-of-type(${t.value.value})`,e.nodes)]}),i.functional("nth-last-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[U(`&:nth-last-of-type(${t.value.value})`,e.nodes)]}),i.functional("supports",(e,t)=>{if(!t.value||t.modifier)return null;let r=t.value.value;return null===r?null:/^[\w-]*\s*\(/.test(r)?(t=r.replace(/\b(and|or|not)\b/g," $1 "),void(e.nodes=[P("@supports",t,e.nodes)])):(r.includes(":")||(r+=": var(--tw)"),"("===r[0]&&")"===r[r.length-1]||(r=`(${r})`),void(e.nodes=[P("@supports",r,e.nodes)]))},{compounds:1}),e("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),e("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),e("contrast-more",["@media (prefers-contrast: more)"]),e("contrast-less",["@media (prefers-contrast: less)"]);{let n=function(e,t,r,a){if(e===t)return 0;e=a.get(e);if(null===e)return"asc"===r?-1:1;a=a.get(t);return null===a?"asc"===r?1:-1:le(e,a,r)};n;{let e=o.namespace("--breakpoint"),r=new I(t=>{switch(t.kind){case"static":return o.resolveValue(t.root,["--breakpoint"])??null;case"functional":{if(!t.value||t.modifier)return null;let e=null;return"arbitrary"===t.value.kind?e=t.value.value:"named"===t.value.kind&&(e=o.resolveValue(t.value.value,["--breakpoint"])),!e||e.includes("var(")?null:e}case"arbitrary":case"compound":return null}});i.group(()=>{i.functional("max",(e,t)=>{return t.modifier||null===(t=r.get(t))?null:void(e.nodes=[P("@media",`(width < ${t})`,e.nodes)])},{compounds:1})},(e,t)=>n(e,t,"desc",r)),i.suggest("max",()=>Array.from(e.keys()).filter(e=>null!==e)),i.group(()=>{for(let[e,t]of o.namespace("--breakpoint"))null!==e&&i.static(e,e=>{e.nodes=[P("@media",`(width >= ${t})`,e.nodes)]},{compounds:1});i.functional("min",(e,t)=>{return t.modifier||null===(t=r.get(t))?null:void(e.nodes=[P("@media",`(width >= ${t})`,e.nodes)])},{compounds:1})},(e,t)=>n(e,t,"asc",r)),i.suggest("min",()=>Array.from(e.keys()).filter(e=>null!==e))}{let e=o.namespace("--container"),a=new I(t=>{switch(t.kind){case"functional":{if(null===t.value)return null;let e=null;return"arbitrary"===t.value.kind?e=t.value.value:"named"===t.value.kind&&(e=o.resolveValue(t.value.value,["--container"])),!e||e.includes("var(")?null:e}case"static":case"arbitrary":case"compound":return null}});i.group(()=>{i.functional("@max",(e,t)=>{var r=a.get(t);if(null===r)return null;e.nodes=[P("@container",t.modifier?t.modifier.value+` (width < ${r})`:`(width < ${r})`,e.nodes)]},{compounds:1})},(e,t)=>n(e,t,"desc",a)),i.suggest("@max",()=>Array.from(e.keys()).filter(e=>null!==e)),i.group(()=>{i.functional("@",(e,t)=>{var r=a.get(t);if(null===r)return null;e.nodes=[P("@container",t.modifier?t.modifier.value+` (width >= ${r})`:`(width >= ${r})`,e.nodes)]},{compounds:1}),i.functional("@min",(e,t)=>{var r=a.get(t);if(null===r)return null;e.nodes=[P("@container",t.modifier?t.modifier.value+` (width >= ${r})`:`(width >= ${r})`,e.nodes)]},{compounds:1})},(e,t)=>n(e,t,"asc",a)),i.suggest("@min",()=>Array.from(e.keys()).filter(e=>null!==e)),i.suggest("@",()=>Array.from(e.keys()).filter(e=>null!==e))}}return e("portrait",["@media (orientation: portrait)"]),e("landscape",["@media (orientation: landscape)"]),e("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),e("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),e("dark",["@media (prefers-color-scheme: dark)"]),e("starting",["@starting-style"]),e("print",["@media print"]),e("forced-colors",["@media (forced-colors: active)"]),i}function Zt(a){if(a.includes("=")){let[e,...t]=_(a,"="),r=t.join("=").trim();if("'"===r[0]||'"'===r[0])return a;if(1<r.length){var n=r[r.length-1];if(" "===r[r.length-2]&&("i"===n||"I"===n||"s"===n||"S"===n))return`${e}="${r.slice(0,-2)}" `+n}return`${e}="${r}"`}return a}function ct(e,r){D(e,(e,{replaceWith:t})=>{if("at-rule"===e.kind&&"@slot"===e.name)t(r);else if("at-rule"===e.kind&&("@keyframes"===e.name||"@property"===e.name))return Object.assign(e,j([P(e.name,e.params,e.nodes)])),1})}function Xt(a){let e=Mt(a),t=Qt(a),i=new I(e=>Dt(e,o)),r=new I(e=>Array.from(Pt(e,o))),n=new I(e=>er(e,o)),o={theme:a,utilities:e,variants:t,invalidCandidates:new Set,important:!1,candidatesToCss(e){let r=[];for(var a of e){let e=!1,t=ee([a],this,{onInvalidCandidate(){e=!0}})["astNodes"];0===(t=oe(t)).length||e?r.push(null):r.push(G(t))}return r},getClassOrder(e){return Yt(this,e)},getClassList(){return Gt(this)},getVariants(){return Jt(this)},parseCandidate(e){return r.get(e)},parseVariant(e){return i.get(e)},compileAstNodes(e){return n.get(e)},getVariantOrder(){let e=Array.from(i.values()),t=(e.sort((e,t)=>this.variants.compare(e,t)),new Map),r,a=0;for(var n of e)null!==n&&(void 0!==r&&0!==this.variants.compare(r,n)&&a++,t.set(n,a),r=n);return t},resolveThemeValue(e){let t=e.lastIndexOf("/"),r=null;-1!==t&&(r=e.slice(t+1).trim(),e=e.slice(0,t).trim());e=a.get([e])??void 0;return r&&e?J(e,r):e}};return o}var ft=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function me(e){if(0==arguments.length)throw new TypeError("`CSS.escape` requires an argument.");var t,r=String(e),a=r.length,n=-1,i="",o=r.charCodeAt(0);if(1==a&&45==o)return"\\"+r;for(;++n<a;)0==(t=r.charCodeAt(n))?i+="�":i+=1<=t&&t<=31||127==t||0==n&&48<=t&&t<=57||1==n&&48<=t&&t<=57&&45==o?"\\"+t.toString(16)+" ":128<=t||45==t||95==t||48<=t&&t<=57||65<=t&&t<=90||97<=t&&t<=122?r.charAt(n):"\\"+r.charAt(n);return i}function tr(e){return e.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,e=>2<e.length?String.fromCodePoint(Number.parseInt(e.slice(1).trim(),16)):e[1])}function ee(e,r,{onInvalidCandidate:a}={}){let i=new Map,n=[],t=new Map;for(var o of e){var l;r.invalidCandidates.has(o)?a?.(o):0===(l=r.parseCandidate(o)).length?a?.(o):t.set(o,l)}let s=r.getVariantOrder();for(var[u,c]of t){let t=!1;for(var d of c){let e=r.compileAstNodes(d);if(0!==e.length){try{de(e.map(({node:e})=>e),r)}catch{continue}t=!0;for(var{node:f,propertySort:p}of e){let e=0n;for(var h of d.variants)e|=1n<<BigInt(s.get(h));i.set(f,{properties:p,variants:e,candidate:u}),n.push(f)}}}t||a?.(u)}return n.sort((e,t)=>{var r=i.get(e),a=i.get(t);if(r.variants-a.variants!==0n)return Number(r.variants-a.variants);let n=0;for(;r.properties.length<n&&a.properties.length<n&&r.properties[n]===a.properties[n];)n+=1;return(r.properties[n]??1/0)-(a.properties[n]??1/0)||a.properties.length-r.properties.length||Fe(r.candidate,a.candidate)}),{astNodes:n,nodeSorting:i}}function er(e,t){var r,a=Un(e,t);if(0===a.length)return[];let n=[],i="."+me(e.raw);for(r of a){var o,l=Fn(r),s=((e.important||t.important)&&nr(r),{kind:"rule",selector:i,nodes:r});for(o of e.variants)if(null===ge(s,o,t.variants))return[];n.push({node:s,propertySort:l})}return n}function ge(t,e,r,a=0){if("arbitrary"===e.kind)return e.relative&&0===a?null:void(t.nodes=[L(e.selector,t.nodes)]);let n=r.get(e.root)["applyFn"];if("compound"!==e.kind)return null===n(t,e)?null:void 0;var i,o=P("@slot");if(null===ge(o,e.variant,r,a+1)||"not"===e.root&&1<o.nodes.length)return null;for(i of o.nodes)if("rule"!==i.kind&&"at-rule"!==i.kind||null===n(i,e))return null;D(o.nodes,e=>{if(("rule"===e.kind||"at-rule"===e.kind)&&e.nodes.length<=0)return e.nodes=t.nodes,1}),t.nodes=o.nodes}function rr(e){let t=e.options?.types??[];return 1<t.length&&t.includes("any")}function Un(t,r){if("arbitrary"===t.kind){let e=t.value;return null===(e=t.modifier?W(e,t.modifier,r.theme):e)?[]:[[a(t.property,e)]]}let e=r.utilities.get(t.root)??[],n=[],i=e.filter(e=>!rr(e));for(var o of i)if(o.kind===t.kind){o=o.compileFn(t);if(void 0!==o){if(null===o)return n;n.push(o)}}if(0<n.length)return n;var l;for(l of e.filter(e=>rr(e)))if(l.kind===t.kind){var s=l.compileFn(t);if(void 0!==s){if(null===s)return n;n.push(s)}}return n}function nr(e){for(var t of e)"at-root"!==t.kind&&("declaration"===t.kind?t.important=!0:"rule"!==t.kind&&"at-rule"!==t.kind||nr(t.nodes))}function Fn(e){let t=new Set,r=e.slice();for(;0<r.length;){var a=r.shift();if("declaration"===a.kind){if("--tw-sort"===a.property){var n=ft.indexOf(a.value??"");if(-1!==n){t.add(n);break}}n=ft.indexOf(a.property);-1!==n&&t.add(n)}else if("rule"===a.kind||"at-rule"===a.kind)for(var i of a.nodes)r.push(i)}return Array.from(t).sort((e,t)=>e-t)}function Ce(e,o){let n=0,t=L("&",e),i=new Set,l=new I(()=>new Set),s=new I(()=>new Set),u=(D([t],(r,{parent:e})=>{if("at-rule"===r.kind){if("@keyframes"===r.name)return D(r.nodes,e=>{if("at-rule"===e.kind&&"@apply"===e.name)throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;var t;if("@utility"===r.name)return t=r.params.replace(/-\*$/,""),s.get(t).add(r),void D(r.nodes,e=>{if("at-rule"===e.kind&&"@apply"===e.name){i.add(r);for(var t of ir(e,o))l.get(r).add(t)}});if("@apply"===r.name&&null!==e){n|=1,i.add(e);for(var a of ir(r,o))l.get(e).add(a)}}}),new Set),c=[],d=new Set;for(var r of i)!function e(t,r=[]){if(!u.has(t)){if(d.has(t)){let a=r[(r.indexOf(t)+1)%r.length];throw"at-rule"===t.kind&&"@utility"===t.name&&"at-rule"===a.kind&&"@utility"===a.name&&D(t.nodes,e=>{var t;if("at-rule"===e.kind&&"@apply"===e.name)for(t of e.params.split(/\s+/g))for(var r of o.parseCandidate(t))switch(r.kind){case"arbitrary":break;case"static":case"functional":if(a.params.replace(/-\*$/,"")===r.root)throw new Error(`You cannot \`@apply\` the \`${t}\` utility here because it creates a circular dependency.`)}}),new Error(`Circular dependency detected:

${G([t])}
Relies on:

`+G([a]))}d.add(t);for(var a of l.get(t))for(var n of s.get(a))r.push(t),e(n,r),r.pop();u.add(t),d.delete(t),c.push(t)}}(r);return D(c,(r,{replaceWith:a})=>{if("at-rule"===r.kind&&"@apply"===r.name){let e=ee(r.params.split(/\s+/g),o,{onInvalidCandidate:e=>{throw new Error("Cannot apply unknown utility class: "+e)}}).astNodes,t=[];for(var n of e)if("rule"===n.kind)for(var i of n.nodes)t.push(i);else t.push(n);a(t)}}),n}function*ir(e,t){for(var r of e.params.split(/\s+/g))for(var a of t.parseCandidate(r))switch(a.kind){case"arbitrary":break;case"static":case"functional":yield a.root}}async function dt(e,s,u,c=0){let r=0,d=[];return D(e,(t,{replaceWith:l})=>{if("at-rule"===t.kind&&("@import"===t.name||"@reference"===t.name)){let e=zn(M(t.params));if(null!==e){"@reference"===t.name&&(e.media="reference"),r|=2;let{uri:a,layer:n,media:i,supports:o}=e;if(!(a.startsWith("data:")||a.startsWith("http://")||a.startsWith("https://"))){let r=ie({},[]);return d.push((async()=>{if(100<c)throw new Error(`Exceeded maximum recursion depth while resolving \`${a}\` in \`${s}\`)`);var e=await u(a,s),t=ne(e.content);await dt(t,e.base,u,c+1),r.nodes=In([ie({base:e.base},t)],n,i,o)})()),l(r),1}}}}),0<d.length&&await Promise.all(d),r}function zn(r){let a,n=null,i=null,o=null;for(let t=0;t<r.length;t++){let e=r[t];if("separator"!==e.kind)if("word"!==e.kind||a){if("function"===e.kind&&"url"===e.value.toLowerCase()||!a)return null;if("word"!==e.kind&&"function"!==e.kind||"layer"!==e.value.toLowerCase()){if("function"!==e.kind||"supports"!==e.value.toLowerCase()){i=q(r.slice(t));break}if(o)return null;o=q(e.nodes)}else{if(n)return null;if(o)throw new Error("`layer(…)` in an `@import` should come before any other functions or conditions");n="nodes"in e?q(e.nodes):""}}else{if(!e.value||'"'!==e.value[0]&&"'"!==e.value[0])return null;a=e.value.slice(1,-1)}}return a?{uri:a,layer:n,media:i,supports:o}:null}function In(e,t,r,a){let n=e;return null!==t&&(n=[P("@layer",t,n)]),null!==r&&(n=[P("@media",r,n)]),n=null!==a?[P("@supports","("===a[0]?a:`(${a})`,n)]:n}var lr=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function or(t,e){return(lr.get(e)??[]).some(e=>t===e||t.startsWith(e+"-"))}var Ie=class{constructor(e=new Map,t=new Set([])){this.values=e,this.keyframes=t}prefix=null;add(e,t,r=0){if((e=e.endsWith("\\*")?e.slice(0,-2)+"*":e).endsWith("-*")){if("initial"!==t)throw new Error(`Invalid theme value \`${t}\` for namespace \`${e}\``);"--*"===e?this.values.clear():this.clearNamespace(e.slice(0,-2),0)}if(4&r){var a=this.values.get(e);if(a&&!(4&a.options))return}"initial"===t?this.values.delete(e):this.values.set(e,{value:t,options:r})}keysInNamespaces(e){let t=[];for(var r of e){var a,n=r+"-";for(a of this.values.keys())!a.startsWith(n)||-1!==a.indexOf("--",2)||or(a,r)||t.push(a.slice(n.length))}return t}get(e){for(var t of e){t=this.values.get(t);if(t)return t.value}return null}hasDefault(e){return 4==(4&this.getOptions(e))}getOptions(e){return this.values.get(e)?.options??0}entries(){return this.prefix?Array.from(this.values,e=>(e[0]=this.#r(e[0]),e)):this.values.entries()}#r(e){return this.prefix?`--${this.prefix}-`+e.slice(2):e}clearNamespace(e,t){var r=lr.get(e)??[];e:for(var a of this.values.keys())if(a.startsWith(e)){if(0!==t&&(this.getOptions(a)&t)!==t)continue;for(var n of r)if(a.startsWith(n))continue e;this.values.delete(a)}}#e(e,t){for(var r of t){var a=null!==e?me(r+"-"+e.replaceAll(".","_")):r;if(this.values.has(a)&&!or(a,r))return a}return null}#t(e){return this.values.has(e)?`var(${this.#r(e)})`:null}resolve(e,t){e=this.#e(e,t);if(!e)return null;t=this.values.get(e);return 1&t.options?t.value:this.#t(e)}resolveValue(e,t){e=this.#e(e,t);return e?this.values.get(e).value:null}resolveWith(e,t,r=[]){var a,n=this.#e(e,t);if(!n)return null;let i={};for(a of r){var o=""+n+a,l=this.values.get(o);l&&(1&l.options?i[a]=l.value:i[a]=this.#t(o))}e=this.values.get(n);return 1&e.options?[e.value,i]:[this.#t(n),i]}namespace(e){let t=new Map,r=e+"-";for(var[a,n]of this.values)a===e?t.set(null,n.value):a.startsWith(r+"-")?t.set(a.slice(e.length),n.value):a.startsWith(r)&&t.set(a.slice(r.length),n.value);return t}addKeyframes(e){this.keyframes.add(e)}getKeyframes(){return Array.from(this.keyframes)}};function he(e,t=null){return Array.isArray(e)&&2===e.length&&"object"==typeof e[1]&&null!==typeof e[1]?t?e[1][t]??null:e[0]:Array.isArray(e)&&null===t?e.join(", "):"string"==typeof e&&null===t?e:null}function sr(e,{theme:t},r){for(var a of r){a=Le([a]);a&&e.theme.clearNamespace("--"+a,4)}for(var[n,i]of Ln(t)){var o;"string"!=typeof i&&"number"!=typeof i||("string"==typeof i&&(i=i.replace(/<alpha-value>/g,"1")),"opacity"!==n[0]||"number"!=typeof i&&"string"!=typeof i||0<=(o="string"==typeof i?parseFloat(i):i)&&o<=1&&(i=100*o+"%"),(o=Le(n))&&e.theme.add("--"+me(o),""+i,7))}return Object.hasOwn(t,"fontFamily")&&((r=he(t.fontFamily.sans))&&e.theme.hasDefault("--font-sans")&&(e.theme.add("--default-font-family",r,5),e.theme.add("--default-font-feature-settings",he(t.fontFamily.sans,"fontFeatureSettings")??"normal",5),e.theme.add("--default-font-variation-settings",he(t.fontFamily.sans,"fontVariationSettings")??"normal",5)),(r=he(t.fontFamily.mono))&&e.theme.hasDefault("--font-mono")&&(e.theme.add("--default-mono-font-family",r,5),e.theme.add("--default-mono-font-feature-settings",he(t.fontFamily.mono,"fontFeatureSettings")??"normal",5),e.theme.add("--default-mono-font-variation-settings",he(t.fontFamily.mono,"fontVariationSettings")??"normal",5))),t}function Ln(e){let a=[];return ur(e,[],(e,t)=>{if(Wn(e))return a.push([t,e]),1;if(Bn(e)){a.push([t,e[0]]);for(var r of Reflect.ownKeys(e[1]))a.push([[...t,"-"+r],e[1][r]]);return 1}return Array.isArray(e)&&e.every(e=>"string"==typeof e)?(a.push([t,e.join(", ")]),1):void 0}),a}var Mn=/^[a-zA-Z0-9-_%/\.]+$/;function Le(r){if("container"===r[0])return null;"animation"===(r=structuredClone(r))[0]&&(r[0]="animate"),"aspectRatio"===r[0]&&(r[0]="aspect"),"borderRadius"===r[0]&&(r[0]="radius"),"boxShadow"===r[0]&&(r[0]="shadow"),"colors"===r[0]&&(r[0]="color"),"fontFamily"===r[0]&&(r[0]="font"),"fontSize"===r[0]&&(r[0]="text"),"letterSpacing"===r[0]&&(r[0]="tracking"),"lineHeight"===r[0]&&(r[0]="leading"),"maxWidth"===r[0]&&(r[0]="container"),"screens"===r[0]&&(r[0]="breakpoint"),"transitionTimingFunction"===r[0]&&(r[0]="ease");for(var e of r)if(!Mn.test(e))return null;return r.map((e,t,r)=>"1"===e&&t!==r.length-1?"":e).map(e=>e.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(e,t,r)=>t+"-"+r.toLowerCase())).filter((e,t)=>"DEFAULT"!==e||t!==r.length-1).join("-")}function Wn(e){return"number"==typeof e||"string"==typeof e}function Bn(e){if(!Array.isArray(e)||2!==e.length||"string"!=typeof e[0]&&"number"!=typeof e[0]||void 0===e[1]||null===e[1]||"object"!=typeof e[1])return!1;for(var t of Reflect.ownKeys(e[1]))if("string"!=typeof t||"string"!=typeof e[1][t]&&"number"!=typeof e[1][t])return!1;return!0}function ur(e,t=[],r){for(var a of Reflect.ownKeys(e)){var n=e[a];if(null!=n){var a=[...t,a],i=r(n,a)??0;if(1!==i){if(2===i)return 2;if((Array.isArray(n)||"object"==typeof n)&&2===ur(n,a,r))return 2}}}}function Me(e){let t=[];for(var r of _(e,"."))if(r.includes("[")){let e=0;for(;;){var a=r.indexOf("[",e),n=r.indexOf("]",a);if(-1===a||-1===n)break;a>e&&t.push(r.slice(e,a)),t.push(r.slice(a+1,n)),e=n+1}e<=r.length-1&&t.push(r.slice(e))}else t.push(r);return t}function ve(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;e=Object.getPrototypeOf(e);return null===e||null===Object.getPrototypeOf(e)}function $e(e,t,r,a=[]){for(var n of t)if(null!=n)for(var i of Reflect.ownKeys(n)){a.push(i);var o=r(e[i],n[i],a);void 0!==o?e[i]=o:ve(e[i])&&ve(n[i])?e[i]=$e({},[e[i],n[i]],r,a):e[i]=n[i],a.pop()}return e}function We(s,u,c){return function(t,e){let r=t.lastIndexOf("/"),a=null,n=(-1!==r&&(a=t.slice(r+1).trim(),t=t.slice(0,r).trim()),(()=>{let e=Me(t),[r,a]=qn(s.theme,e),n=c(cr(u()??{},e)??null);if("string"==typeof n&&(n=n.replace("<alpha-value>","1")),"object"!=typeof r)return"object"!=typeof a&&4&a?n??r:r;if(null!==n&&"object"==typeof n&&!Array.isArray(n)){let t=$e({},[n],(e,t)=>t);if(null===r&&Object.hasOwn(n,"__CSS_VALUES__")){let e={};for(var i in n.__CSS_VALUES__)e[i]=n[i],delete t[i];r=e}for(var o in r)"__CSS_VALUES__"===o||4&n?.__CSS_VALUES__?.[o]&&void 0!==cr(t,o.split("-"))||(t[tr(o)]=r[o]);return t}if(Array.isArray(r)&&Array.isArray(a)&&Array.isArray(n)){let e=r[0],t=r[1];4&a[0]&&(e=n[0]??e);for(var l of Object.keys(t))4&a[1][l]&&(t[l]=n[1][l]??t[l]);return[e,t]}return r??n})());return(n=a&&"string"==typeof n?J(n,a):n)??e}}function qn(a,e){if(1===e.length&&e[0].startsWith("--"))return[a.get([e[0]]),a.getOptions(e[0])];let n=Le(e),r=new Map,i=new I(()=>new Map),t=a.namespace("--"+n);if(0===t.size)return[null,0];let o=new Map;for(var[l,s]of t)if(l&&l.includes("--")){let e=l.indexOf("--"),t=l.slice(0,e),r=l.slice(e+2);r=r.replace(/-([a-z])/g,(e,t)=>t.toUpperCase()),i.get(""===t?null:t).set(r,[s,a.getOptions("--"+n+l)])}else r.set(l,s),o.set(l,a.getOptions(l?`--${n}-`+l:"--"+n));var u,c,d,f,p,h,m=a.getOptions("--"+n);for([u,c]of i){var v=r.get(u);if("string"==typeof v){let e={},t={};for(var[g,[w,b]]of c)e[g]=w,t[g]=b;r.set(u,[v,e]),o.set(u,[m,t])}}let y={},k={};for([d,f]of r)fr(y,[d??"DEFAULT"],f);for([p,h]of o)fr(k,[p??"DEFAULT"],h);return"DEFAULT"===e[e.length-1]?[y?.DEFAULT??null,k.DEFAULT??0]:"DEFAULT"in y&&1===Object.keys(y).length?[y.DEFAULT,k.DEFAULT??0]:(y.__CSS_VALUES__=k,[y,k])}function cr(t,r){for(let e=0;e<r.length;++e){var a=r[e];if(void 0===t[a]){if(void 0===r[e+1])return;r[e+1]=a+"-"+r[e+1]}else t=t[a]}return t}function fr(e,t,r){for(var a of t.slice(0,-1))void 0===e[a]&&(e[a]={}),e=e[a];e[t[t.length-1]]=r}function Hn(e){return{kind:"combinator",value:e}}function Gn(e,t){return{kind:"function",value:e,nodes:t}}function Ne(e){return{kind:"selector",value:e}}function Jn(e){return{kind:"separator",value:e}}function Yn(e){return{kind:"value",value:e}}function Be(r,e,a=null){for(let t=0;t<r.length;t++){var n=r[t],i=e(n,{parent:a,replaceWith(e){Array.isArray(e)?0===e.length?r.splice(t,1):1===e.length?r[t]=e[0]:r.splice(t,1,...e):r[t]=e,t--}})??0;if(2===i)return 2;if(1!==i&&"function"===n.kind&&2===Be(n.nodes,e,n))return 2}}function qe(e){let t="";for(var r of e)switch(r.kind){case"combinator":case"selector":case"separator":case"value":t+=r.value;break;case"function":t+=r.value+"("+qe(r.nodes)+")"}return t}var dr=92,Zn=93,pr=41,Qn=58,gr=44,Xn=34,ei=46,mr=62,hr=10,ti=35,vr=91,yr=40,br=43,ri=39,wr=32,kr=9,xr=126;function pt(i){i=i.replaceAll(`\r
`,`
`);let o=[],t=[],l=null,s="",u;for(let n=0;n<i.length;n++){var r,a=i.charCodeAt(n);switch(a){case gr:case mr:case hr:case wr:case br:case kr:case xr:{0<s.length&&(c=Ne(s),(l?l.nodes:o).push(c),s="");let e=n,t=n+1;for(;t<i.length&&((u=i.charCodeAt(t))===gr||u===mr||u===hr||u===wr||u===br||u===kr||u===xr);t++);n=t-1;let r=i.slice(e,t),a=(","===r.trim()?Jn:Hn)(r);(l?l.nodes:o).push(a);break}case yr:{let r=Gn(s,[]);if(s="",":not"!==r.value&&":where"!==r.value&&":has"!==r.value&&":is"!==r.value){let e=n+1,t=0;for(let e=n+1;e<i.length;e++)if((u=i.charCodeAt(e))===yr)t++;else if(u===pr){if(0===t){n=e;break}t--}var c=n;r.nodes.push(Yn(i.slice(e,c))),s="",n=c,o.push(r);break}(l?l.nodes:o).push(r),t.push(r),l=r;break}case pr:{let e=t.pop();0<s.length&&(r=Ne(s),e.nodes.push(r),s=""),l=0<t.length?t[t.length-1]:null;break}case ei:case Qn:case ti:0<s.length&&(r=Ne(s),(l?l.nodes:o).push(r)),s=String.fromCharCode(a);break;case vr:{0<s.length&&(d=Ne(s),(l?l.nodes:o).push(d)),s="";let e=n,t=0;for(let e=n+1;e<i.length;e++)if((u=i.charCodeAt(e))===vr)t++;else if(u===Zn){if(0===t){n=e;break}t--}s+=i.slice(e,n+1);break}case ri:case Xn:var d=n;for(let e=n+1;e<i.length;e++)if((u=i.charCodeAt(e))===dr)e+=1;else if(u===a){n=e;break}s+=i.slice(d,n+1);break;case dr:var e=i.charCodeAt(n+1);s+=String.fromCharCode(a)+String.fromCharCode(e),n+=1;break;default:s+=String.fromCharCode(a)}}return 0<s.length&&o.push(Ne(s)),o}var Ar=/^[a-z@][a-zA-Z0-9/%._-]*$/;function gt({designSystem:c,ast:r,resolvedConfig:i,featuresRef:d,referenceMode:a}){let e={addBase(e){a||(e=Z(e),d.current|=de(e,c),r.push(P("@layer","base",e)))},addVariant(e,t){if(!ze.test(e))throw new Error(`\`addVariant('${e}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);"string"==typeof t||Array.isArray(t)?c.variants.static(e,e=>{e.nodes=Cr(t,e.nodes)},{compounds:se("string"==typeof t?[t]:t)}):"object"==typeof t&&c.variants.fromAst(e,Z(t))},matchVariant(e,a,s){function n(e,t,r){return Cr(a(e,{modifier:t?.value??null}),r)}let u=Object.keys(s?.values??{});c.variants.group(()=>{c.variants.functional(e,(e,t)=>{if(!t.value)return s?.values&&"DEFAULT"in s.values?void(e.nodes=n(s.values.DEFAULT,t.modifier,e.nodes)):null;var r;"arbitrary"===t.value.kind?e.nodes=n(t.value.value,t.modifier,e.nodes):"named"===t.value.kind&&s?.values&&"string"==typeof(r=s.values[t.value.value])&&(e.nodes=n(r,t.modifier,e.nodes))})},(e,t)=>{if("functional"!==e.kind||"functional"!==t.kind)return 0;var r=e.value?e.value.value:"DEFAULT",a=t.value?t.value.value:"DEFAULT",n=s?.values?.[r]??r,i=s?.values?.[a]??a;if(s&&"function"==typeof s.sort)return s.sort({value:n,modifier:e.modifier?.value??null},{value:i,modifier:t.modifier?.value??null});let o=u.indexOf(r),l=u.indexOf(a);return o=-1===o?u.length:o,l=-1===l?u.length:l,o!==l?o-l:n<i?-1:1})},addUtilities(e){let t=(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e)),l=(t=t.flatMap(([e,t])=>_(e,",").map(e=>[e.trim(),t])),new I(()=>[]));for(let[e,o]of t)if(e.startsWith("@keyframes "))a||r.push(L(e,Z(o)));else{let n=pt(e),i=!1;if(Be(n,t=>{if("selector"===t.kind&&"."===t.value[0]&&Ar.test(t.value.slice(1))){let e=t.value;t.value="&";var r=qe(n),a=e.slice(1),r="&"===r?Z(o):[L(r,Z(o))];return l.get(a).push(...r),i=!0,void(t.value=e)}if("function"===t.kind&&":not"===t.value)return 1}),!i)throw new Error(`\`addUtilities({ '${e}' : … })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[e,t]of l)c.theme.prefix&&D(t,e=>{var t;"rule"===e.kind&&(Be(t=pt(e.selector),e=>{"selector"===e.kind&&"."===e.value[0]&&(e.value=`.${c.theme.prefix}\\:`+e.value.slice(1))}),e.selector=qe(t))}),c.utilities.static(e,()=>{var e=structuredClone(t);return d.current|=Ce(e,c),e})},matchUtilities(t,s){let u=s?.type?Array.isArray(s?.type)?s.type:[s.type]:["any"];for(let[e,l]of Object.entries(t)){var r=function({negative:o}){return n=>{if(!("arbitrary"===n.value?.kind&&0<u.length)||u.includes("any")||(!n.value.dataType||u.includes(n.value.dataType))&&(n.value.dataType||z(n.value.value,u))){let t=u.includes("color"),r=null,a=!1;{let e=s?.values??{};t&&(e=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},e)),n.value?"arbitrary"===n.value.kind?r=n.value.value:n.value.fraction&&e[n.value.fraction]?(r=e[n.value.fraction],a=!0):e[n.value.value]?r=e[n.value.value]:e.__BARE_VALUE__&&(r=e.__BARE_VALUE__(n.value)??null,a=(null!==n.value.fraction&&r?.includes("/"))??!1):r=e.DEFAULT??null}if(null!==r){let e;var i=s?.modifiers??null;if(e=n.modifier?"any"===i||"arbitrary"===n.modifier.kind?n.modifier.value:i?.[n.modifier.value]?i[n.modifier.value]:t&&!Number.isNaN(Number(n.modifier.value))?n.modifier.value+"%":null:null,n.modifier&&null===e&&!a)return"arbitrary"===n.value?.kind?null:void 0;t&&null!==e&&(r=J(r,e)),o&&(r=`calc(${r} * -1)`);i=Z(l(r,{modifier:e}));return d.current|=Ce(i,c),i}}}};if(!Ar.test(e))throw new Error(`\`matchUtilities({ '${e}' : … })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);s?.supportsNegativeValues&&c.utilities.functional("-"+e,r({negative:!0}),{types:u}),c.utilities.functional(e,r({negative:!1}),{types:u}),c.utilities.suggest(e,()=>{let e=s?.values??{},t=new Set(Object.keys(e));t.delete("__BARE_VALUE__"),t.has("DEFAULT")&&(t.delete("DEFAULT"),t.add(null));var r=s?.modifiers??{},r="any"===r?[]:Object.keys(r);return[{supportsNegative:s?.supportsNegativeValues??!1,values:Array.from(t),modifiers:r}]})}},addComponents(e,t){this.addUtilities(e,t)},matchComponents(e,t){this.matchUtilities(e,t)},theme:We(c,()=>i.theme??{},e=>e),prefix(e){return e},config(e,t){let r=i;if(!e)return r;var a=Me(e);for(let e=0;e<a.length;++e){var n=a[e];if(void 0===r[n])return t;r=r[n]}return r??t}};return e.addComponents=e.addComponents.bind(e),e.matchComponents=e.matchComponents.bind(e),e}function Z(e){let t=[];var r,n;for([r,n]of(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e)))if("object"!=typeof n)r.startsWith("--")||"@slot"!==n?(r=r.replace(/([A-Z])/g,"-$1").toLowerCase(),t.push(a(r,String(n)))):t.push(L(r,[P("@slot")]));else if(Array.isArray(n))for(var i of n)"string"==typeof i?t.push(a(r,i)):t.push(L(r,Z(i)));else null!==n&&t.push(L(r,Z(n)));return t}function Cr(e,r){return("string"==typeof e?[e]:e).flatMap(e=>{var t;return e.trim().endsWith("}")?(ct(t=ne(e.replace("}","{@slot}}")),r),t):L(e,r)})}function $r(e,t,r){for(var a of ii(t))e.theme.addKeyframes(a)}function ii(e){let t=[];if("keyframes"in e.theme)for(var[r,a]of Object.entries(e.theme.keyframes))t.push(P("@keyframes",r,Z(a)));return t}var He={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(0.984 0.003 247.858)",100:"oklch(0.968 0.007 247.896)",200:"oklch(0.929 0.013 255.508)",300:"oklch(0.869 0.022 252.894)",400:"oklch(0.704 0.04 256.788)",500:"oklch(0.554 0.046 257.417)",600:"oklch(0.446 0.043 257.281)",700:"oklch(0.372 0.044 257.287)",800:"oklch(0.279 0.041 260.031)",900:"oklch(0.208 0.042 265.755)",950:"oklch(0.129 0.042 264.695)"},gray:{50:"oklch(0.985 0.002 247.839)",100:"oklch(0.967 0.003 264.542)",200:"oklch(0.928 0.006 264.531)",300:"oklch(0.872 0.01 258.338)",400:"oklch(0.707 0.022 261.325)",500:"oklch(0.551 0.027 264.364)",600:"oklch(0.446 0.03 256.802)",700:"oklch(0.373 0.034 259.733)",800:"oklch(0.278 0.033 256.848)",900:"oklch(0.21 0.034 264.665)",950:"oklch(0.13 0.028 261.692)"},zinc:{50:"oklch(0.985 0 0)",100:"oklch(0.967 0.001 286.375)",200:"oklch(0.92 0.004 286.32)",300:"oklch(0.871 0.006 286.286)",400:"oklch(0.705 0.015 286.067)",500:"oklch(0.552 0.016 285.938)",600:"oklch(0.442 0.017 285.786)",700:"oklch(0.37 0.013 285.805)",800:"oklch(0.274 0.006 286.033)",900:"oklch(0.21 0.006 285.885)",950:"oklch(0.141 0.005 285.823)"},neutral:{50:"oklch(0.985 0 0)",100:"oklch(0.97 0 0)",200:"oklch(0.922 0 0)",300:"oklch(0.87 0 0)",400:"oklch(0.708 0 0)",500:"oklch(0.556 0 0)",600:"oklch(0.439 0 0)",700:"oklch(0.371 0 0)",800:"oklch(0.269 0 0)",900:"oklch(0.205 0 0)",950:"oklch(0.145 0 0)"},stone:{50:"oklch(0.985 0.001 106.423)",100:"oklch(0.97 0.001 106.424)",200:"oklch(0.923 0.003 48.717)",300:"oklch(0.869 0.005 56.366)",400:"oklch(0.709 0.01 56.259)",500:"oklch(0.553 0.013 58.071)",600:"oklch(0.444 0.011 73.639)",700:"oklch(0.374 0.01 67.558)",800:"oklch(0.268 0.007 34.298)",900:"oklch(0.216 0.006 56.043)",950:"oklch(0.147 0.004 49.25)"},red:{50:"oklch(0.971 0.013 17.38)",100:"oklch(0.936 0.032 17.717)",200:"oklch(0.885 0.062 18.334)",300:"oklch(0.808 0.114 19.571)",400:"oklch(0.704 0.191 22.216)",500:"oklch(0.637 0.237 25.331)",600:"oklch(0.577 0.245 27.325)",700:"oklch(0.505 0.213 27.518)",800:"oklch(0.444 0.177 26.899)",900:"oklch(0.396 0.141 25.723)",950:"oklch(0.258 0.092 26.042)"},orange:{50:"oklch(0.98 0.016 73.684)",100:"oklch(0.954 0.038 75.164)",200:"oklch(0.901 0.076 70.697)",300:"oklch(0.837 0.128 66.29)",400:"oklch(0.75 0.183 55.934)",500:"oklch(0.705 0.213 47.604)",600:"oklch(0.646 0.222 41.116)",700:"oklch(0.553 0.195 38.402)",800:"oklch(0.47 0.157 37.304)",900:"oklch(0.408 0.123 38.172)",950:"oklch(0.266 0.079 36.259)"},amber:{50:"oklch(0.987 0.022 95.277)",100:"oklch(0.962 0.059 95.617)",200:"oklch(0.924 0.12 95.746)",300:"oklch(0.879 0.169 91.605)",400:"oklch(0.828 0.189 84.429)",500:"oklch(0.769 0.188 70.08)",600:"oklch(0.666 0.179 58.318)",700:"oklch(0.555 0.163 48.998)",800:"oklch(0.473 0.137 46.201)",900:"oklch(0.414 0.112 45.904)",950:"oklch(0.279 0.077 45.635)"},yellow:{50:"oklch(0.987 0.026 102.212)",100:"oklch(0.973 0.071 103.193)",200:"oklch(0.945 0.129 101.54)",300:"oklch(0.905 0.182 98.111)",400:"oklch(0.852 0.199 91.936)",500:"oklch(0.795 0.184 86.047)",600:"oklch(0.681 0.162 75.834)",700:"oklch(0.554 0.135 66.442)",800:"oklch(0.476 0.114 61.907)",900:"oklch(0.421 0.095 57.708)",950:"oklch(0.286 0.066 53.813)"},lime:{50:"oklch(0.986 0.031 120.757)",100:"oklch(0.967 0.067 122.328)",200:"oklch(0.938 0.127 124.321)",300:"oklch(0.897 0.196 126.665)",400:"oklch(0.841 0.238 128.85)",500:"oklch(0.768 0.233 130.85)",600:"oklch(0.648 0.2 131.684)",700:"oklch(0.532 0.157 131.589)",800:"oklch(0.453 0.124 130.933)",900:"oklch(0.405 0.101 131.063)",950:"oklch(0.274 0.072 132.109)"},green:{50:"oklch(0.982 0.018 155.826)",100:"oklch(0.962 0.044 156.743)",200:"oklch(0.925 0.084 155.995)",300:"oklch(0.871 0.15 154.449)",400:"oklch(0.792 0.209 151.711)",500:"oklch(0.723 0.219 149.579)",600:"oklch(0.627 0.194 149.214)",700:"oklch(0.527 0.154 150.069)",800:"oklch(0.448 0.119 151.328)",900:"oklch(0.393 0.095 152.535)",950:"oklch(0.266 0.065 152.934)"},emerald:{50:"oklch(0.979 0.021 166.113)",100:"oklch(0.95 0.052 163.051)",200:"oklch(0.905 0.093 164.15)",300:"oklch(0.845 0.143 164.978)",400:"oklch(0.765 0.177 163.223)",500:"oklch(0.696 0.17 162.48)",600:"oklch(0.596 0.145 163.225)",700:"oklch(0.508 0.118 165.612)",800:"oklch(0.432 0.095 166.913)",900:"oklch(0.378 0.077 168.94)",950:"oklch(0.262 0.051 172.552)"},teal:{50:"oklch(0.984 0.014 180.72)",100:"oklch(0.953 0.051 180.801)",200:"oklch(0.91 0.096 180.426)",300:"oklch(0.855 0.138 181.071)",400:"oklch(0.777 0.152 181.912)",500:"oklch(0.704 0.14 182.503)",600:"oklch(0.6 0.118 184.704)",700:"oklch(0.511 0.096 186.391)",800:"oklch(0.437 0.078 188.216)",900:"oklch(0.386 0.063 188.416)",950:"oklch(0.277 0.046 192.524)"},cyan:{50:"oklch(0.984 0.019 200.873)",100:"oklch(0.956 0.045 203.388)",200:"oklch(0.917 0.08 205.041)",300:"oklch(0.865 0.127 207.078)",400:"oklch(0.789 0.154 211.53)",500:"oklch(0.715 0.143 215.221)",600:"oklch(0.609 0.126 221.723)",700:"oklch(0.52 0.105 223.128)",800:"oklch(0.45 0.085 224.283)",900:"oklch(0.398 0.07 227.392)",950:"oklch(0.302 0.056 229.695)"},sky:{50:"oklch(0.977 0.013 236.62)",100:"oklch(0.951 0.026 236.824)",200:"oklch(0.901 0.058 230.902)",300:"oklch(0.828 0.111 230.318)",400:"oklch(0.746 0.16 232.661)",500:"oklch(0.685 0.169 237.323)",600:"oklch(0.588 0.158 241.966)",700:"oklch(0.5 0.134 242.749)",800:"oklch(0.443 0.11 240.79)",900:"oklch(0.391 0.09 240.876)",950:"oklch(0.293 0.066 243.157)"},blue:{50:"oklch(0.97 0.014 254.604)",100:"oklch(0.932 0.032 255.585)",200:"oklch(0.882 0.059 254.128)",300:"oklch(0.809 0.105 251.813)",400:"oklch(0.707 0.165 254.624)",500:"oklch(0.623 0.214 259.815)",600:"oklch(0.546 0.245 262.881)",700:"oklch(0.488 0.243 264.376)",800:"oklch(0.424 0.199 265.638)",900:"oklch(0.379 0.146 265.522)",950:"oklch(0.282 0.091 267.935)"},indigo:{50:"oklch(0.962 0.018 272.314)",100:"oklch(0.93 0.034 272.788)",200:"oklch(0.87 0.065 274.039)",300:"oklch(0.785 0.115 274.713)",400:"oklch(0.673 0.182 276.935)",500:"oklch(0.585 0.233 277.117)",600:"oklch(0.511 0.262 276.966)",700:"oklch(0.457 0.24 277.023)",800:"oklch(0.398 0.195 277.366)",900:"oklch(0.359 0.144 278.697)",950:"oklch(0.257 0.09 281.288)"},violet:{50:"oklch(0.969 0.016 293.756)",100:"oklch(0.943 0.029 294.588)",200:"oklch(0.894 0.057 293.283)",300:"oklch(0.811 0.111 293.571)",400:"oklch(0.702 0.183 293.541)",500:"oklch(0.606 0.25 292.717)",600:"oklch(0.541 0.281 293.009)",700:"oklch(0.491 0.27 292.581)",800:"oklch(0.432 0.232 292.759)",900:"oklch(0.38 0.189 293.745)",950:"oklch(0.283 0.141 291.089)"},purple:{50:"oklch(0.977 0.014 308.299)",100:"oklch(0.946 0.033 307.174)",200:"oklch(0.902 0.063 306.703)",300:"oklch(0.827 0.119 306.383)",400:"oklch(0.714 0.203 305.504)",500:"oklch(0.627 0.265 303.9)",600:"oklch(0.558 0.288 302.321)",700:"oklch(0.496 0.265 301.924)",800:"oklch(0.438 0.218 303.724)",900:"oklch(0.381 0.176 304.987)",950:"oklch(0.291 0.149 302.717)"},fuchsia:{50:"oklch(0.977 0.017 320.058)",100:"oklch(0.952 0.037 318.852)",200:"oklch(0.903 0.076 319.62)",300:"oklch(0.833 0.145 321.434)",400:"oklch(0.74 0.238 322.16)",500:"oklch(0.667 0.295 322.15)",600:"oklch(0.591 0.293 322.896)",700:"oklch(0.518 0.253 323.949)",800:"oklch(0.452 0.211 324.591)",900:"oklch(0.401 0.17 325.612)",950:"oklch(0.293 0.136 325.661)"},pink:{50:"oklch(0.971 0.014 343.198)",100:"oklch(0.948 0.028 342.258)",200:"oklch(0.899 0.061 343.231)",300:"oklch(0.823 0.12 346.018)",400:"oklch(0.718 0.202 349.761)",500:"oklch(0.656 0.241 354.308)",600:"oklch(0.592 0.249 0.584)",700:"oklch(0.525 0.223 3.958)",800:"oklch(0.459 0.187 3.815)",900:"oklch(0.408 0.153 2.432)",950:"oklch(0.284 0.109 3.907)"},rose:{50:"oklch(0.969 0.015 12.422)",100:"oklch(0.941 0.03 12.58)",200:"oklch(0.892 0.058 10.001)",300:"oklch(0.81 0.117 11.638)",400:"oklch(0.712 0.194 13.428)",500:"oklch(0.645 0.246 16.439)",600:"oklch(0.586 0.253 17.585)",700:"oklch(0.514 0.222 16.935)",800:"oklch(0.455 0.188 13.697)",900:"oklch(0.41 0.159 10.272)",950:"oklch(0.271 0.105 12.094)"}};function ue(e){return{__BARE_VALUE__:e}}var Y=ue(e=>{if(N(e.value))return e.value}),B=ue(e=>{if(N(e.value))return e.value+"%"}),te=ue(e=>{if(N(e.value))return e.value+"px"}),Nr=ue(e=>{if(N(e.value))return e.value+"ms"}),Ge=ue(e=>{if(N(e.value))return e.value+"deg"}),oi=ue(e=>{var t,r;if(null!==e.fraction)return[t,r]=_(e.fraction,"/"),N(t)&&N(r)?e.fraction:void 0}),Tr=ue(e=>{if(N(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),Vr={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...oi},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...B}),backdropContrast:({theme:e})=>({...e("contrast"),...B}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...B}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...Ge}),backdropInvert:({theme:e})=>({...e("invert"),...B}),backdropOpacity:({theme:e})=>({...e("opacity"),...B}),backdropSaturate:({theme:e})=>({...e("saturate"),...B}),backdropSepia:({theme:e})=>({...e("sepia"),...B}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentColor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...te},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...B},caretColor:({theme:e})=>e("colors"),colors:()=>({...He}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...Y},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...B},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...te}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...Y},flexShrink:{0:"0",DEFAULT:"1",...Y},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...B},grayscale:{0:"0",DEFAULT:"100%",...B},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Tr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Tr},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...Ge},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...B},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...Y},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...B},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...Y},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentColor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...Ge},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...B},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...B},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...B},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...Ge},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...Y},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...te},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Nr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Nr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...Y}};function Sr(e){return{theme:{...Vr,colors:({theme:e})=>e("color",{}),extend:{fontSize:({theme:e})=>({...e("text",{})}),boxShadow:({theme:e})=>({...e("shadow",{})}),animation:({theme:e})=>({...e("animate",{})}),aspectRatio:({theme:e})=>({...e("aspect",{})}),borderRadius:({theme:e})=>({...e("radius",{})}),screens:({theme:e})=>({...e("breakpoint",{})}),letterSpacing:({theme:e})=>({...e("tracking",{})}),lineHeight:({theme:e})=>({...e("leading",{})}),transitionDuration:{DEFAULT:e.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:e.get(["--default-transition-timing-function"])??null},maxWidth:({theme:e})=>({...e("container",{})})}}}}var li={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function ht(e,t){let r={design:e,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(li)};for(var a of t)mt(r,a);for(var n of r.configs)"darkMode"in n&&void 0!==n.darkMode&&(r.result.darkMode=n.darkMode??null),"prefix"in n&&void 0!==n.prefix&&(r.result.prefix=n.prefix??""),"blocklist"in n&&void 0!==n.blocklist&&(r.result.blocklist=n.blocklist??[]),"important"in n&&void 0!==n.important&&(r.result.important=n.important??!1);e=si(r);return{resolvedConfig:{...r.result,content:r.content,theme:r.theme,plugins:r.plugins},replacedThemeKeys:e}}function ai(e,t){return Array.isArray(e)&&ve(e[0])?e.concat(t):Array.isArray(t)&&ve(t[0])&&ve(e)?[e,...t]:Array.isArray(t)?t:void 0}function mt(e,{config:t,base:r,path:a,reference:n}){let i=[];for(var o of t.plugins??[])"__isOptionsFunction"in o?i.push({...o(),reference:n}):"handler"in o?i.push({...o,reference:n}):i.push({handler:o,reference:n});if(Array.isArray(t.presets)&&0===t.presets.length)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(var l of t.presets??[])mt(e,{path:a,base:r,config:l,reference:n});for(var s of i)e.plugins.push(s),s.config&&mt(e,{path:a,base:r,config:s.config,reference:!!s.reference});var u,c=t.content??[];for(u of Array.isArray(c)?c:c.files)e.content.files.push("object"==typeof u?u:{base:r,pattern:u});e.configs.push(t)}function si(t){let e=new Set,r=We(t.design,()=>t.theme,n),a=Object.assign(r,{theme:r,colors:He});function n(e){return"function"==typeof e?e(a)??null:e??null}for(var i of t.configs){var o,l,i=i.theme??{},s=i.extend??{};for(o in i)"extend"!==o&&e.add(o);for(l in Object.assign(t.theme,i),s)t.extend[l]??=[],t.extend[l].push(s[l])}for(var u in delete t.theme.extend,t.extend){let e=[t.theme[u],...t.extend[u]];t.theme[u]=()=>{return $e({},e.map(n),ai)}}for(var c in t.theme)t.theme[c]=n(t.theme[c]);if(t.theme.screens&&"object"==typeof t.theme.screens)for(var d of Object.keys(t.theme.screens)){var f=t.theme.screens[d];!f||"object"!=typeof f||"raw"in f||"max"in f||"min"in f&&(t.theme.screens[d]=f.min)}return e}function Er(t,r){t=t.theme.container||{};if("object"==typeof t&&null!==t){let e=ui(t,r);0!==e.length&&r.utilities.static("container",()=>structuredClone(e))}}function ui({center:t,padding:r,screens:n},i){let o=[],l=null;if(t&&o.push(a("margin-inline","auto")),("string"==typeof r||"object"==typeof r&&null!==r&&"DEFAULT"in r)&&o.push(a("padding-inline","string"==typeof r?r:r.DEFAULT)),"object"==typeof n&&null!==n){l=new Map;let e=Array.from(i.theme.namespace("--breakpoint").entries());var s,u;e.sort((e,t)=>le(e[1],t[1],"asc")),0<e.length&&([t]=e[0],o.push(P("@media",`(width >= --theme(--breakpoint-${t}))`,[a("max-width","none")])));for([s,u]of Object.entries(n)){if("object"==typeof u){if(!("min"in u))continue;u=u.min}l.set(s,P("@media",`(width >= ${u})`,[a("max-width",u)]))}}if("object"==typeof r&&null!==r){let e=Object.entries(r).filter(([e])=>"DEFAULT"!==e).map(([e,t])=>[e,i.theme.resolveValue(e,["--breakpoint"]),t]).filter(Boolean);e.sort((e,t)=>le(e[1],t[1],"asc"));for(var[c,,d]of e)if(l&&l.has(c))l.get(c).nodes.push(a("padding-inline",d));else{if(l)continue;o.push(P("@media",`(width >= theme(--breakpoint-${c}))`,[a("padding-inline",d)]))}}if(l)for(var[,e]of l)o.push(e);return o}function Rr({addVariant:e,config:t}){let r=t("darkMode",null),[a,n=".dark"]=Array.isArray(r)?r:[r];if("variant"===a){let e;if(Array.isArray(n)||"function"==typeof n?e=n:"string"==typeof n&&(e=[n]),Array.isArray(e))for(var i of e)".dark"===i?(a=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):i.includes("&")||(a=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=e}null!==a&&("selector"===a?e("dark",`&:where(${n}, ${n} *)`):"media"===a?e("dark","@media (prefers-color-scheme: dark)"):"variant"===a?e("dark",n):"class"===a&&e("dark",`&:is(${n} *)`))}function Or(r){for(let[e,t]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])r.utilities.static("bg-gradient-to-"+e,()=>[a("--tw-gradient-position",`to ${t} in oklab,`),a("background-image","linear-gradient(var(--tw-gradient-stops))")]);r.utilities.functional("max-w-screen",e=>{if(e.value&&"arbitrary"!==e.value.kind)return e=r.theme.resolve(e.value.value,["--breakpoint"]),e?[a("max-width",e)]:void 0}),r.utilities.static("overflow-ellipsis",()=>[a("text-overflow","ellipsis")]),r.utilities.static("decoration-slice",()=>[a("-webkit-box-decoration-break","slice"),a("box-decoration-break","slice")]),r.utilities.static("decoration-clone",()=>[a("-webkit-box-decoration-break","clone"),a("box-decoration-break","clone")]),r.utilities.functional("flex-shrink",e=>{if(!e.modifier)return e.value?"arbitrary"===e.value.kind||N(e.value.value)?[a("flex-shrink",e.value.value)]:void 0:[a("flex-shrink","1")]}),r.utilities.functional("flex-grow",e=>{if(!e.modifier)return e.value?"arbitrary"===e.value.kind||N(e.value.value)?[a("flex-grow",e.value.value)]:void 0:[a("flex-grow","1")]})}function Kr(e,n){let t=e.theme.screens||{},i=n.variants.get("min")?.order??0,o=[];for(let[r,a]of Object.entries(t)){var l=function(e){n.variants.static(r,e=>{e.nodes=[P("@media",t,e.nodes)]},{order:e})},s=n.variants.get(r),u=n.theme.resolveValue(r,["--breakpoint"]);if(s&&u&&!n.theme.hasDefault("--breakpoint-"+r))continue;let e=!0,t=("string"==typeof a&&(e=!1),ci(a));e?o.push(l):l(i)}if(0!==o.length){for(var[,r]of n.variants.variants)r.order>i&&(r.order+=o.length);n.variants.compareFns=new Map(Array.from(n.variants.compareFns).map(([e,t])=>(e>i&&(e+=o.length),[e,t])));for(var[a,c]of o.entries())c(i+a+1)}}function ci(e){return(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?{min:e}:e&&"object"==typeof e?e:null).map(e=>{if(null===e)return null;if("raw"in e)return e.raw;let t="";return void 0!==e.max&&(t+=e.max+" >= "),t+="width",void 0!==e.min&&(t+=" >= "+e.min),`(${t})`}).filter(Boolean).join(", ")}function _r(e,r){let n=e.theme.aria||{},i=e.theme.supports||{},o=e.theme.data||{};if(0<Object.keys(n).length){let e=r.variants.get("aria"),a=e?.applyFn,t=e?.compounds;r.variants.functional("aria",(e,t)=>{var r=t.value;return r&&"named"===r.kind&&r.value in n?a?.(e,{...t,value:{kind:"arbitrary",value:n[r.value]}}):a?.(e,t)},{compounds:t})}if(0<Object.keys(i).length){let e=r.variants.get("supports"),a=e?.applyFn,t=e?.compounds;r.variants.functional("supports",(e,t)=>{var r=t.value;return r&&"named"===r.kind&&r.value in i?a?.(e,{...t,value:{kind:"arbitrary",value:i[r.value]}}):a?.(e,t)},{compounds:t})}if(0<Object.keys(o).length){let e=r.variants.get("data"),a=e?.applyFn,t=e?.compounds;r.variants.functional("data",(e,t)=>{var r=t.value;return r&&"named"===r.kind&&r.value in o?a?.(e,{...t,value:{kind:"arbitrary",value:o[r.value]}}):a?.(e,t)},{compounds:t})}}var fi=/^[a-z]+$/;async function Dr({designSystem:t,base:r,ast:a,loadModule:n,globs:i}){let l=0,s=[],e=[],o=(D(a,(r,{parent:t,replaceWith:a,context:n})=>{if("at-rule"===r.kind){if("@plugin"===r.name){if(null!==t)throw new Error("`@plugin` cannot be nested.");var i,o=r.params.slice(1,-1);if(0===o.length)throw new Error("`@plugin` must have a path.");let e={};for(let t of r.nodes??[]){if("declaration"!==t.kind)throw new Error(`Unexpected \`@plugin\` option:

${G([t])}

\`@plugin\` options must be a flat list of declarations.`);void 0!==t.value&&(i=_(t.value,",").map(e=>{if("null"===(e=e.trim()))return null;if("true"===e)return!0;if("false"===e)return!1;if(!Number.isNaN(Number(e)))return Number(e);if('"'===e[0]&&'"'===e[e.length-1]||"'"===e[0]&&"'"===e[e.length-1])return e.slice(1,-1);if("{"===e[0]&&"}"===e[e.length-1])throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${G([t]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`);return e}),e[t.property]=1===i.length?i[0]:i)}return s.push([{id:o,base:n.base,reference:!!n.reference},0<Object.keys(e).length?e:null]),a([]),void(l|=4)}if("@config"===r.name){if(0<r.nodes.length)throw new Error("`@config` cannot have a body.");if(null!==t)throw new Error("`@config` cannot be nested.");e.push({id:r.params.slice(1,-1),base:n.base,reference:!!n.reference}),a([]),l|=4}}}),Or(t),t.resolveThemeValue);if(t.resolveThemeValue=function(e){return e.startsWith("--")?o(e):(l|=Pr({designSystem:t,base:r,ast:a,globs:i,configs:[],pluginDetails:[]}),t.resolveThemeValue(e))},!s.length&&!e.length)return 0;var[u,c]=await Promise.all([Promise.all(e.map(async({id:e,base:t,reference:r})=>{t=await n(e,t,"config");return{path:e,base:t.base,config:t.module,reference:r}})),Promise.all(s.map(async([{id:e,base:t,reference:r},a])=>{t=await n(e,t,"plugin");return{path:e,base:t.base,plugin:t.module,options:a,reference:r}}))]);return l|=Pr({designSystem:t,base:r,ast:a,globs:i,configs:u,pluginDetails:c})}function Pr({designSystem:e,base:t,ast:r,globs:a,configs:n,pluginDetails:i}){let o=0,l=[...i.map(e=>{if(!e.options)return{config:{plugins:[e.plugin]},base:e.base,reference:e.reference};if("__isOptionsFunction"in e.plugin)return{config:{plugins:[e.plugin(e.options)]},base:e.base,reference:e.reference};throw new Error(`The plugin "${e.path}" does not accept options`)}),...n],s=ht(e,[{config:Sr(e.theme),base:t,reference:!0},...l,{config:{plugins:[Rr]},base:t,reference:!0}])["resolvedConfig"],{resolvedConfig:u,replacedThemeKeys:c}=ht(e,l),d=(e.resolveThemeValue=function(e,t){let r=f.theme(e,t);return Array.isArray(r)&&2===r.length?r[0]:Array.isArray(r)?r.join(", "):"string"==typeof r?r:void 0},{designSystem:e,ast:r,resolvedConfig:s,featuresRef:{set current(e){o|=e}}}),f=gt({...d,referenceMode:!1}),p;for(var{handler:h,reference:m}of s.plugins)m?(p||=gt({...d,referenceMode:!0}),h(p)):h(f);if(sr(e,u,c),$r(e,u,c),_r(u,e),Kr(u,e),Er(u,e),!e.theme.prefix&&s.prefix){if(s.prefix.endsWith("-")&&(s.prefix=s.prefix.slice(0,-1),console.warn(`The prefix "${s.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!fi.test(s.prefix))throw new Error(`The prefix "${s.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);e.theme.prefix=s.prefix}if(e.important||!0!==s.important||(e.important=!0),"string"==typeof s.important){let a=s.important;D(r,(e,{replaceWith:t,parent:r})=>{if("at-rule"===e.kind&&"@tailwind"===e.name&&"utilities"===e.params)return"rule"===r?.kind&&r.selector===a||t(U(a,[e])),2})}for(var v of s.blocklist)e.invalidCandidates.add(v);for(var g of s.content.files){if("raw"in g)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(g,null,2)}

This feature is not currently supported.`);a.push(g)}return o}var di=/^[a-z]+$/;function pi(){throw new Error("No `loadModule` function provided to `compile`")}function gi(){throw new Error("No `loadStylesheet` function provided to `compile`")}function mi(e){let t=0,r=null;for(var a of _(e," "))"reference"===a?t|=2:"inline"===a?t|=1:"default"===a?t|=4:a.startsWith("prefix(")&&a.endsWith(")")&&(r=a.slice(7,-1));return[t,r]}var pe=(e=>(e[e.None=0]="None",e[e.AtApply=1]="AtApply",e[e.AtImport=2]="AtImport",e[e.JsPluginCompat=4]="JsPluginCompat",e[e.ThemeFunction=8]="ThemeFunction",e[e.Utilities=16]="Utilities",e[e.Variants=32]="Variants",e))(pe||{});async function jr(e,{base:t="",loadModule:r=pi,loadStylesheet:n=gi}={}){let u=0,c=(e=[ie({base:t},e)],u|=await dt(e,t,n),null),d=new Ie,f=[],p=[],h=null,m=null,v=[],g=[],w=null,i=(D(e,(a,{parent:n,replaceWith:i,context:o})=>{if("at-rule"===a.kind){if("@tailwind"===a.name&&("utilities"===a.params||a.params.startsWith("utilities"))){if(null!==m)return void i([]);var t;for(t of _(a.params," "))if(t.startsWith("source(")){let e=t.slice(7,-1);if("none"===e){w=e;continue}if('"'===e[0]&&'"'!==e[e.length-1]||"'"===e[0]&&"'"!==e[e.length-1]||"'"!==e[0]&&'"'!==e[0])throw new Error("`source(…)` paths must be quoted.");w={base:o.sourceBase??o.base,pattern:e.slice(1,-1)}}m=a,u|=16}if("@utility"===a.name){if(null!==n)throw new Error("`@utility` cannot be nested.");if(0===a.nodes.length)throw new Error(`\`@utility ${a.params}\` is empty. Utilities should include at least one property.`);var e=Wt(a);if(null===e)throw new Error(`\`@utility ${a.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);p.push(e)}if("@source"===a.name){if(0<a.nodes.length)throw new Error("`@source` cannot have a body.");if(null!==n)throw new Error("`@source` cannot be nested.");let e=a.params;if('"'===e[0]&&'"'!==e[e.length-1]||"'"===e[0]&&"'"!==e[e.length-1]||"'"!==e[0]&&'"'!==e[0])throw new Error("`@source` paths must be quoted.");return g.push({base:o.base,pattern:e.slice(1,-1)}),void i([])}if("@variant"===a.name&&(null===n?0===a.nodes.length?a.name="@custom-variant":D(a.nodes,e=>{if("at-rule"===e.kind&&"@slot"===e.name)return a.name="@custom-variant",2}):v.push(a)),"@custom-variant"===a.name){if(null!==n)throw new Error("`@custom-variant` cannot be nested.");i([]);let[t,r]=_(a.params," ");if(!ze.test(t))throw new Error(`\`@custom-variant ${t}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(0<a.nodes.length&&r)throw new Error(`\`@custom-variant ${t}\` cannot have both a selector and a body.`);if(0!==a.nodes.length)return void f.push(e=>{e.variants.fromAst(t,a.nodes)});{if(!r)throw new Error(`\`@custom-variant ${t}\` has no selector or body.`);let e=_(r.slice(1,-1),",");if(0===e.length||e.some(e=>""===e.trim()))throw new Error(`\`@custom-variant ${t} (${e.join(",")})\` selector is invalid.`);let a=[],n=[];for(var l of e)l=l.trim(),("@"===l[0]?a:n).push(l);void f.push(e=>{e.variants.static(t,e=>{let t=[];0<n.length&&t.push(U(n.join(", "),e.nodes));for(var r of a)t.push(L(r,e.nodes));e.nodes=t},{compounds:se([...n,...a])})})}}else{if("@media"===a.name){let e=_(a.params," "),t=[];for(var s of e)if(s.startsWith("source(")){let r=s.slice(7,-1);D(a.nodes,(e,{replaceWith:t})=>{if("at-rule"===e.kind&&"@tailwind"===e.name&&"utilities"===e.params)return e.params+=` source(${r})`,t([ie({sourceBase:o.base},[e])]),2})}else if(s.startsWith("theme(")){let t=s.slice(6,-1);D(a.nodes,e=>{if("at-rule"!==e.kind)throw new Error('Files imported with `@import "…" theme(…)` must only contain `@theme` blocks.');if("@theme"===e.name)return e.params+=" "+t,1})}else if(s.startsWith("prefix(")){let t=s.slice(7,-1);D(a.nodes,e=>{if("at-rule"===e.kind&&"@theme"===e.name)return e.params+=` prefix(${t})`,1})}else"important"===s?c=!0:"reference"===s?a.nodes=[ie({reference:!0},a.nodes)]:t.push(s);return 0<t.length?a.params=t.join(" "):0<e.length&&i(a.nodes),1}if("@theme"===a.name){let[r,e]=mi(a.params);if(o.reference&&(r|=2),e){if(!di.test(e))throw new Error(`The prefix "${e}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);d.prefix=e}return D(a.nodes,(e,{replaceWith:t})=>{if("at-rule"===e.kind&&"@keyframes"===e.name)return d.addKeyframes(e),t([]),1;if("comment"!==e.kind){if("declaration"!==e.kind||!e.property.startsWith("--"))throw t=G([P(a.name,a.params,[e])]).split(`
`).map((e,t,r)=>`${0===t||t>=r.length-2?" ":">"} `+e).join(`
`),new Error("`@theme` blocks must only contain custom properties or `@keyframes`.\n\n"+t);d.add(e.property,e.value??"",r)}}),h||2&r?i([]):i([h=U(":root, :host",a.nodes)]),1}}}}),Xt(d));c&&(i.important=c),u|=await Dr({designSystem:i,base:t,ast:e,loadModule:r,globs:g});for(var o of f)o(i);for(var l of p)l(i);if(h){let t=[];for(var[s,b]of d.entries())2&b.options||t.push(a(s,b.value));n=d.getKeyframes();if(0<n.length){let e=[...d.namespace("--animate").values()].flatMap(e=>e.split(" "));for(var y of n){var k=y.params;e.includes(k)&&t.push(j([y]))}}h.nodes=t}if(m){let e=m;e.kind="context",e.context={}}if(0<v.length){for(var x of v){var $=U("&",x.nodes),A=x.params,z=i.parseVariant(A);if(null===z)throw new Error("Cannot use `@variant` with unknown variant: "+A);if(null===ge($,z,i.variants))throw new Error("Cannot use `@variant` with variant: "+A);Object.assign(x,$)}u|=32}return u=(u|=de(e,i))|Ce(e,i),D(e,(e,{replaceWith:t})=>{if("at-rule"===e.kind)return"@utility"===e.name&&t([]),1}),{designSystem:i,ast:e,globs:g,root:w,utilitiesNode:m,features:u}}async function Ur(n,e={}){let{designSystem:i,ast:o,globs:t,root:r,utilitiesNode:l,features:s}=await jr(n,e);function u(e){i.invalidCandidates.add(e)}o.unshift(Ke(`! tailwindcss v${vt} | MIT License | https://tailwindcss.com `));let c=new Set,d=null,f=0;return{globs:t,root:r,features:s,build(e){if(0===s)return n;if(!l)return d??=oe(o);let t=!1,r=c.size;for(var a of e)i.invalidCandidates.has(a)||(c.add(a),t||=c.size!==r);if(!t)return d??=oe(o);e=ee(c,i,{onInvalidCandidate:u}).astNodes;return f===e.length?d??=oe(o):(f=e.length,l.nodes=e,d=oe(o)),d}}}async function hi(e,t={}){let r=ne(e),a=await Ur(r,t),n=r,i=e;return{...a,build(e){e=a.build(e);return e!==n&&(i=G(e),n=e),i}}}async function vi(e,t={}){return(await jr(ne(e),t)).designSystem}function Te(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let e in Je)"default"!==e&&(Te[e]=Je[e]);module.exports=Te;