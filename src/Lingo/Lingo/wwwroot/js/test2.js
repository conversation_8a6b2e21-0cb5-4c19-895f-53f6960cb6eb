export class Calculator {
    add(a, b) {
        return a + b;
    }
    subtract(a, b) {
        return a - b;
    }
    multiply(a, b) {
        return a * b;
    }
    divide(a, b) {
        if (b === 0) {
            throw new Error("Division by zero is not allowed");
        }
        return a / b;
    }
}
export class CalculatorTests {
    constructor() {
        this.calculator = new Calculator();
    }
    runTests() {
        this.testAddition();
        this.testSubtraction();
        this.testMultiplication();
        this.testDivision();
        console.log("All tests completed successfully!");
    }
    testAddition() {
        const result = this.calculator.add(5, 3);
        if (result !== 8) {
            throw new Error(`Addition test failed: expected 8, got ${result}`);
        }
    }
    testSubtraction() {
        const result = this.calculator.subtract(10, 4);
        if (result !== 6) {
            throw new Error(`Subtraction test failed: expected 6, got ${result}`);
        }
    }
    testMultiplication() {
        const result = this.calculator.multiply(6, 7);
        if (result !== 42) {
            throw new Error(`Multiplication test failed: expected 42, got ${result}`);
        }
    }
    testDivision() {
        const result = this.calculator.divide(15, 3);
        if (result !== 5) {
            throw new Error(`Division test failed: expected 5, got ${result}`);
        }
    }
}
// Run the tests
const tests = new CalculatorTests();
tests.runTests();
//# sourceMappingURL=test2.js.map