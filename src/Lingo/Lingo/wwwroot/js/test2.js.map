{"version": 3, "file": "test2.js", "sourceRoot": "", "sources": ["../../scripts/test2.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,UAAU;IACZ,GAAG,CAAC,CAAS,EAAE,CAAS;QAC3B,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,QAAQ,CAAC,CAAS,EAAE,CAAS;QAChC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,QAAQ,CAAC,CAAS,EAAE,CAAS;QAChC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,CAAS,EAAE,CAAS;QAC9B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;CACJ;AAED,MAAM,OAAO,eAAe;IAGxB;QACI,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACvC,CAAC;IAEM,QAAQ;QACX,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACrD,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,MAAM,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,kBAAkB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,gDAAgD,MAAM,EAAE,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC;AACpC,KAAK,CAAC,QAAQ,EAAE,CAAC"}