/* Lingo Global Styles */

/* Common button styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #58cc02;
    color: white;
}

.btn-primary:hover {
    background: #4caf00;
}

.btn-secondary {
    background: #e5e5e5;
    color: #3c3c3c;
}

.btn-secondary:hover {
    background: #d0d0d0;
}

.btn-outline {
    background: transparent;
    border: 2px solid #58cc02;
    color: #58cc02;
}

.btn-outline:hover {
    background: #58cc02;
    color: white;
}

/* Loading states */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2em;
    color: #777;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #58cc02;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #58cc02, #89e219);
    transition: width 0.3s ease;
}

/* Difficulty badges */
.difficulty {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-beginner {
    background: #e8f5e8;
    color: #58cc02;
}

.difficulty-intermediate {
    background: #fff3cd;
    color: #ffc107;
}

.difficulty-advanced {
    background: #f8d7da;
    color: #dc3545;
}

/* Cards */
.card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.card-clickable {
    cursor: pointer;
}

.card-clickable:hover {
    border-color: #58cc02;
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #777;
}

.empty-state h3 {
    margin-bottom: 16px;
    color: #3c3c3c;
}

.empty-state p {
    margin-bottom: 24px;
    line-height: 1.6;
}

.empty-state a {
    color: #1cb0f6;
    text-decoration: none;
    font-weight: 600;
}

.empty-state a:hover {
    text-decoration: underline;
}

/* Badges and achievements */
.badge-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.badge-item img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #f0f0f0;
}

.badge-item span {
    font-size: 0.9em;
    color: #777;
    font-weight: 500;
}

/* XP and score displays */
.xp-badge {
    background: #ffc107;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.score-display {
    text-align: center;
    padding: 20px;
}

.score-number {
    display: block;
    font-size: 3em;
    font-weight: 700;
    color: #58cc02;
    margin-bottom: 8px;
}

.score-label {
    color: #777;
    font-size: 1.1em;
}

/* Responsive utilities */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-sm {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* Spacing utilities */
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 40px; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 40px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }
.p-5 { padding: 40px; }

/* Flexbox utilities */
.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.align-items-center {
    align-items: center;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }
.gap-3 { gap: 24px; }
.gap-4 { gap: 32px; }

/* Color utilities */
.text-primary {
    color: #58cc02;
}

.text-secondary {
    color: #777;
}

.text-muted {
    color: #999;
}

.text-success {
    color: #58cc02;
}

.text-warning {
    color: #ffc107;
}

.text-danger {
    color: #dc3545;
}

.bg-primary {
    background-color: #58cc02;
    color: white;
}

.bg-light {
    background-color: #f8f9fa;
}

.bg-white {
    background-color: white;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
    .container,
    .container-sm {
        padding: 0 16px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9em;
    }
    
    .score-number {
        font-size: 2.5em;
    }
}
