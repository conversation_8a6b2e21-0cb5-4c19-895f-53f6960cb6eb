using JasperFx;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Lingo.Components;
using Lingo.Components.Account;
using Lingo.Data;
using Lingo.Infrastructure.Data;
using Lingo.Infrastructure.EventHandlers;
using Lingo.Services.Extensions;
using Marten;
using Wolverine;
using Wolverine.EntityFrameworkCore;
using Serilog;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
builder.Host.UseSerilog((context, services, configuration) => configuration
    .ReadFrom.Configuration(context.Configuration)
    .ReadFrom.Services(services)
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName)
    .WriteTo.Console()
    .WriteTo.File(
        path: "logs/lingo-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        shared: true,
        flushToDiskInterval: TimeSpan.FromSeconds(1)));

// Configure OpenTelemetry
var serviceName = builder.Configuration["OpenTelemetry:ServiceName"] ?? "Lingo";
var serviceVersion = builder.Configuration["OpenTelemetry:ServiceVersion"] ?? "1.0.0";

builder.Services.AddOpenTelemetry()
    .ConfigureResource(resource => resource
        .AddService(serviceName: serviceName, serviceVersion: serviceVersion)
        .AddAttributes(new Dictionary<string, object>
        {
            ["deployment.environment"] = builder.Environment.EnvironmentName
        }))
    .WithTracing(tracing => tracing
        .AddAspNetCoreInstrumentation(options =>
        {
            options.RecordException = true;
            options.Filter = (httpContext) =>
            {
                // Filter out health check and static asset requests
                var path = httpContext.Request.Path.Value?.ToLowerInvariant();
                return !path?.Contains("/health") == true &&
                       !path?.Contains("/_framework") == true &&
                       !path?.Contains("/css") == true &&
                       !path?.Contains("/js") == true;
            };
        })
        .AddHttpClientInstrumentation(options =>
        {
            options.RecordException = true;
        })
        .AddEntityFrameworkCoreInstrumentation(options =>
        {
            options.SetDbStatementForText = true;
            options.SetDbStatementForStoredProcedure = true;
        })
        .AddConsoleExporter())
    .WithMetrics(metrics => metrics
        .AddAspNetCoreInstrumentation()
        .AddHttpClientInstrumentation()
        .AddConsoleExporter());

// Add OpenTelemetry console exporter in development
if (builder.Environment.IsDevelopment() &&
    builder.Configuration.GetValue<bool>("OpenTelemetry:EnableConsoleExporter"))
{
    builder.Services.Configure<OpenTelemetryLoggerOptions>(logging => logging.AddConsoleExporter());
}

builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddMvc();
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents()
    .AddAuthenticationStateSerialization();

builder.Services.AddOpenApi();
builder.Services.AddOpenTelemetry();
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = IdentityConstants.ApplicationScheme;
        options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
    })
    .AddIdentityCookies();

// Database configurations
builder.AddNpgsqlDataSource("lingodb");
var lingoConnectionString = builder.Configuration.GetConnectionString("lingodb") ?? "";

// Identity database (keep existing)
builder.AddNpgsqlDbContext<LingoDbContext>("lingodb");
builder.AddNpgsqlDbContext<ApplicationDbContext>(connectionName: "lingodb");
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

// Marten for document storage
builder.Services.AddMarten(options =>
{
    options.Connection(lingoConnectionString);
    options.AutoCreateSchemaObjects = AutoCreate.All;
    options.DatabaseSchemaName = "marten";
});

// Redis caching
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
});

// Orleans configuration
builder.Host.UseOrleans((context, siloBuilder) =>
{
    // if (context.HostingEnvironment.IsDevelopment())
    // {
    siloBuilder.UseLocalhostClustering();
    siloBuilder.UseInMemoryReminderService();
    siloBuilder.AddMemoryGrainStorageAsDefault();
    // }
    // else
    // {
    //     siloBuilder.UseAdoNetClustering(options =>
    //     {
    //         options.ConnectionString = lingoConnectionString;
    //         options.Invariant = "Npgsql";
    //     });
    //     siloBuilder.UseAdoNetReminderService(options =>
    //     {
    //         options.ConnectionString = lingoConnectionString;
    //         options.Invariant = "Npgsql";
    //     });
    //     siloBuilder.AddAdoNetGrainStorageAsDefault(options =>
    //     {
    //         options.ConnectionString = lingoConnectionString;
    //         options.Invariant = "Npgsql";
    //     });
    // }
});

// Wolverine for messaging
builder.Host.UseWolverine(opts =>
{
    opts.UseEntityFrameworkCoreTransactions();
    opts.Services.AddScoped<UserEventHandlers>();
});

// API Controllers
builder.Services.AddControllers();

builder.Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();

// Add data seeder
builder.Services.AddScoped<DataSeeder>();

// Add typed HttpClients for API endpoints
builder.Services.AddLingoApiClients(builder.Configuration);

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
    app.UseMigrationsEndPoint();
    
    // Seed the database in development
    using (var scope = app.Services.CreateScope())
    {
        var seeder = scope.ServiceProvider.GetRequiredService<DataSeeder>();
        await seeder.SeedAsync();
    }
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseAntiforgery();

app.MapStaticAssets();
app.MapOpenApi();
app.MapScalarApiReference();
app.MapRazorPages();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(Lingo.Client._Imports).Assembly);

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();

// Map API controllers
app.MapControllers();

app.Run();



public partial class Program { }