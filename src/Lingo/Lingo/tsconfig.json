{"compileOnSave": true, "allowJs": false, "compilerOptions": {"noImplicitAny": false, "noEmitOnError": true, "removeComments": false, "sourceMap": true, "target": "ES2020", "skipLibCheck": true, "esModuleInterop": true, "rootDir": "scripts", "outDir": "wwwroot/js"}, "include": ["scripts/**/*.ts", "scripts/**/*.tsx", "Components/**/*.ts", "ClientApp/**/*.ts", "Areas/**/*.ts", "Pages/**/*.ts", "Views/**/*.ts"], "exclude": ["wwwroot/js/**/*", "node_modules", "bin/**/*", "obj/**/*"]}