export class Calculator {
    public add(a: number, b: number): number {
        return a + b;
    }

    public subtract(a: number, b: number): number {
        return a - b;
    }

    public multiply(a: number, b: number): number {
        return a * b;
    }

    public divide(a: number, b: number): number {
        if (b === 0) {
            throw new Error("Division by zero is not allowed");
        }
        return a / b;
    }
}

export class CalculatorTests {
    private calculator: Calculator;

    constructor() {
        this.calculator = new Calculator();
    }

    public runTests(): void {
        this.testAddition();
        this.testSubtraction();
        this.testMultiplication();
        this.testDivision();
        console.log("All tests completed successfully!");
    }

    private testAddition(): void {
        const result = this.calculator.add(5, 3);
        if (result !== 8) {
            throw new Error(`Addition test failed: expected 8, got ${result}`);
        }
    }

    private testSubtraction(): void {
        const result = this.calculator.subtract(10, 4);
        if (result !== 6) {
            throw new Error(`Subtraction test failed: expected 6, got ${result}`);
        }
    }

    private testMultiplication(): void {
        const result = this.calculator.multiply(6, 7);
        if (result !== 42) {
            throw new Error(`Multiplication test failed: expected 42, got ${result}`);
        }
    }

    private testDivision(): void {
        const result = this.calculator.divide(15, 3);
        if (result !== 5) {
            throw new Error(`Division test failed: expected 5, got ${result}`);
        }
    }
}

// Run the tests
const tests = new CalculatorTests();
tests.runTests();
