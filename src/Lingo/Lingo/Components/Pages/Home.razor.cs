using Lingo.Core.Models;
using Microsoft.AspNetCore.Components;

namespace Lingo.Components.Pages;

public partial class Home
{
    [Inject] private HttpClient Http { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<Home> Logger { get; set; } = default!;

    private List<Course>? courses;
    private List<UserCourseProgress> userProgress = new();
    private bool showCourses = false;
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        // In a real app, you'd check if user is authenticated
        await LoadUserProgress();
    }

    private async Task ShowCourses()
    {
        showCourses = true;
        if (courses == null)
        {
            await LoadCourses();
        }
    }

    private async Task LoadCourses()
    {
        try
        {
            courses = await Http.GetFromJsonAsync<List<Course>>("api/courses");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading courses");
            courses = new List<Course>();
        }
    }

    private async Task LoadUserProgress()
    {
        try
        {
            userProgress = await Http.GetFromJsonAsync<List<UserCourseProgress>>($"api/users/{currentUserId}/progress") ?? new();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading user progress for user {UserId}", currentUserId);
            userProgress = new List<UserCourseProgress>();
        }
    }

    private UserCourseProgress? GetCourseProgress(long courseId)
    {
        return userProgress.FirstOrDefault(p => p.CourseId == courseId);
    }

    private void LoginWithGoogle()
    {
        // Placeholder for Google OAuth
        Navigation.NavigateTo("/Account/Login");
    }

    private void LoginWithFacebook()
    {
        // Placeholder for Facebook OAuth
        Navigation.NavigateTo("/Account/Login");
    }
}
