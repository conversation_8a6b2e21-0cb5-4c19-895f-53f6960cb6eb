using Lingo.Core.Models;
using Microsoft.AspNetCore.Components;

namespace Lingo.Components.Pages;

public partial class LessonDetail
{
    [Parameter] public long LessonId { get; set; }
    [Inject] private HttpClient Http { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<LessonDetail> Logger { get; set; } = default!;
    
    private Lesson? lesson;
    private List<LessonQuestion> questions = new();
    private Dictionary<long, List<long>> selectedAnswers = new();
    private int currentQuestionIndex = 0;
    private bool hasAnswered = false;
    private bool isCompleted = false;
    private int correctAnswers = 0;
    private List<Badge> earnedBadges = new();
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        await LoadLesson();
        await LoadQuestions();
    }

    private async Task LoadLesson()
    {
        try
        {
            lesson = await Http.GetFromJsonAsync<Lesson>($"api/lessons/{LessonId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading lesson: {ex.Message}");
        }
    }

    private async Task LoadQuestions()
    {
        try
        {
            questions = await Http.GetFromJsonAsync<List<LessonQuestion>>($"api/lessons/{LessonId}/questions") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading questions: {ex.Message}");
        }
    }

    private void SelectOption(long questionId, long optionId)
    {
        if (hasAnswered) return;
        
        if (!selectedAnswers.ContainsKey(questionId))
        {
            selectedAnswers[questionId] = new List<long>();
        }
        
        var currentSelections = selectedAnswers[questionId];
        
        // For now, assume single selection (can be extended for multiple choice)
        currentSelections.Clear();
        currentSelections.Add(optionId);
    }

    private bool CanCheckAnswer(long questionId)
    {
        return selectedAnswers.ContainsKey(questionId) && selectedAnswers[questionId].Any();
    }

    private void CheckAnswer()
    {
        hasAnswered = true;
        StateHasChanged();
    }

    private void NextQuestion()
    {
        if (currentQuestionIndex < questions.Count - 1)
        {
            currentQuestionIndex++;
            hasAnswered = false;
        }
        else
        {
            CompleteLesson();
        }
    }

    private async Task CompleteLesson()
    {
        // Calculate score
        correctAnswers = 0;
        foreach (var question in questions)
        {
            if (IsAnswerCorrect(question))
            {
                correctAnswers++;
            }
        }

        // Submit answers to server
        try
        {
            var submitRequest = new
            {
                UserId = currentUserId,
                Answers = selectedAnswers
            };

            var response = await Http.PostAsJsonAsync($"api/lessons/{LessonId}/submit", submitRequest);
            if (response.IsSuccessStatusCode)
            {
                // Load any earned badges
                await LoadEarnedBadges();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error submitting lesson {LessonId} for user {UserId}", LessonId, currentUserId);
        }

        isCompleted = true;
    }

    private async Task LoadEarnedBadges()
    {
        try
        {
            earnedBadges = await Http.GetFromJsonAsync<List<Badge>>($"api/users/{currentUserId}/badges") ?? new();
            // In a real app, you'd only show newly earned badges
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading badges for user {UserId}", currentUserId);
        }
    }

    private bool IsAnswerCorrect(LessonQuestion question)
    {
        if (!selectedAnswers.ContainsKey(question.Id)) return false;
        
        var userAnswers = selectedAnswers[question.Id];
        var correctOptions = question.Options.Where(o => o.IsCorrect).Select(o => o.Id).ToList();
        
        return userAnswers.Count == correctOptions.Count && 
               userAnswers.All(ua => correctOptions.Contains(ua)) &&
               correctOptions.All(co => userAnswers.Contains(co));
    }

    private string GetCorrectAnswerText(LessonQuestion question)
    {
        var correctOption = question.Options.FirstOrDefault(o => o.IsCorrect);
        return correctOption?.OptionText ?? "Unknown";
    }

    private string GetOptionClass(long optionId)
    {
        var question = questions[currentQuestionIndex];
        if (!hasAnswered || !selectedAnswers.ContainsKey(question.Id)) 
        {
            var isSelected = selectedAnswers.ContainsKey(question.Id) && selectedAnswers[question.Id].Contains(optionId);
            return isSelected ? "selected" : "";
        }
        
        var option = question.Options.FirstOrDefault(o => o.Id == optionId);
        var isUserSelection = selectedAnswers[question.Id].Contains(optionId);
        
        if (option?.IsCorrect == true)
        {
            return "correct";
        }
        else if (isUserSelection)
        {
            return "incorrect";
        }
        
        return "";
    }

    private double GetProgressPercentage()
    {
        if (questions.Count == 0) return 0;
        return ((double)(currentQuestionIndex + 1) / questions.Count) * 100;
    }

    private void PlayAudio(string audioUrl)
    {
        // In a real app, you'd implement audio playback
        Logger.LogDebug("Playing audio: {AudioUrl}", audioUrl);
    }

    private void GoBack()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }

    private void ContinueLearning()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }

    private void GoToCourse()
    {
        Navigation.NavigateTo($"/courses/{lesson?.CourseId}");
    }
}
