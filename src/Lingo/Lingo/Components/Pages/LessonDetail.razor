@page "/lessons/{LessonId:long}"
@using Lingo.Core.Models

<PageTitle>@(lesson?.Title ?? "Lesson") - Lingo</PageTitle>

@if (lesson == null || questions == null)
{
    <div class="loading">Loading lesson...</div>
}
else
{
    <div class="lesson-container">
        <div class="lesson-header">
            <button class="back-button" @onclick="GoBack">← Back</button>
            <div class="lesson-info">
                <h1>@lesson.Title</h1>
                <p>@lesson.Description</p>
            </div>
            <div class="lesson-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: @(GetProgressPercentage())%"></div>
                </div>
                <span class="progress-text">@(currentQuestionIndex + 1) / @questions.Count</span>
            </div>
        </div>

        @if (!isCompleted)
        {
            <div class="question-container">
                @if (currentQuestionIndex < questions.Count)
                {
                    var question = questions[currentQuestionIndex];
                    
                    <div class="question-card">
                        <div class="question-header">
                            <h2>@question.QuestionText</h2>
                            @if (!string.IsNullOrEmpty(question.AudioUrl))
                            {
                                <button class="audio-button" @onclick="() => PlayAudio(question.AudioUrl)">🔊</button>
                            }
                        </div>
                        
                        @if (!string.IsNullOrEmpty(question.ImageUrl))
                        {
                            <div class="question-image">
                                <img src="@question.ImageUrl" alt="Question image" />
                            </div>
                        }
                        
                        <div class="options-container">
                            @foreach (var option in question.Options.OrderBy(o => o.OrderIndex))
                            {
                                <button class="option-button @GetOptionClass(option.Id)" 
                                        @onclick="() => SelectOption(question.Id, option.Id)"
                                        disabled="@(hasAnswered && selectedAnswers.ContainsKey(question.Id))">
                                    @option.OptionText
                                    @if (!string.IsNullOrEmpty(option.AudioUrl))
                                    {
                                        <span class="option-audio" @onclick:stopPropagation="true" @onclick="() => PlayAudio(option.AudioUrl)">🔊</span>
                                    }
                                </button>
                            }
                        </div>
                        
                        @if (hasAnswered && selectedAnswers.ContainsKey(question.Id))
                        {
                            <div class="answer-feedback @(IsAnswerCorrect(question) ? "correct" : "incorrect")">
                                @if (IsAnswerCorrect(question))
                                {
                                    <div class="feedback-content">
                                        <span class="feedback-icon">✓</span>
                                        <span class="feedback-text">Correct!</span>
                                    </div>
                                }
                                else
                                {
                                    <div class="feedback-content">
                                        <span class="feedback-icon">✗</span>
                                        <span class="feedback-text">Incorrect. The correct answer is: @GetCorrectAnswerText(question)</span>
                                    </div>
                                }
                            </div>
                        }
                        
                        <div class="question-actions">
                            @if (!hasAnswered || !selectedAnswers.ContainsKey(question.Id))
                            {
                                <button class="btn btn-primary" @onclick="CheckAnswer" disabled="@(!CanCheckAnswer(question.Id))">
                                    Check Answer
                                </button>
                            }
                            else
                            {
                                <button class="btn btn-primary" @onclick="NextQuestion">
                                    @(currentQuestionIndex < questions.Count - 1 ? "Next Question" : "Complete Lesson")
                                </button>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="completion-screen">
                <div class="completion-content">
                    <div class="completion-icon">🎉</div>
                    <h2>Lesson Complete!</h2>
                    <div class="score-display">
                        <span class="score">@correctAnswers / @questions.Count</span>
                        <span class="score-label">Correct Answers</span>
                    </div>
                    <div class="xp-earned">
                        <span class="xp-amount">+@lesson.ExperiencePoints XP</span>
                    </div>
                    
                    @if (earnedBadges.Any())
                    {
                        <div class="badges-earned">
                            <h3>Badges Earned!</h3>
                            <div class="badges-list">
                                @foreach (var badge in earnedBadges)
                                {
                                    <div class="badge-item">
                                        <img src="@badge.IconUrl" alt="@badge.Name" />
                                        <span>@badge.Name</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    
                    <div class="completion-actions">
                        <button class="btn btn-primary" @onclick="ContinueLearning">Continue Learning</button>
                        <button class="btn btn-secondary" @onclick="GoToCourse">Back to Course</button>
                    </div>
                </div>
            </div>
        }
    </div>
}


