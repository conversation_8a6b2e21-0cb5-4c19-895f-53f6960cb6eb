@page "/courses/{CourseId:long}"
@using Lingo.Core.Models

<PageTitle>@(course?.Name ?? "Course") - Lingo</PageTitle>

@if (course == null)
{
    <div class="loading">Loading course...</div>
}
else
{
    <div class="course-header">
        <div class="course-info">
            <img src="@course.ImageUrl" alt="@course.Language" class="course-flag" />
            <div class="course-details">
                <h1>@course.Name</h1>
                <p>@course.Description</p>
                <div class="course-meta">
                    <span class="difficulty @GetDifficultyClass()">@course.Difficulty</span>
                    <span class="lesson-count">@course.Lessons.Count lessons</span>
                </div>
            </div>
        </div>
        
        @if (progress != null)
        {
            <div class="progress-section">
                <div class="progress-circle">
                    <div class="progress-text">
                        <span class="percentage">@((int)progress.CompletionPercentage)%</span>
                        <span class="label">Complete</span>
                    </div>
                </div>
                <div class="progress-stats">
                    <div class="stat">
                        <span class="stat-value">@progress.CompletedLessons</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@(progress.TotalLessons - progress.CompletedLessons)</span>
                        <span class="stat-label">Remaining</span>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="lessons-section">
        <h2>Lessons</h2>
        
        @if (userLessonProgress == null)
        {
            <div class="loading">Loading progress...</div>
        }
        else
        {
            <div class="lessons-list">
                @for (int i = 0; i < course.Lessons.Count; i++)
                {
                    var lesson = course.Lessons[i];
                    var lessonProgress = GetLessonProgress(lesson.Id);
                    var isLocked = IsLessonLocked(i, lessonProgress);
                    
                    <Lingo.Client.Components.LessonCard 
                        Lesson="lesson" 
                        Progress="lessonProgress" 
                        IsLocked="isLocked" />
                }
            </div>
        }
    </div>
}



