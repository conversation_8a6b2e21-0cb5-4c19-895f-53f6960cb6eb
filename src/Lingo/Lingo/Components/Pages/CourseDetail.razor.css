.course-header {
    background: linear-gradient(135deg, #1cb0f6, #58cc02);
    color: white;
    padding: 40px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.course-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.course-flag {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 3px solid white;
}

.course-details h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 700;
}

.course-details p {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    opacity: 0.9;
}

.course-meta {
    display: flex;
    gap: 20px;
    align-items: center;
}

.difficulty {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
    text-transform: uppercase;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
}

.lesson-count {
    font-size: 1em;
    opacity: 0.9;
}

.progress-section {
    display: flex;
    align-items: center;
    gap: 30px;
}

.progress-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255,255,255,0.3);
}

.progress-text {
    text-align: center;
}

.percentage {
    display: block;
    font-size: 1.5em;
    font-weight: 700;
}

.label {
    font-size: 0.8em;
    opacity: 0.8;
}

.progress-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8em;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.8;
}

.lessons-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.lessons-section h2 {
    color: #3c3c3c;
    margin-bottom: 30px;
    font-size: 2em;
    font-weight: 700;
}

.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2em;
    color: #777;
}

@media (max-width: 768px) {
    .course-header {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }
    
    .course-info {
        flex-direction: column;
        text-align: center;
    }
    
    .progress-section {
        justify-content: center;
    }
}
