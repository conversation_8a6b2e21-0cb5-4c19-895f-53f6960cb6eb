@page "/profile"
@using Lingo.Core.Models

<PageTitle>Profile - Lingo</PageTitle>

<div class="profile-container">
    @if (user == null)
    {
        <div class="loading">Loading profile...</div>
    }
    else
    {
        <div class="profile-header">
            <div class="profile-avatar">
                @if (!string.IsNullOrEmpty(user.ProfileImageUrl))
                {
                    <img src="@user.ProfileImageUrl" alt="@user.DisplayName" />
                }
                else
                {
                    <div class="avatar-placeholder">@GetInitials(user.DisplayName)</div>
                }
            </div>
            <div class="profile-info">
                <h1>@user.DisplayName</h1>
                <p class="email">@user.Email</p>
                <div class="profile-stats">
                    <div class="stat">
                        <span class="stat-value">@user.ExperiencePoints</span>
                        <span class="stat-label">Total XP</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@user.CurrentStreak</span>
                        <span class="stat-label">Day Streak</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">@user.LongestStreak</span>
                        <span class="stat-label">Best Streak</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="profile-content">
            <div class="section">
                <h2>Badges (@user.Badges.Count)</h2>
                @if (user.Badges.Any())
                {
                    <div class="badges-grid">
                        @foreach (var badge in user.Badges.OrderByDescending(b => b.EarnedAt))
                        {
                            <div class="badge-card">
                                <img src="@badge.IconUrl" alt="@badge.Name" />
                                <h3>@badge.Name</h3>
                                <p>@badge.Description</p>
                                <span class="earned-date">Earned @badge.EarnedAt.ToString("MMM dd, yyyy")</span>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <p>No badges earned yet. Complete lessons to earn your first badge!</p>
                    </div>
                }
            </div>

            <div class="section">
                <h2>Course Progress</h2>
                @if (courseProgress.Any())
                {
                    <div class="progress-list">
                        @foreach (var progress in courseProgress.OrderByDescending(p => p.LastAccessedAt))
                        {
                            <div class="progress-card" @onclick="() => GoToCourse(progress.CourseId)">
                                <div class="progress-info">
                                    <h3>@GetCourseName(progress.CourseId)</h3>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: @(progress.CompletionPercentage)%"></div>
                                    </div>
                                    <div class="progress-details">
                                        <span>@progress.CompletedLessons / @progress.TotalLessons lessons</span>
                                        <span class="completion-percent">@((int)progress.CompletionPercentage)%</span>
                                    </div>
                                    <p class="last-accessed">Last accessed @progress.LastAccessedAt.ToString("MMM dd, yyyy")</p>
                                </div>
                                @if (progress.IsCompleted)
                                {
                                    <div class="completion-badge">✓ Completed</div>
                                }
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="empty-state">
                        <p>No courses started yet. <a href="/">Browse available courses</a> to get started!</p>
                    </div>
                }
            </div>

            <div class="section">
                <h2>Learning Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <span class="stat-number">@GetTotalLessonsCompleted()</span>
                            <span class="stat-description">Lessons Completed</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏆</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.Badges.Count</span>
                            <span class="stat-description">Badges Earned</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.ExperiencePoints</span>
                            <span class="stat-description">Experience Points</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <span class="stat-number">@user.CurrentStreak</span>
                            <span class="stat-description">Current Streak</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>




