.lesson-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.lesson-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.back-button {
    background: none;
    border: none;
    font-size: 1.1em;
    color: #1cb0f6;
    cursor: pointer;
    padding: 8px;
}

.lesson-info h1 {
    margin: 0 0 8px 0;
    color: #3c3c3c;
    font-size: 1.5em;
}

.lesson-info p {
    margin: 0;
    color: #777;
}

.lesson-progress {
    text-align: right;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #58cc02, #89e219);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9em;
    color: #777;
}

.question-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.question-header h2 {
    margin: 0;
    color: #3c3c3c;
    font-size: 1.4em;
    flex: 1;
}

.audio-button {
    background: #1cb0f6;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    cursor: pointer;
    font-size: 1.2em;
}

.question-image {
    text-align: center;
    margin-bottom: 20px;
}

.question-image img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.option-button {
    padding: 16px 20px;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    font-size: 1em;
    text-align: left;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-button:hover:not(:disabled) {
    border-color: #1cb0f6;
    background: #f8fcff;
}

.option-button.selected {
    border-color: #1cb0f6;
    background: #e8f4fd;
}

.option-button.correct {
    border-color: #58cc02;
    background: #f0f9e8;
    color: #58cc02;
}

.option-button.incorrect {
    border-color: #ff4b4b;
    background: #fff0f0;
    color: #ff4b4b;
}

.option-button:disabled {
    cursor: not-allowed;
}

.option-audio {
    background: #1cb0f6;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    color: white;
    cursor: pointer;
    font-size: 0.8em;
    display: flex;
    align-items: center;
    justify-content: center;
}

.answer-feedback {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.answer-feedback.correct {
    background: #f0f9e8;
    border: 1px solid #58cc02;
}

.answer-feedback.incorrect {
    background: #fff0f0;
    border: 1px solid #ff4b4b;
}

.feedback-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feedback-icon {
    font-size: 1.5em;
    font-weight: bold;
}

.answer-feedback.correct .feedback-icon {
    color: #58cc02;
}

.answer-feedback.incorrect .feedback-icon {
    color: #ff4b4b;
}

.feedback-text {
    font-weight: 600;
}

.question-actions {
    text-align: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #58cc02;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #4caf00;
    transform: translateY(-2px);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #e5e5e5;
    color: #3c3c3c;
    margin-left: 12px;
}

.btn-secondary:hover {
    background: #d0d0d0;
}

.completion-screen {
    text-align: center;
    padding: 40px;
}

.completion-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.completion-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.completion-content h2 {
    color: #3c3c3c;
    margin-bottom: 30px;
    font-size: 2em;
}

.score-display {
    margin-bottom: 20px;
}

.score {
    display: block;
    font-size: 3em;
    font-weight: 700;
    color: #58cc02;
}

.score-label {
    color: #777;
    font-size: 1.1em;
}

.xp-earned {
    margin-bottom: 30px;
}

.xp-amount {
    background: #ffc107;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1.2em;
    font-weight: 600;
}

.badges-earned {
    margin-bottom: 30px;
}

.badges-earned h3 {
    color: #3c3c3c;
    margin-bottom: 20px;
}

.badges-list {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.badge-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.badge-item img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.badge-item span {
    font-size: 0.9em;
    color: #777;
}

.completion-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2em;
    color: #777;
}

@media (max-width: 768px) {
    .lesson-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .progress-bar {
        width: 100%;
    }
    
    .completion-actions {
        flex-direction: column;
    }
}
