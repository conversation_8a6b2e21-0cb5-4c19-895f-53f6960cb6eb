using Lingo.Core.Models;
using Microsoft.AspNetCore.Components;

namespace Lingo.Components.Pages;

public partial class Profile
{
    [Inject] private HttpClient Http { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<Profile> Logger { get; set; } = default!;
    
    private User? user;
    private List<UserCourseProgress> courseProgress = new();
    private List<Course> allCourses = new();
    private long currentUserId = 1; // Demo user ID

    protected override async Task OnInitializedAsync()
    {
        await LoadUser();
        await LoadCourseProgress();
        await LoadCourses();
    }

    private async Task LoadUser()
    {
        try
        {
            user = await Http.GetFromJsonAsync<User>($"api/users/{currentUserId}");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading user {UserId}", currentUserId);
            // Create demo user if not found
            user = new User
            {
                Id = currentUserId,
                DisplayName = "Demo User",
                Email = "<EMAIL>",
                ExperiencePoints = 150,
                CurrentStreak = 3,
                LongestStreak = 7,
                Badges = new List<Badge>(),
                CourseProgress = new List<UserCourseProgress>()
            };
        }
    }

    private async Task LoadCourseProgress()
    {
        try
        {
            courseProgress = await Http.GetFromJsonAsync<List<UserCourseProgress>>($"api/users/{currentUserId}/progress") ?? new();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading course progress for user {UserId}", currentUserId);
        }
    }

    private async Task LoadCourses()
    {
        try
        {
            allCourses = await Http.GetFromJsonAsync<List<Course>>("api/courses") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading courses: {ex.Message}");
        }
    }

    private string GetInitials(string displayName)
    {
        if (string.IsNullOrEmpty(displayName)) return "U";
        
        var parts = displayName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return displayName[0].ToString().ToUpper();
    }

    private string GetCourseName(long courseId)
    {
        var course = allCourses.FirstOrDefault(c => c.Id == courseId);
        return course?.Name ?? "Unknown Course";
    }

    private int GetTotalLessonsCompleted()
    {
        return courseProgress.Sum(p => p.CompletedLessons);
    }

    private void GoToCourse(long courseId)
    {
        Navigation.NavigateTo($"/courses/{courseId}");
    }
}
