.profile-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.profile-header {
    background: linear-gradient(135deg, #1cb0f6, #58cc02);
    color: white;
    padding: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
}

.profile-avatar {
    flex-shrink: 0;
}

.profile-avatar img,
.avatar-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid white;
}

.avatar-placeholder {
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    font-weight: bold;
}

.profile-info h1 {
    margin: 0 0 8px 0;
    font-size: 2.5em;
    font-weight: 700;
}

.email {
    margin: 0 0 20px 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.profile-stats {
    display: flex;
    gap: 30px;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2em;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.8;
}

.profile-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.section h2 {
    color: #3c3c3c;
    margin-bottom: 20px;
    font-size: 1.8em;
    font-weight: 700;
}

.badges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.badge-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.badge-card img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 15px;
}

.badge-card h3 {
    margin: 0 0 8px 0;
    color: #3c3c3c;
    font-size: 1.1em;
}

.badge-card p {
    margin: 0 0 12px 0;
    color: #777;
    font-size: 0.9em;
    line-height: 1.4;
}

.earned-date {
    font-size: 0.8em;
    color: #999;
}

.progress-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.progress-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-card:hover {
    border-color: #58cc02;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.progress-info {
    flex: 1;
}

.progress-info h3 {
    margin: 0 0 12px 0;
    color: #3c3c3c;
    font-size: 1.2em;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #58cc02, #89e219);
    transition: width 0.3s ease;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9em;
    color: #777;
}

.completion-percent {
    font-weight: 600;
    color: #58cc02;
}

.last-accessed {
    margin: 0;
    font-size: 0.8em;
    color: #999;
}

.completion-badge {
    background: #58cc02;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-icon {
    font-size: 2.5em;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8em;
    font-weight: 700;
    color: #3c3c3c;
}

.stat-description {
    font-size: 0.9em;
    color: #777;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #777;
}

.empty-state a {
    color: #1cb0f6;
    text-decoration: none;
}

.empty-state a:hover {
    text-decoration: underline;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2em;
    color: #777;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-stats {
        justify-content: center;
    }
    
    .badges-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-card {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
