.hero-section {
    background: linear-gradient(135deg, #58cc02, #89e219);
    color: white;
    padding: 80px 20px;
    text-align: center;
    margin-bottom: 40px;
}

.hero-content h1 {
    font-size: 3em;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.3em;
    margin-bottom: 40px;
    opacity: 0.9;
}

.auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 300px;
    margin: 0 auto 40px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.btn-google {
    background: white;
    color: #333;
    border: 1px solid #ddd;
}

.btn-google:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.btn-facebook {
    background: #1877f2;
    color: white;
}

.btn-facebook:hover {
    background: #166fe5;
    transform: translateY(-2px);
}

.btn-demo {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid white;
}

.btn-demo:hover {
    background: white;
    color: #58cc02;
}

.demo-section p {
    margin-bottom: 20px;
    font-size: 1.1em;
}

.courses-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.courses-section h2 {
    text-align: center;
    color: #3c3c3c;
    margin-bottom: 40px;
    font-size: 2.5em;
    font-weight: 700;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2em;
    color: #777;
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2em;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
}
