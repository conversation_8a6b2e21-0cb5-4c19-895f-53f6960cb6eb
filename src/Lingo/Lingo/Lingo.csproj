<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>lingo.web</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Aspire.Npgsql" Version="9.3.1"/>
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.3.1"/>
        <PackageReference Include="Marten.AspNetCore" Version="8.3.0"/>
        <PackageReference Include="Microsoft.Orleans.Persistence.Memory" Version="9.1.2"/>
        <PackageReference Include="Microsoft.Orleans.Reminders.AdoNet" Version="9.1.2"/>
        <PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.8.3">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.12.0-beta.1"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0"/>
        <PackageReference Include="Scalar.AspNetCore" Version="2.4.17"/>
        <PackageReference Include="Scalar.AspNetCore.Microsoft" Version="2.4.17"/>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0"/>
        <PackageReference Include="Weasel.Core" Version="8.1.1"/>
        <PackageReference Include="Weasel.Postgresql" Version="8.1.1"/>
        <PackageReference Include="WolverineFx" Version="4.3.0"/>
        <PackageReference Include="WolverineFx.EntityFrameworkCore" Version="4.3.0"/>
        <PackageReference Include="WolverineFx.Http" Version="4.3.0"/>
        <PackageReference Include="WolverineFx.Marten" Version="4.3.0"/>
        <PackageReference Include="WolverineFx.Postgresql" Version="4.3.0"/>
        <PackageReference Include="WolverineFx.RDBMS" Version="4.3.0"/>
        <PackageReference Include="ZiggyCreatures.FusionCache" Version="2.3.0"/>
        <PackageReference Include="ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis" Version="2.3.0"/>
        <PackageReference Include="ZiggyCreatures.FusionCache.Serialization.SystemTextJson" Version="2.3.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6"/>
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6"/>
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6"/>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4"/>
        <PackageReference Include="Microsoft.Orleans.Server" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Orleans.Clustering.AdoNet" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Orleans.Persistence.AdoNet" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6"/>
        <ProjectReference Include="..\..\Lingo.ServiceDefaults\Lingo.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\..\Lingo.Core\Lingo.Core.csproj"/>
        <ProjectReference Include="..\..\Lingo.Infrastructure\Lingo.Infrastructure.csproj"/>
        <ProjectReference Include="..\Lingo.Client\Lingo.Client.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="wwwroot/lib/lodash.js/**"/>
        <Content Remove="wwwroot/lib/bootstrap/**"/>
        <Content Remove="wwwroot/lib/shoelace-css/**"/>
        <Content Remove="wwwroot/lib/tailwindcss/**"/>
        <Content Remove="wwwroot/lib/alpinejs/**"/>
    </ItemGroup>
</Project>
