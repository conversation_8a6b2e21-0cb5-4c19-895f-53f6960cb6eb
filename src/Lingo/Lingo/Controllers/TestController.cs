using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace Lingo.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ILogger<TestController> _logger;

    public TestController(ILogger<TestController> logger)
    {
        _logger = logger;
    }

    [HttpGet("logging")]
    public IActionResult TestLogging()
    {
        _logger.LogDebug("Debug message from TestController");
        _logger.LogInformation("Information message from TestController");
        _logger.LogWarning("Warning message from TestController");
        
        try
        {
            // Simulate some work
            var random = new Random();
            var value = random.Next(1, 100);
            
            _logger.LogInformation("Generated random value: {Value}", value);
            
            if (value > 50)
            {
                throw new InvalidOperationException("Simulated error for testing");
            }
            
            return Ok(new { message = "Logging test completed successfully", value });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during logging test");
            return StatusCode(500, new { message = "Error occurred during logging test", error = ex.Message });
        }
    }

    [HttpGet("telemetry")]
    public async Task<IActionResult> TestTelemetry()
    {
        using var activity = new Activity("TestTelemetry").Start();
        activity?.SetTag("test.type", "telemetry");
        activity?.SetTag("test.timestamp", DateTimeOffset.UtcNow.ToString());

        _logger.LogInformation("Starting telemetry test");

        // Simulate some async work
        await Task.Delay(100);

        _logger.LogInformation("Telemetry test completed");

        return Ok(new { message = "Telemetry test completed", timestamp = DateTimeOffset.UtcNow });
    }
}
