{"CloudFlare": {"Email": "", "ApiKey": "", "ApiToken": ""}, "UpdateIntervalSeconds": 30, "Serilog": {"Using": ["Serilog.Sinks.Async", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Verbose", "Override": {"Microsoft": "Warning", "System": "Error"}}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss+fff}{EventType:x8} {Level:u3}][{Application}] {Message:lj} [{SourceContext}]{NewLine}{Exception}", "restrictedToMinimumLevel": "Debug"}}]}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "CloudFlareDnsUpdater"}}}