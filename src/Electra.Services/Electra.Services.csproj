<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Commone services for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Services</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Caching\Electra.Caching.csproj" />
    <ProjectReference Include="..\Electra.Actors\Electra.Actors.csproj" />
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
    <ProjectReference Include="..\Electra.Models\Electra.Models.csproj" />
    <ProjectReference Include="..\Electra.Persistence.Marten\Electra.Persistence.Marten.csproj" />
    <ProjectReference Include="..\Electra.Persistence\Electra.Persistence.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Confluent.Kafka" Version="2.10.0" />
    <PackageReference Include="Foundatio.AzureStorage" Version="11.1.0" />
    <PackageReference Include="Foundatio.RabbitMQ" Version="11.1.0" />
    <PackageReference Include="PasswordGenerator" Version="2.1.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Twilio" Version="7.11.1" />
    <PackageReference Include="FluentEmail.Core" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Handlebars" Version="0.1.3" />
    <PackageReference Include="FluentEmail.Mailgun" Version="3.0.2" />
    <PackageReference Include="FluentEmail.MailKit" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Razor" Version="3.0.2" />
    <PackageReference Include="FluentEmail.SendGrid" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
    <PackageReference Include="Sendgrid" Version="9.29.3" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.5" />
    <PackageReference Include="microsoft.extensions.configuration" Version="9.0.5" />
    <PackageReference Include="NewId" Version="4.0.1" />
  </ItemGroup>
</Project>