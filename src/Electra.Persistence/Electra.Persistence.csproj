<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Data access patterns for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Persistence</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="MultipleResultSetHelper.cs" />
    <Compile Remove="MultipleResultSetWrapper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="MultipleResultSetHelper.cs" />
    <None Include="MultipleResultSetWrapper.cs" />
    <Compile Remove="ElectraIdentityServerContext.cs" />
    <None Include="ElectraIdentityServerContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elasticsearch.Net" Version="7.17.5" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="Foundatio" Version="11.1.0" />
    <PackageReference Include="Foundatio.Repositories" Version="7.17.15" />
    <PackageReference Include="Foundatio.Repositories.Elasticsearch" Version="7.17.15" />
    <PackageReference Include="LiteDB" Version="5.0.21" />
    <PackageReference Include="Marten" Version="7.40.5" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.Design" Version="1.1.0" />
    <PackageReference Include="Npgsql.Json.NET" Version="9.0.3" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Formatting.Elasticsearch" Version="10.0.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
    <PackageReference Include="ServiceStack.Aws" Version="8.8.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
    <PackageReference Include="System.Data.Async" Version="2.0.0" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="YeSql.Net" Version="2.0.0" />
    <PackageReference Include="Z.EntityFramework.Extensions.EFCore" Version="9.103.8.1" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="9.103.8.1" />
    <PackageReference Include="Z.Expressions.Eval" Version="6.2.11" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.VisualBasic.Workspaces" Version="4.14.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
    <ProjectReference Include="..\Electra.Models\Electra.Models.csproj" />
    <ProjectReference Include="..\Electra.Common\Electra.Common.csproj" />
  </ItemGroup>
</Project>