<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Description>Electra common utils</Description>
    <IsPackable>false</IsPackable>
    <Title>Electra.Common.Web</Title>
    <RootNamespace>Electra.Common.Web</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Extensions\IdentityServerExtensions.cs" />
    <Compile Remove="Identity\IdentityServerSettings.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Extensions\IdentityServerExtensions.cs" />
    <None Include="Identity\IdentityServerSettings.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Boxed.AspNetCore" Version="9.0.1" />
    <PackageReference Include="Foundatio.AzureStorage" Version="11.1.0" />
    <PackageReference Include="Honeycomb" Version="1.4.0" />
    <PackageReference Include="Honeycomb.AspNetCore" Version="1.4.0" />
    <PackageReference Include="Honeycomb.OpenTelemetry" Version="1.5.0" />
    <PackageReference Include="Honeycomb.Serilog.Sink" Version="2.2.0" />
    <PackageReference Include="IPAddressRange" Version="6.2.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.Async" Version="2.0.1" />
    <PackageReference Include="Mapster.Core" Version="1.2.1" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Mapster.EFCore" Version="5.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite.Core" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.5" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.6.24" />
    <PackageReference Include="MiniProfiler.AspNetCore" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.Elasticsearch" Version="7.1.0" />
    <PackageReference Include="MiniProfiler.EntityFrameworkCore" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.Providers.PostgreSql" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.Providers.Redis" Version="4.5.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="OwaspHeaders.Core" Version="9.7.2" />
    <PackageReference Include="Scalar.AspNetCore" Version="2.4.4" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.1.1" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Context" Version="4.6.5" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Memory" Version="1.0.4" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.Trace" Version="4.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Caching\Electra.Caching.csproj" />
    <ProjectReference Include="..\Electra.Core\Electra.Core.csproj" />
    <ProjectReference Include="..\Electra.Persistence\Electra.Persistence.csproj" />
    <ProjectReference Include="..\Electra.Models\Electra.Models.csproj" />
    <ProjectReference Include="..\Electra.Services\Electra.Services.csproj" />
    <ProjectReference Include="..\Electra.SignalR\Electra.SignalR.csproj" />
    <ProjectReference Include="..\Electra.Validators\Electra.Validators.csproj" />
  </ItemGroup>
</Project>