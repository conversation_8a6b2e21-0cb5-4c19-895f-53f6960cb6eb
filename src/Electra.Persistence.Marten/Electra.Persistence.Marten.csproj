<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title><PERSON>en Persistence for the Electra Web Platform</Title>
    <Description>Core components for the Electra web application platform</Description>
    <IsPackable>false</IsPackable>
    <RootNamespace>Electra.Persistence.Marten</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="marten" Version="7.40.5" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.VisualBasic.Workspaces" Version="4.14.0" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="Npgsql.Json.NET" Version="9.0.3" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="ThrowGuard" Version="1.0.7" />
    <PackageReference Include="Z.ExtensionMethods" Version="2.1.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Electra.Caching\Electra.Caching.csproj" />
  </ItemGroup>
</Project>